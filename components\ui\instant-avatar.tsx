'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface InstantAvatarProps {
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackClassName?: string;
  fallbackText?: string;
}

const sizeMap = {
  sm: { size: 32, className: 'h-8 w-8' },
  md: { size: 40, className: 'h-10 w-10' },
  lg: { size: 48, className: 'h-12 w-12' },
  xl: { size: 80, className: 'h-20 w-20' }
};

/**
 * Avatar instantâneo para LCP ZERO
 * 🎯 ESTRATÉGIA: APENAS FALLBACK - sem carregamento de imagem
 * ✅ Renderização instantânea com CSS puro
 * 🚀 ZERO impacto no LCP - apenas texto e gradiente
 * 💡 Perfeito para elementos críticos above-the-fold
 */
export function InstantAvatar({
  alt,
  size = 'md',
  className,
  fallbackClassName,
  fallbackText
}: InstantAvatarProps) {
  const sizeConfig = sizeMap[size];
  
  // Gerar iniciais para fallback
  const getInitials = (text: string) => {
    if (!text) return '??';
    return text
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(fallbackText || alt);

  return (
    <div 
      className={cn(
        'relative flex shrink-0 overflow-hidden rounded-full bg-gradient-to-br from-blue-500 to-purple-600',
        sizeConfig.className,
        className
      )}
    >
      {/* 🎯 APENAS FALLBACK - Renderização instantânea */}
      <div 
        className={cn(
          'flex items-center justify-center text-white font-semibold w-full h-full',
          size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : size === 'lg' ? 'text-base' : 'text-lg',
          fallbackClassName
        )}
      >
        {initials}
      </div>
    </div>
  );
}

/**
 * Avatar instantâneo especificamente para influenciadores
 * ✅ SEM CARREGAMENTO DE IMAGEM - apenas iniciais
 * 🎯 ZERO IMPACTO NO LCP - renderização instantânea
 */
export function InstantInfluencerAvatar({
  influencerName,
  className,
  fallbackClassName,
  size = 'xl'
}: {
  influencerName: string;
  className?: string;
  fallbackClassName?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  return (
    <InstantAvatar
      alt={influencerName}
      size={size}
      className={className}
      fallbackClassName={fallbackClassName}
      fallbackText={influencerName}
    />
  );
}
