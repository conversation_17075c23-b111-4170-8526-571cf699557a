
"use client";

import React, { createContext, useContext, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useUser } from '@clerk/nextjs';

interface LoaderProps {
  isLoading?: boolean;
  message?: string;
  showLogo?: boolean;
  className?: string;
}

interface GlobalLoaderContextType {
  isLoading: boolean;
  message: string;
  showLoader: (loadingMessage?: string) => void;
  hideLoader: () => void;
  updateMessage: (newMessage: string) => void;
}

// Contexto global para o loader
const GlobalLoaderContext = createContext<GlobalLoaderContextType | undefined>(undefined);

// Provider do contexto
export function GlobalLoaderProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = React.useState(false);
  const [message, setMessage] = React.useState("");
  
  // Hook para monitorar estado do usuário e resetar loader quando necessário
  const { user, isLoaded } = useUser();
  
  // Ref para controlar timeout de segurança
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  
  // Resetar loader automaticamente quando usuário faz logout
  useEffect(() => {
    if (isLoaded && !user && isLoading) {
      setIsLoading(false);
      setMessage("");
      
      // Limpar timeout se existir
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
  }, [user, isLoaded, isLoading]);

  const showLoader = React.useCallback((loadingMessage?: string) => {
    setMessage(loadingMessage || "");
    setIsLoading(true);
    
    // Definir timeout de segurança para esconder loader automaticamente após 10 segundos
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsLoading(false);
      setMessage("");
      timeoutRef.current = null;
    }, 10000); // 10 segundos
  }, []);

  const hideLoader = React.useCallback(() => {
    setIsLoading(false);
    setMessage("");
    
    // Limpar timeout se existir
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Limpar timeout ao desmontar o componente
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const updateMessage = React.useCallback((newMessage: string) => {
    setMessage(newMessage);
  }, []);

  const value = React.useMemo(() => ({
    isLoading,
    message,
    showLoader,
    hideLoader,
    updateMessage
  }), [isLoading, message, showLoader, hideLoader, updateMessage]);

  return (
    <GlobalLoaderContext.Provider value={value}>
      {children}
      {/* Renderizar o loader global automaticamente */}
      <Loader isLoading={isLoading} message={message} showLogo={true} />
    </GlobalLoaderContext.Provider>
  );
}

// Animações otimizadas
const spinnerVariants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1.2,
      repeat: Infinity,
      ease: "linear" as const
    }
  }
};

const containerVariants = {
  initial: { 
    opacity: 0,
    scale: 0.9
  },
  animate: { 
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.3,
      ease: [0.4, 0, 0.2, 1] as const
    }
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    transition: {
      duration: 0.2,
      ease: [0.4, 0, 1, 1] as const
    }
  }
};

const messageVariants = {
  initial: { opacity: 0, y: 10 },
  animate: { 
    opacity: 1, 
    y: 0,
    transition: { 
      delay: 0.2,
      duration: 0.4 
    }
  }
};

// Componente de logo otimizado
const LogoComponent = React.memo(() => (
  <motion.div 
    className="mb-6"
    initial={{ opacity: 0, scale: 0.8 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] as const }}
  >
    <img
      src="/loader-dm.webp"
      alt="Carregando DeuMatch..."
      className="w-16 h-16 object-contain mx-auto"
      style={{
        imageRendering: 'auto',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)',
      }}
      loading="lazy"
      decoding="async"
    />
  </motion.div>
));

LogoComponent.displayName = 'LogoComponent';

// Spinner principal
const SpinnerComponent = React.memo(() => (
  <motion.div
    variants={spinnerVariants}
    animate="animate"
    className="relative"
  >
    <div className="w-16 h-16 border-4 border-gray-200 dark:border-gray-700 border-t-[#ff0074] rounded-full" />
    <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-[#9810fa] rounded-full animate-spin" 
         style={{ animationDirection: 'reverse', animationDuration: '1.5s' }} />
  </motion.div>
));

SpinnerComponent.displayName = 'SpinnerComponent';

// Componente principal de loading centralizado - DESATIVADO
export function Loader({
  isLoading = true,
  message = "",
  showLogo = true,
  className
}: LoaderProps) {

  // 🔥 LOADER DESATIVADO: Retorna null para não renderizar nada
  return null;

  // Código original comentado para possível reativação futura
  /*
  return (
    <AnimatePresence mode="wait">
      {isLoading && (
        <motion.div
          variants={containerVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          className={cn(
            "fixed inset-0 z-[9999] flex items-center justify-center",
            "bg-muted/30 dark:bg-[#08050f] backdrop-blur-2xl",
            "supports-[backdrop-filter]:bg-white/80 supports-[backdrop-filter]:dark:bg-background/80",
            className
          )}
        >
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            {showLogo ? <LogoComponent /> : <SpinnerComponent />}

            {message && message.trim() && (
              <motion.div
                variants={messageVariants}
                initial="initial"
                animate="animate"
                className="space-y-2"
              >
                <p className="text-lg font-medium text-foreground dark:text-foreground">
                  {message}
                </p>
                <div className="flex items-center justify-center space-x-1">
                  <motion.div
                    className="w-2 h-2 bg-[#ff0074] rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: 0
                    }}
                  />
                  <motion.div
                    className="w-2 h-2 bg-[#9810fa] rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: 0.2
                    }}
                  />
                  <motion.div
                    className="w-2 h-2 bg-[#ff0074] rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.7, 1, 0.7]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: 0.4
                    }}
                  />
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
  */
}

// Hook para usar o contexto global de loading
export function useGlobalLoader() {
  const context = useContext(GlobalLoaderContext);
  if (context === undefined) {
    throw new Error('useGlobalLoader deve ser usado dentro de um GlobalLoaderProvider');
  }
  return context;
}

// Componente para loading global da aplicação
export function GlobalLoader() {
  const { isLoading, message } = useGlobalLoader();
  
  return <Loader isLoading={isLoading} message={message} showLogo={true} />;
}

// Export padrão
export default Loader; 


