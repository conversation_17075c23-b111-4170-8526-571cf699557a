"use client";

// 🔓 PÁGINA PÚBLICA: Visualização de influenciadores sem autenticação
// Rota: /public/[userId]/influencers

import { useState, useEffect, useMemo, Suspense } from "react";
import { useSearchPara<PERSON>, useRouter, usePathname } from "next/navigation";
import dynamic from "next/dynamic";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useTranslations } from '@/hooks/use-translations';
import { usePublicInfluencers } from '@/hooks/use-public-influencers';
import { Loader, useGlobalLoader } from "@/components/ui/loader";
import { Toaster } from "sonner";
import {
  Users,
  MapPin,
  Star,
  Eye,
  Globe,
  Share2,
  ExternalLink,
  Filter,
  Search,
  SlidersHorizontal
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

// 🔥 OTIMIZAÇÃO: Lazy loading para componentes pesados
const InfluencerGridComponent = dynamic(() => import('@/components/influencer-grid'), {
  ssr: false,
  loading: () => <InfluencerGridSkeleton />
});

// Skeleton para o grid de influenciadores
function InfluencerGridSkeleton() {
  return (
    <div className="p-6 space-y-4">
      <div className="grid grid-cols-[repeat(auto-fit,minmax(17rem,1fr))] gap-3">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="bg-card rounded-lg p-4 animate-pulse">
            <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-3"></div>
            <div className="h-4 bg-muted rounded mb-2"></div>
            <div className="h-3 bg-muted rounded w-2/3 mx-auto"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Componente para card de influenciador público
function PublicInfluencerCard({
  influencer,
  onClick,
  isSelected
}: {
  influencer: any;
  onClick: () => void;
  isSelected: boolean;
}) {
  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const formatRating = (rating: number) => {
    return rating.toFixed(1);
  };

  return (
    <Card
      className={`cursor-pointer transition-all hover:shadow-md hover:scale-[1.02] ${
        isSelected ? 'ring-2 ring-[#ff0074] border-[#ff0074]' : ''
      }`}
      onClick={onClick}
    >
      <CardContent className="p-4">
        {/* Avatar e informações básicas */}
        <div className="flex flex-col items-center text-center mb-4">
          <div className="relative mb-3">
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-[#ff0074] to-[#5600ce] p-0.5">
              <div className="w-full h-full rounded-full bg-card flex items-center justify-center overflow-hidden">
                {influencer.avatar ? (
                  <img
                    src={influencer.avatar}
                    alt={influencer.name}
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <div className="w-full h-full bg-muted rounded-full flex items-center justify-center text-lg font-semibold text-muted-foreground">
                    {influencer.name?.charAt(0)?.toUpperCase() || '?'}
                  </div>
                )}
              </div>
            </div>
            {influencer.isVerified && (
              <div className="absolute -bottom-1 -right-1 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center">
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </div>

          <h3 className="font-semibold text-foreground mb-1 line-clamp-1">
            {influencer.name}
          </h3>

          {influencer.category && (
            <Badge variant="secondary" className="text-xs mb-2">
              {influencer.category}
            </Badge>
          )}

          {(influencer.city || influencer.state) && (
            <p className="text-xs text-muted-foreground flex items-center gap-1 mb-2">
              <MapPin className="w-3 h-3" />
              {[influencer.city, influencer.state].filter(Boolean).join(', ')}
            </p>
          )}
        </div>

        {/* Métricas */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Seguidores</span>
            <span className="font-medium">{formatFollowers(influencer.totalFollowers)}</span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Engagement</span>
            <span className="font-medium">{(influencer.engagementRate * 100).toFixed(1)}%</span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Avaliação</span>
            <div className="flex items-center gap-1">
              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
              <span className="font-medium">{formatRating(influencer.rating)}</span>
            </div>
          </div>
        </div>

        {/* Redes sociais */}
        <div className="mt-3 pt-3 border-t border-border">
          <div className="flex justify-center gap-2">
            {influencer.instagramUsername && (
              <Badge variant="outline" className="text-xs">
                IG: {formatFollowers(influencer.instagramFollowers || 0)}
              </Badge>
            )}
            {influencer.tiktokUsername && (
              <Badge variant="outline" className="text-xs">
                TT: {formatFollowers(influencer.tiktokFollowers || 0)}
              </Badge>
            )}
            {influencer.youtubeUsername && (
              <Badge variant="outline" className="text-xs">
                YT: {formatFollowers(influencer.youtubeSubscribers || 0)}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface PageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function PublicInfluencersPage({ params }: PageProps) {
  const { t } = useTranslations();
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { isLoading, showLoader, hideLoader } = useGlobalLoader();

  // Estados para parâmetros da URL
  const [userId, setUserId] = useState<string | null>(null);
  const [selectedInfluencer, setSelectedInfluencer] = useState<any>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Estados para filtros (mantidos da página original)
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [minFollowers, setMinFollowers] = useState<number>(0);
  const [maxFollowers, setMaxFollowers] = useState<number>(10000000);
  const [minRating, setMinRating] = useState<number>(0);
  const [verifiedOnly, setVerifiedOnly] = useState<boolean>(false);
  const [availableOnly, setAvailableOnly] = useState<boolean>(true);
  const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [showFilters, setShowFilters] = useState<boolean>(false);

  // Estados para informações do proprietário
  const [ownerInfo, setOwnerInfo] = useState<{
    name?: string;
    totalInfluencers?: number;
  } | null>(null);

  // Resolver parâmetros assíncronos
  useEffect(() => {
    const resolveParams = async () => {
      try {
        const resolvedParams = await params;
        setUserId(resolvedParams.userId);
      } catch (error) {
        console.error('Erro ao resolver parâmetros:', error);
      }
    };

    resolveParams();
  }, [params]);

  // Hook para buscar influenciadores públicos
  const {
    influencers,
    loading: loadingInfluencers,
    error: errorInfluencers,
    totalCount,
    refetch
  } = usePublicInfluencers({
    userId: userId || '',
    filters: {
      category: selectedLocation === 'all' ? undefined : selectedLocation,
      isVerified: verifiedOnly || undefined,
      minFollowers: minFollowers > 0 ? minFollowers : undefined,
      maxFollowers: maxFollowers < 10000000 ? maxFollowers : undefined,
      minRating: minRating > 0 ? minRating : undefined,
      search: searchTerm.trim() || undefined
    },
    pagination: { limit: 50 },
    enabled: !!userId
  });

  // Atualizar informações do proprietário quando os dados carregarem
  useEffect(() => {
    if (influencers.length > 0 && !ownerInfo) {
      setOwnerInfo({
        name: `Lista de ${influencers.length} Influenciadores`,
        totalInfluencers: totalCount
      });
    }
  }, [influencers, totalCount, ownerInfo]);

  // Função para selecionar influenciador
  const handleSelectInfluencer = (influencer: any) => {
    setSelectedInfluencer(influencer);
    
    // Atualizar URL com o influenciador selecionado
    const newSearchParams = new URLSearchParams(searchParams?.toString());
    newSearchParams.set('influencer', influencer.id);
    
    const newUrl = `${pathname}?${newSearchParams.toString()}`;
    router.push(newUrl, { scroll: false });
  };

  // Função para compartilhar a página
  const handleShare = async () => {
    const url = window.location.href;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Lista de Influenciadores',
          text: `Confira esta lista com ${totalCount} influenciadores`,
          url: url
        });
      } catch (error) {
        // Fallback para clipboard
        await navigator.clipboard.writeText(url);
        // toast.success('Link copiado para a área de transferência!');
      }
    } else {
      // Fallback para clipboard
      await navigator.clipboard.writeText(url);
      // toast.success('Link copiado para a área de transferência!');
    }
  };

  // Loading state
  if (!userId || loadingInfluencers) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <Loader className="mx-auto" />
          <p className="text-muted-foreground">Carregando influenciadores...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (errorInfluencers) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4 max-w-md">
          <div className="text-6xl">⚠️</div>
          <h1 className="text-2xl font-bold text-foreground">Erro ao Carregar</h1>
          <p className="text-muted-foreground">
            Não foi possível carregar os influenciadores. Verifique se o link está correto.
          </p>
          <Button onClick={() => refetch()} variant="outline">
            Tentar Novamente
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      <Toaster />
      
      {/* Header da página pública */}
      <div className="h-16 bg-card border-b border-border flex items-center px-6">
        <div className="flex items-center space-x-4">
          <div className="text-2xl">🔓</div>
          <div>
            <h1 className="font-semibold text-foreground flex items-center gap-2">
              <Users className="w-4 h-4" />
              Lista Pública de Influenciadores
            </h1>
            <p className="text-sm text-muted-foreground flex items-center gap-2">
              <Eye className="w-3 h-3" />
              {totalCount} influenciadores disponíveis
            </p>
          </div>
        </div>
        
        <div className="ml-auto flex items-center space-x-3">
          <Badge variant="outline" className="text-xs flex items-center gap-1">
            <Globe className="w-3 h-3" />
            Visualização Pública
          </Badge>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleShare}
            className="flex items-center gap-2"
          >
            <Share2 className="w-4 h-4" />
            Compartilhar
          </Button>
        </div>
      </div>

      {/* Painel de filtros */}
      <div className="bg-card border-b border-border">
        <div className="px-6 py-3">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filtros</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <SlidersHorizontal className="w-4 h-4" />
              {showFilters ? 'Ocultar' : 'Mostrar'} Filtros
            </Button>
          </div>

          {/* Barra de busca sempre visível */}
          <div className="relative mb-3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Buscar influenciadores..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filtros expandidos */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Categoria/Localização */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Categoria</Label>
                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Todas as categorias" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todas as categorias</SelectItem>
                    <SelectItem value="lifestyle">Lifestyle</SelectItem>
                    <SelectItem value="fashion">Moda</SelectItem>
                    <SelectItem value="beauty">Beleza</SelectItem>
                    <SelectItem value="fitness">Fitness</SelectItem>
                    <SelectItem value="food">Gastronomia</SelectItem>
                    <SelectItem value="travel">Viagem</SelectItem>
                    <SelectItem value="tech">Tecnologia</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Seguidores */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Seguidores (mín)</Label>
                <Select value={minFollowers.toString()} onValueChange={(value) => setMinFollowers(Number(value))}>
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Qualquer quantidade</SelectItem>
                    <SelectItem value="1000">1K+</SelectItem>
                    <SelectItem value="10000">10K+</SelectItem>
                    <SelectItem value="50000">50K+</SelectItem>
                    <SelectItem value="100000">100K+</SelectItem>
                    <SelectItem value="500000">500K+</SelectItem>
                    <SelectItem value="1000000">1M+</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Rating */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Avaliação (mín)</Label>
                <Select value={minRating.toString()} onValueChange={(value) => setMinRating(Number(value))}>
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Qualquer avaliação</SelectItem>
                    <SelectItem value="3">3+ estrelas</SelectItem>
                    <SelectItem value="4">4+ estrelas</SelectItem>
                    <SelectItem value="4.5">4.5+ estrelas</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Verificados */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Opções</Label>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="verified"
                    checked={verifiedOnly}
                    onCheckedChange={(checked) => setVerifiedOnly(checked as boolean)}
                  />
                  <Label htmlFor="verified" className="text-sm">Apenas verificados</Label>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex-1 flex overflow-hidden">
        {/* Grade de influenciadores */}
        <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-auto min-h-0">
          <Suspense fallback={<InfluencerGridSkeleton />}>
            {/* 🔓 PÚBLICO: Usar grid customizado para dados públicos */}
            <div className="p-6">
              {influencers.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    Nenhum influenciador encontrado
                  </h3>
                  <p className="text-muted-foreground">
                    Tente ajustar os filtros ou verifique se há influenciadores públicos disponíveis.
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-[repeat(auto-fit,minmax(17rem,1fr))] gap-3">
                  {influencers.map((influencer) => (
                    <PublicInfluencerCard
                      key={influencer.id}
                      influencer={influencer}
                      onClick={() => handleSelectInfluencer(influencer)}
                      isSelected={selectedInfluencer?.id === influencer.id}
                    />
                  ))}
                </div>
              )}
            </div>
          </Suspense>
        </div>
      </div>
    </div>
  );
}
