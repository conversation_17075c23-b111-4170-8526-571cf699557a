'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface NoSrcsetAvatarProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackClassName?: string;
  fallbackText?: string;
  priority?: boolean;
}

const sizeMap = {
  sm: { size: 32, className: 'h-8 w-8' },
  md: { size: 40, className: 'h-10 w-10' },
  lg: { size: 48, className: 'h-12 w-12' },
  xl: { size: 80, className: 'h-20 w-20' }
};

/**
 * Avatar SEM SRCSET - UMA ÚNICA REQUISIÇÃO GARANTIDA
 * 🎯 ESTRATÉGIA: Usar <img> nativo ao invés de next/image
 * ✅ ZERO srcset responsivo - apenas uma versão da imagem
 * 🚀 UMA única requisição HTTP por imagem
 * 💡 Elimina completamente as múltiplas versões (16w, 32w, 48w, etc.)
 * 
 * Este componente usa <img> HTML nativo para garantir que apenas
 * UMA versão da imagem seja carregada, eliminando o srcset do Next.js.
 */
export function NoSrcsetAvatar({
  src,
  alt,
  size = 'md',
  className,
  fallbackClassName,
  fallbackText,
  priority = false
}: NoSrcsetAvatarProps) {
  const sizeConfig = sizeMap[size];
  
  // Gerar iniciais para fallback
  const getInitials = (text: string) => {
    if (!text) return '??';
    return text
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(fallbackText || alt);

  // Gerar cor baseada no nome para consistência
  const getColorFromName = (name: string) => {
    const colors = [
      'from-blue-500 to-purple-600',
      'from-green-500 to-blue-600', 
      'from-purple-500 to-pink-600',
      'from-orange-500 to-red-600',
      'from-teal-500 to-cyan-600',
      'from-indigo-500 to-purple-600',
      'from-pink-500 to-rose-600',
      'from-emerald-500 to-teal-600'
    ];
    
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const gradientColors = getColorFromName(fallbackText || alt);

  // Se não há src, renderizar apenas fallback (ZERO requisições)
  if (!src) {
    return (
      <div 
        className={cn(
          'relative flex shrink-0 overflow-hidden rounded-full bg-gradient-to-br',
          gradientColors,
          sizeConfig.className,
          className
        )}
        role="img"
        aria-label={alt}
      >
        <div 
          className={cn(
            'flex items-center justify-center text-white font-semibold w-full h-full select-none',
            size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : size === 'lg' ? 'text-base' : 'text-lg',
            fallbackClassName
          )}
        >
          {initials}
        </div>
      </div>
    );
  }

  // Otimizar URL da imagem para tamanho específico
  const optimizeImageUrl = (url: string, targetSize: number) => {
    // Se for Firebase Storage, adicionar parâmetros de redimensionamento
    if (url.includes('firebasestorage.googleapis.com') || url.includes('storage.googleapis.com')) {
      // Adicionar parâmetros para redimensionar no servidor
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}alt=media&w=${targetSize}&h=${targetSize}&fit=crop&format=webp&quality=85`;
    }
    
    return url;
  };

  const optimizedSrc = optimizeImageUrl(src, sizeConfig.size);

  return (
    <div 
      className={cn(
        'relative flex shrink-0 overflow-hidden rounded-full bg-gradient-to-br',
        gradientColors,
        sizeConfig.className,
        className
      )}
      role="img"
      aria-label={alt}
    >
      {/* 🎯 IMG NATIVO - SEM SRCSET DO NEXT.JS */}
      <img
        src={optimizedSrc}
        alt={alt}
        width={sizeConfig.size}
        height={sizeConfig.size}
        loading={priority ? 'eager' : 'lazy'}
        decoding={priority ? 'sync' : 'async'}
        fetchPriority={priority ? 'high' : 'auto'}
        
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          objectPosition: 'center',
          display: 'block'
        }}
        
        // Fallback em caso de erro
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          // Mostrar fallback
          const fallbackDiv = target.nextElementSibling as HTMLElement;
          if (fallbackDiv) {
            fallbackDiv.style.display = 'flex';
          }
        }}
        
        onLoad={(e) => {
          // Esconder fallback quando imagem carrega
          const target = e.target as HTMLImageElement;
          const fallbackDiv = target.nextElementSibling as HTMLElement;
          if (fallbackDiv) {
            fallbackDiv.style.display = 'none';
          }
        }}
      />
      
      {/* 🎯 FALLBACK sempre presente */}
      <div 
        className={cn(
          'absolute inset-0 flex items-center justify-center text-white font-semibold',
          size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : size === 'lg' ? 'text-base' : 'text-lg',
          fallbackClassName
        )}
        style={{ display: 'flex' }}
      >
        {initials}
      </div>
    </div>
  );
}

/**
 * Avatar crítico sem srcset para LCP otimizado
 * ✅ UMA única requisição HTTP garantida
 * 🎯 Preload automático para elementos críticos
 */
export function CriticalNoSrcsetAvatar(props: NoSrcsetAvatarProps) {
  return (
    <NoSrcsetAvatar
      {...props}
      priority={true}
    />
  );
}
