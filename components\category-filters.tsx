"use client";

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface CategoryFiltersProps {
  onSelectCategory: (category: string) => void;
  selectedCategory: string;
}

// Cache global para categorias para evitar duplas chamadas
let categoriesCache: {id: string, name: string, slug: string}[] | null = null;
let categoriesCacheTime: number = 0;
let categoriesPromise: Promise<{id: string, name: string, slug: string}[]> | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

export function CategoryFilters({ onSelectCategory, selectedCategory }: CategoryFiltersProps) {
  const [categories, setCategories] = useState<{id: string, name: string, slug: string}[]>([]);

  // Buscar todas as categorias do banco de dados com cache
  useEffect(() => {
    const fetchCategories = async () => {
      // Verificar se já temos dados em cache válidos
      const now = Date.now();
      if (categoriesCache && (now - categoriesCacheTime) < CACHE_DURATION) {
        setCategories(categoriesCache);
        return;
      }

      // Se já há uma requisição em andamento, aguardar ela
      if (categoriesPromise) {
        try {
          const cachedData = await categoriesPromise;
          setCategories(cachedData);
          return;
        } catch (error) {
          // Se a requisição em andamento falhou, continuar com nova requisição
        }
      }

      // Criar nova promise para evitar duplas chamadas
      categoriesPromise = (async () => {
        try {
          const response = await fetch('/api/categories/public');
          
          if (!response.ok) {
            throw new Error('Erro ao buscar categorias');
          }
          
          const data = await response.json();
          
          // Atualizar cache
          categoriesCache = data;
          categoriesCacheTime = Date.now();
          
          return data;
        } catch (error) {
          console.error('Erro ao carregar categorias:', error);
          throw error;
        }
      })();

      try {
        const data = await categoriesPromise;
        setCategories(data);
      } catch (error) {
        toast.error('Não foi possível carregar as categorias');
        setCategories([]);
      } finally {
        categoriesPromise = null;
      }
    };
    
    fetchCategories();
  }, []);

  return (
    <div className="flex items-center space-x-2 overflow-x-auto scrollbar-hide py-1 text-[11px]">
      <button
        onClick={() => onSelectCategory('all')}
        className={`px-3 py-1 rounded-full whitespace-nowrap flex-shrink-0 ${
          selectedCategory === 'all'
            ? 'bg-[#ff0074] text-white'
            : 'bg-black border border-gray-850 text-white hover:bg-[#9810fa]'
        }`}
      >
        Todos
      </button>
      
      {categories.map((category) => (
        <button
          key={category.id}
          onClick={() => onSelectCategory(category.name)}
          className={`px-3 py-1 rounded-full whitespace-nowrap flex-shrink-0 ${
            selectedCategory === category.name
              ? 'bg-[#ff0074] text-white'
              : 'bg-black border border-gray-850 text-white hover:bg-[#9810fa]'
          }`}
        >
          {category.name}
        </button>
      ))}
    </div>
  );
}



