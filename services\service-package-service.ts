import { ServicePackage, PackageService, CreatePackageInput } from '@/types/service-package';

export interface CreateServicePackageInput {
  name: string;
  description?: string;
  totalPrice: number;
  isTemplate?: boolean;
  category?: string;
  tags?: string[];
  minFollowers?: number;
  platforms: string[];
  services: PackageService[];
  influencerId: string;
  influencerName: string;
  proposalId: string;
  userId: string;
  brandId: string;
}

export interface ServicePackageResponse {
  success: boolean;
  message?: string;
  package?: ServicePackage;
  error?: string;
}

export interface GetServicePackagesResponse {
  success: boolean;
  packages: ServicePackage[];
  total: number;
  error?: string;
  details?: string;
  message?: string;
}

/**
 * Cria um novo pacote de serviços
 */
export async function createServicePackage(input: CreateServicePackageInput): Promise<ServicePackageResponse> {
  try {
    console.log('🚀 [SERVICE] Criando pacote de serviços:', {
      name: input.name,
      influencerId: input.influencerId,
      proposalId: input.proposalId,
      servicesCount: input.services.length
    });

    const response = await fetch('/api/service-packages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('❌ [SERVICE] Erro ao criar pacote:', data.error);
      return {
        success: false,
        error: data.error || 'Erro ao criar pacote de serviços'
      };
    }

    console.log('✅ [SERVICE] Pacote criado com sucesso:', data.package?.id);
    return data;

  } catch (error) {
    console.error('❌ [SERVICE] Erro na requisição:', error);
    return {
      success: false,
      error: 'Erro de conexão ao criar pacote de serviços'
    };
  }
}

/**
 * Busca pacotes de serviços de um influenciador em uma proposta
 */
export async function getServicePackages(
  userId: string,
  proposalId: string,
  influencerId: string
): Promise<GetServicePackagesResponse> {
  try {
    console.log('📊 [SERVICE] Buscando pacotes de serviços:', {
      userId,
      proposalId,
      influencerId
    });

    const params = new URLSearchParams({
      userId,
      proposalId,
      influencerId
    });

    const response = await fetch(`/api/service-packages?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      console.error('❌ [SERVICE] Erro ao buscar pacotes:', {
        status: response.status,
        statusText: response.statusText,
        error: data.error,
        details: data.details
      });
      return {
        success: false,
        packages: [],
        total: 0,
        error: data.error || 'Erro ao buscar pacotes de serviços',
        details: data.details
      };
    }

    console.log('✅ [SERVICE] Pacotes encontrados:', data.total);
    return data;

  } catch (error: any) {
    console.error('❌ [SERVICE] Erro na requisição:', {
      message: error.message,
      name: error.name,
      stack: error.stack
    });
    return {
      success: false,
      packages: [],
      total: 0,
      error: 'Erro de conexão ao buscar pacotes de serviços',
      details: error.message
    };
  }
}

/**
 * Valida os dados de um pacote antes de enviar
 */
export function validatePackageData(input: CreateServicePackageInput): string[] {
  const errors: string[] = [];

  // Validar nome
  if (!input.name?.trim()) {
    errors.push('Nome do pacote é obrigatório');
  }

  // Validar campos obrigatórios
  if (!input.influencerId) errors.push('ID do influenciador é obrigatório');
  if (!input.proposalId) errors.push('ID da proposta é obrigatório');
  if (!input.userId) errors.push('ID do usuário é obrigatório');
  if (!input.brandId) errors.push('ID da marca é obrigatório');

  // Validar serviços
  if (!input.services || input.services.length === 0) {
    errors.push('Pelo menos um serviço deve ser selecionado');
  } else {
    input.services.forEach((service, index) => {
      if (!service.serviceType) {
        errors.push(`Serviço ${index + 1}: Tipo de serviço é obrigatório`);
      }
      if (!service.platform) {
        errors.push(`Serviço ${index + 1}: Plataforma é obrigatória`);
      }
      if (!service.serviceName) {
        errors.push(`Serviço ${index + 1}: Nome do serviço é obrigatório`);
      }
      if (service.quantity <= 0) {
        errors.push(`Serviço ${index + 1}: Quantidade deve ser maior que 0`);
      }
      if (service.unitPrice < 0) {
        errors.push(`Serviço ${index + 1}: Preço unitário não pode ser negativo`);
      }
      
      // Validar cálculo do preço total
      const expectedTotal = service.quantity * service.unitPrice;
      if (Math.abs(service.totalPrice - expectedTotal) > 0.01) {
        errors.push(`Serviço ${index + 1}: Preço total incorreto`);
      }
    });
  }

  // Validar preço total do pacote
  if (input.services && input.services.length > 0) {
    const calculatedTotal = input.services.reduce((total, service) => total + service.totalPrice, 0);
    if (Math.abs(input.totalPrice - calculatedTotal) > 0.01) {
      errors.push('Preço total do pacote não confere com a soma dos serviços');
    }
  }

  return errors;
}

/**
 * Formata um pacote para exibição
 */
export function formatPackageForDisplay(pkg: ServicePackage) {
  return {
    ...pkg,
    formattedTotalPrice: pkg.totalPrice.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }),
    servicesCount: pkg.services.length,
    totalItems: pkg.services.reduce((total, service) => total + service.quantity, 0),
    platformsCount: pkg.platforms.length,
    formattedCreatedAt: pkg.createdAt.toLocaleDateString('pt-BR'),
    formattedUpdatedAt: pkg.updatedAt.toLocaleDateString('pt-BR')
  };
}

/**
 * Calcula estatísticas de um pacote
 */
export function calculatePackageStats(pkg: ServicePackage) {
  const servicesByPlatform = pkg.services.reduce((acc, service) => {
    if (!acc[service.platform]) {
      acc[service.platform] = {
        count: 0,
        totalValue: 0,
        services: []
      };
    }
    acc[service.platform].count += service.quantity;
    acc[service.platform].totalValue += service.totalPrice;
    acc[service.platform].services.push(service);
    return acc;
  }, {} as Record<string, { count: number; totalValue: number; services: PackageService[] }>);

  const averageServicePrice = pkg.services.length > 0 
    ? pkg.totalPrice / pkg.services.reduce((total, service) => total + service.quantity, 0)
    : 0;

  return {
    servicesByPlatform,
    averageServicePrice,
    totalServices: pkg.services.length,
    totalItems: pkg.services.reduce((total, service) => total + service.quantity, 0),
    mostExpensiveService: pkg.services.reduce((max, service) => 
      service.totalPrice > max.totalPrice ? service : max, pkg.services[0]
    ),
    cheapestService: pkg.services.reduce((min, service) => 
      service.totalPrice < min.totalPrice ? service : min, pkg.services[0]
    )
  };
}
