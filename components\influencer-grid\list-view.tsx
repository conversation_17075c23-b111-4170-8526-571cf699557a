

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Influencer } from "./types"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { InfluencerAvatar } from "@/components/ui/influencer-avatar"
import { Badge } from "@/components/ui/badge"
import { Instagram, Youtube, Music, Square, CheckSquare } from "lucide-react"
import { useEffect, useState } from "react"
import axios from "axios"
import { useTranslations } from "@/hooks/use-translations"


// Interface para a categoria
interface Category {
  id: string;
  name: string;
  slug: string;
  color?: string;
}

interface ListViewProps {
  influencers: Influencer[];
  onInfluencerClick: (influencer: Influencer) => void;
  selectedInfluencers: string[];
  selectionMode: boolean;
  onToggleSelection: (id: string) => void;
}

export function ListView({
  influencers,
  onInfluencerClick,
  selectedInfluencers,
  selectionMode,
  onToggleSelection
}: ListViewProps) {
  const { t, isLoading: translationsLoading } = useTranslations();
  // Estado para armazenar as categorias
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Headers com fallback para garantir que sempre mostrem texto correto
  const getTableHeader = (key: string, fallback: string) => {
    const translation = t(`influencers.table_headers.${key}`);
    // Se a tradução retorna '...' (loading) ou a própria chave, usar fallback
    if (translation === '...' || translation === `influencers.table_headers.${key}` || translationsLoading) {
      return fallback;
    }
    return translation;
  };
  
  // Função utilitária para formatar localização filtrando valores inválidos
  const formatLocationDisplay = (influencer: Influencer) => {
    if (influencer.location && influencer.location.trim() !== "") {
      return influencer.location;
    }
    
    // Type-safe check for GraphQL fields that might not exist in the base interface
    const infAny = influencer as any;
    const validCity = infAny.city && infAny.city !== t('influencers.not_informed') && infAny.city.trim() !== "" ? infAny.city : null;
    const validState = infAny.state && infAny.state !== t('influencers.not_informed') && infAny.state.trim() !== "" ? infAny.state : null;
    const validCountry = infAny.country && infAny.country !== t('influencers.not_informed') && infAny.country.trim() !== "" ? infAny.country : null;
    
    if (validCity && validState) {
      return `${validCity}, ${validState}`;
    } else if (validCity && validCountry) {
      return `${validCity}, ${validCountry}`;
    } else if (validState && validCountry) {
      return `${validState}, ${validCountry}`;
    } else if (validCountry) {
      return validCountry;
    } else if (validCity) {
      return validCity;
    } else if (validState) {
      return validState;
    }
    return t('influencers.location_not_informed');
  };
  
  // Função para formatar números em K e MI
  const formatarNumero = (numero: string | number): string => {
    const num = typeof numero === 'string' ? parseInt(numero.replace(/[^0-9]/g, '')) : numero;
    
    if (isNaN(num)) return '0';
    
    if (num >= 1000000) {
      return (num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 1).replace('.0', '') + 'MI';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1).replace('.0', '') + 'K';
    } else {
      return num.toString();
    }
  };

  // Função para buscar categorias
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Tenta obter categorias do cache
        const cachedCategories = localStorage.getItem('categories');
        if (cachedCategories) {
          setCategories(JSON.parse(cachedCategories));
          setIsLoading(false);
          return;
        }

        // Se não houver cache, busca da API pública (para páginas compartilhadas)
        const response = await axios.get('/api/categories/public');
        setCategories(response.data);
        
        // Salva no cache
        localStorage.setItem('categories', JSON.stringify(response.data));
      } catch (error) {
        console.error('Erro ao buscar categorias:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCategories();
  }, []);

  // Função para obter o nome da categoria a partir do ID
  const getCategoryName = (categoryId: string) => {
    if (!categoryId) return 'Sem categoria';
    
    // Se as categorias ainda estão carregando, mostra um placeholder
    if (isLoading) {
      return 'Carregando...';
    }
    
    // Buscar a categoria no array de categorias
    const category = categories.find(cat => cat.id === categoryId);
    
    if (category) {
      return category.name; // Retorna o nome real da categoria
    }
    
    // Se não encontrou a categoria, retorna o ID formatado como fallback
    return categoryId.charAt(0).toUpperCase() + categoryId.slice(1).toLowerCase();
  };
  
  // Função para obter a cor da categoria
  const getCategoryColor = (categoryId: string) => {
    if (!categoryId) return '#cccccc';
    
    // Buscar a categoria no array de categorias
    const category = categories.find(cat => cat.id === categoryId);
    
    if (category && category.color) {
      return category.color; // Retorna a cor definida da categoria
    }
    
    return "#9810fa"; // Cor padrão
  };

  return (
    <div className="w-full overflow-auto">
      <Table>
        <TableHeader>
          <TableRow className="space-x-0">
            {selectionMode && <TableHead className="w-12 p-1"></TableHead>}
            <TableHead className="w-12 p-1"></TableHead>
            <TableHead className="p-1">{getTableHeader('name', 'Nome')}</TableHead>
            <TableHead className="p-1">{getTableHeader('category', 'Categoria')}</TableHead>
            <TableHead className="p-1">{getTableHeader('location', 'Localização')}</TableHead>
            <TableHead className="p-1">{getTableHeader('instagram', 'Instagram')}</TableHead>
            <TableHead className="p-1">{getTableHeader('youtube', 'YouTube')}</TableHead>
            <TableHead className="p-1">{getTableHeader('tiktok', 'TikTok')}</TableHead>
            <TableHead className="p-1">{getTableHeader('value', 'Valor')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {influencers.map((influencer) => (
            <TableRow 
              key={influencer.id}
              className={`cursor-pointer ${
                selectedInfluencers.includes(String(influencer.id)) ? 'bg-black/10 dark:bg-white/10' : ''
              }`}
              onClick={() => selectionMode ? onToggleSelection(String(influencer.id)) : onInfluencerClick(influencer)}
            >
              {selectionMode && (
                <TableCell className="p-1">
                  <div 
                    className="flex items-center justify-center"
                    onClick={(e) => {
                      e.stopPropagation();
                      onToggleSelection(String(influencer.id));
                    }}
                  >
                    {selectedInfluencers.includes(String(influencer.id)) ? (
                      <CheckSquare className="h-5 w-5 text-primary" />
                    ) : (
                      <Square className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                </TableCell>
              )}
              <TableCell className="p-1">
                <div className="relative">
                  <InfluencerAvatar
              influencerId={influencer.id.toString()}
              influencerName={influencer.name}
              avatarPath={influencer.avatar}
              avatarUrl={influencer.avatar}
                    className="h-10 w-10"
                  />
                  {influencer.verified && (
                    <div className="absolute -bottom-0.5 -right-[0px] bg-transparent text-white rounded-full h-4 w-4 flex items-center justify-center">
                      <svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 156.61 189.98" width="20" height="20">
                        <defs>
                          <linearGradient id="Gradiente_sem_nome_147" data-name="Gradiente sem nome 147" x1="52.12" y1="184.26" x2="115.13" y2="9.61" gradientUnits="userSpaceOnUse">
                            <stop offset="0" stopColor="#ff0074"/>
                            <stop offset="1" stopColor="#ff0074"/>
                          </linearGradient>
                        </defs>
                        <g id="Camada_1-2" data-name="Camada 1">
                          <path className="cls-1" fill="url(#Gradiente_sem_nome_147)" d="m155.35,97.66h0c-.82-4.58-2.05-9.01-3.63-13.28-5.77-15.52-16.32-28.72-29.87-37.79,1.72,4.96,2.66,10.29,2.66,15.84,0,4.5-.61,8.86-1.77,12.99-12.01-8.56-19.95-22.48-20.31-38.27-.01-.39-.02-.78-.02-1.16,0-4.5.61-8.86,1.77-12.99,1.02-3.68,2.46-7.18,4.28-10.44,2.62-4.73,6.01-8.97,10-12.56-9.86.78-19.21,3.38-27.71,7.47-3.97,1.91-7.75,4.15-11.32,6.68-8.21,5.82-15.25,13.19-20.7,21.68-7.82,12.18-12.35,26.68-12.35,42.22,0,3.88.28,7.68.83,11.41.71,4.87,1.87,9.59,3.43,14.12-3.55-2.2-6.8-4.85-9.67-7.87-8.22-8.67-13.26-20.39-13.26-33.29,0-4.04.5-7.96,1.43-11.7C14.54,62.52,4.26,79.45,1.06,98.78c-.7,4.2-1.06,8.51-1.06,12.9,0,43.25,35.06,78.3,78.3,78.3,27.69,0,52.03-14.38,65.95-36.08,7.82-12.19,12.35-26.68,12.35-42.23,0-4.78-.43-9.47-1.25-14.02Zm-77.15,77.38c-17.82,0-33.39-9.63-41.79-23.98,12.07,7.61,26.37,12.01,41.69,12.01,12.16,0,23.68-2.77,33.94-7.72,2.79-1.35,5.48-2.85,8.07-4.49-1.03,1.78-2.17,3.48-3.41,5.11-8.84,11.59-22.79,19.07-38.5,19.07Z"/>
                        </g>
                      </svg>
                    </div>
                  )}
                </div>
              </TableCell>
              <TableCell className="p-1">
                <span className="font-medium">{influencer.name}</span>
              </TableCell>
              <TableCell className="p-1">
                <div className="flex flex-wrap gap-1">
                  {(influencer.mainCategories && influencer.mainCategories.length > 0 ? 
                    influencer.mainCategories.slice(0, 1) : 
                    influencer.category ? [influencer.category] : []
                  ).map((categoryId, i) => (
                    <Badge 
                      key={i} 
                      variant="outline" 
                      className={`text-xs px-1.5 py-0 bg-[#ff0074] text-white dark:text-white border-gray-850 hover:bg-[${getCategoryColor(categoryId)}] hover:text-[#ff0074]`}
                    >
                      <div className={`w-1.5 h-1.5 bg-[#9810fa] rounded-full mr-1`}></div>
                      {getCategoryName(categoryId)}
                    </Badge>
                  ))}
                  {influencer.mainCategories && influencer.mainCategories.length > 1 && (
                    <Badge className="text-white border-gray-700 bg-black text-[10px] py-0 px-2 h-5">
                      +{influencer.mainCategories.length - 1}
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell className="p-1">{formatLocationDisplay(influencer)}</TableCell>
              <TableCell className="p-1">
                <div className="flex items-center">
                  <a 
                    href={`https://instagram.com/${influencer.socialNetworks?.instagram?.username || ''}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={(e) => e.stopPropagation()}
                    className="flex items-center hover:opacity-80 transition-opacity space-x-0.5"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                      <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                    </svg>
                    <span>{formatarNumero(influencer.socialNetworks?.instagram?.followers || influencer.instagram)}</span>
                  </a>
                </div>
              </TableCell>
              <TableCell className="p-1">
                <div className="flex items-center">
                  <a 
                    href={`https://youtube.com/@${influencer.socialNetworks?.youtube?.username || ''}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={(e) => e.stopPropagation()}
                    className="flex items-center hover:opacity-80 transition-opacity space-x-0.5"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className="text-black dark:text-white">
                      <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                    </svg>
                    <span>{formatarNumero(influencer.socialNetworks?.youtube?.followers || influencer.youtube)}</span>
                  </a>
                </div>
              </TableCell>
              <TableCell className="p-1">
                <div className="flex items-center">
                  <a 
                    href={`https://tiktok.com/@${influencer.socialNetworks?.tiktok?.username || ''}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={(e) => e.stopPropagation()}
                    className="flex items-center hover:opacity-80 transition-opacity space-x-0.5"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 448 512" className="text-black dark:text-white">
                      <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
                    </svg>
                    <span>{formatarNumero(influencer.socialNetworks?.tiktok?.followers || influencer.tiktok)}</span>
                  </a>
                </div>
              </TableCell>
              <TableCell className="p-1">
                <span className="font-medium">{influencer.rate}</span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}



