"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, Loader2, Upload } from "lucide-react";
import { compressImage, validateImageFile } from "@/lib/image-compression";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth-v2";
import { useMutation } from "@apollo/client";
import { UPLOAD_AVATAR_MUTATION } from "@/lib/graphql/mutations";

interface UserAvatarUploadProps {
  currentAvatarUrl?: string;
  userId: string;
  userName?: string;
  onAvatarUpdate?: (newAvatarUrl: string) => void;
  className?: string;
}

export function UserAvatarUpload({ 
  currentAvatarUrl, 
  userId, 
  userName,
  onAvatarUpdate,
  className = "" 
}: UserAvatarUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentAvatarUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { updateUserAvatar } = useAuth();
  
  // Mutation GraphQL para upload de avatar
  const [uploadAvatarMutation] = useMutation(UPLOAD_AVATAR_MUTATION);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validar arquivo
    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Erro no arquivo",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(true);
      
      // Comprimir imagem para avatar (pequeno)
      const compressedFile = await compressImage(file, {
        maxWidth: 200,
        maxHeight: 200,
        quality: 0.9,
        format: 'jpeg'
      });
      
      // Converter arquivo para base64 para envio via GraphQL
      const base64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(compressedFile);
      });

      // Exibir preview imediatamente
      setPreviewUrl(base64);

      console.log('👤 [Avatar] Enviando via GraphQL mutation:', {
        userId,
        filename: compressedFile.name,
        size: compressedFile.size,
        contentType: compressedFile.type
      });

      // Fazer upload via GraphQL mutation
      const { data } = await uploadAvatarMutation({
        variables: {
          input: {
            userId,
            filename: compressedFile.name,
            size: compressedFile.size,
            contentType: compressedFile.type,
            fileData: base64
          }
        }
      });

      const result = data?.uploadAvatar;

      if (!result?.success) {
        throw new Error(result?.message || 'Erro no upload via GraphQL');
      }

      console.log('✅ [Avatar] Upload GraphQL concluído:', result.avatarUrl);

      // Atualizar URL do avatar
      setPreviewUrl(result.avatarUrl);
      
      // Atualizar contexto de autenticação
      await updateUserAvatar(result.avatarUrl);
      
      // Callback para atualizar componente pai (opcional)
      if (onAvatarUpdate) {
        onAvatarUpdate(result.avatarUrl);
      }

      toast({
        title: "Sucesso!",
        description: result.message || "Avatar atualizado com sucesso!",
      });

    } catch (error) {
      console.error("Erro ao fazer upload do avatar:", error);
      
      // Reverter preview em caso de erro
      setPreviewUrl(currentAvatarUrl || null);
      
      toast({
        title: "Erro no upload",
        description: error instanceof Error ? error.message : "Erro inesperado ao fazer upload",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const getInitials = () => {
    if (!userName) return 'U';
    return userName
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`flex flex-col items-center gap-4 ${className}`}>
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        ref={fileInputRef}
        className="hidden"
        id="user-avatar-upload"
      />

      {/* Avatar com overlay para upload */}
      <div className="relative group">
        <Avatar className="h-24 w-24 border-4 border-[#ff0074]/20 transition-all duration-200 group-hover:border-[#ff0074]/40">
          <AvatarImage
            src={previewUrl || ''}
            alt={userName || 'Avatar do usuário'}
          />
          <AvatarFallback className="bg-[#ff0074]/10 text-[#ff0074] text-2xl font-bold">
            {getInitials()}
          </AvatarFallback>
        </Avatar>

        {/* Overlay para upload */}
        <div 
          className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer"
          onClick={triggerFileInput}
        >
          {isUploading ? (
            <Loader2 className="h-6 w-6 text-white animate-spin" />
          ) : (
            <Camera className="h-6 w-6 text-white" />
          )}
        </div>
      </div>

      {/* Botão de upload */}
      <Button 
        variant="outline" 
        size="sm" 
        onClick={triggerFileInput}
        disabled={isUploading}
        className="border-[#ff0074]/30 hover:border-[#ff0074] hover:bg-[#ff0074]/10"
      >
        {isUploading ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Carregando...
          </>
        ) : (
          <>
            <Upload className="h-4 w-4 mr-2" />
            Alterar Avatar
          </>
        )}
      </Button>

      {/* Instruções */}
      <p className="text-xs text-muted-foreground text-center max-w-sm">
        Clique no avatar ou no botão para alterar sua foto de perfil.
        <br />
        Formatos aceitos: JPEG, PNG, WebP (máx. 5MB)
      </p>
    </div>
  );
} 


