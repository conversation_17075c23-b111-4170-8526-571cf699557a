'use client';

import React, { useState, useEffect, Suspense, useMemo, useCallback } from 'react';

// Força renderização dinâmica para evitar problemas de SSG
export const dynamic = 'force-dynamic';
import { useSearchParams } from 'next/navigation';
import { Protect } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { OptimizedAvatar } from '@/components/ui/optimized-avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Filter, SortAsc, SortDesc, MapPin, Users, Heart, MessageCircle, Share2, Eye, Instagram, Youtube, Phone, Calendar, X, Loader2, Mail, BarChart3, Trash2, CheckSquare, User, Square, TrendingUp, FileText, Send, Plus, ChevronRight } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { TiktokIcon } from '@/components/ui/tiktok-icon';

import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { ChartContainer, ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, PieChart, Pie, Cell } from 'recharts';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { motion } from 'framer-motion';

// Componentes separados
import { ToolbarFiltros } from '@/components/campanhas/toolbar-filtros';
import { NavigationTabs } from '@/components/campanhas/navigation-tabs';
import { InfluencerTable } from '@/components/campanhas/influencer-table';
import { InfluencerDetails } from '@/components/campanhas/influencer-details';
import { CreateGroupDialog } from '@/components/campanhas/dialogs';
import { createTableColumns } from '@/components/campanhas/table-columns';
import AgeRangeBarChart from '@/components/age-range-bar-chart';
import { 
  formatNumber, 
  formatCurrency, 
  formatProposalCurrency, 
  renderStatusBadge, 
  getAvailableServices, 
  getServiceIcon 
} from '@/components/campanhas/utils';

// Hook de autenticação
import { useFirebaseAuth } from '@/contexts/firebase-auth-context';

interface Influencer {
  id: string;
  nome: string;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  servicos: {
    postFeed: number;
    stories: number;
    reels: number;
    videoYoutube: number;
    videoTiktok: number;
  };
  avatar?: string;
  // Campos de visualizações específicas
  stories_views?: number | string;
  instagram_reels_views?: number | string;
  youtube_long_video_views?: number | string;
  youtube_shorts_views?: number | string;
  tiktok_views?: number | string;
  // Dados financeiros
  dadosFinanceiros?: {
    precos?: {
      instagramStory?: { price: number };
      instagramReel?: { price: number };
      youtubeInsertion?: { price: number };
      youtubeDedicated?: { price: number };
      youtubeShorts?: { price: number };
      tiktokVideo?: { price: number };
    };
    responsavel?: string;
    agencia?: string;
    whatsappFinanceiro?: string;
    emailFinanceiro?: string;
  };
  // Dados demográficos da audiência
  audienceGender?: {
    instagram?: {
      female: number;
      male: number;
      other: number;
    };
    youtube?: {
      female: number;
      male: number;
      other: number;
    };
    tiktok?: {
      female: number;
      male: number;
      other: number;
    };
    facebook?: {
      female: number;
      male: number;
      other: number;
    };
    twitch?: {
      female: number;
      male: number;
      other: number;
    };
    kwai?: {
      female: number;
      male: number;
      other: number;
    };
  };
  audienceAgeRanges?: {
    instagram?: { ageRange: string; percentage: number }[];
    youtube?: { ageRange: string; percentage: number }[];
    tiktok?: { ageRange: string; percentage: number }[];
    facebook?: { ageRange: string; percentage: number }[];
    twitch?: { ageRange: string; percentage: number }[];
    kwai?: { ageRange: string; percentage: number }[];
  };
}

interface BrandInfluencer {
  id: string;
  brandId: string;
  brandName: string;
  influencerId: string;
  influencerName: string;
  influencerData: Influencer;
  status: 'enviado' | 'visualizado' | 'interessado' | 'rejeitado' | 'proposta_enviada';
  sentAt: Date;
  viewedAt?: Date;
  lastInteractionAt?: Date;
  notes?: string;
  createdBy: string;
  updatedAt: Date;
}

interface CustomBudget {
  influencerId: string;
  serviceType: 'instagramStory' | 'instagramReel' | 'youtubeInsertion' | 'youtubeDedicated' | 'youtubeShorts' | 'tiktokVideo';
  customPrice: number;
  notes?: string;
  createdAt: Date;
}

interface FilterState {
  busca: string;
  pais: string;
  categoria: string;
  genero: string;
  verificado: boolean | null;
  divulgaTrader: boolean | null;
  minSeguidores: number;
  maxSeguidores: number;
}

interface SortState {
  campo: keyof Influencer | 'seguidores' | 'engajamento';
  direcao: 'asc' | 'desc';
}

interface ProposalService {
  id: string;
  name: string;
  platform: 'instagram' | 'youtube' | 'tiktok';
  type: 'post' | 'story' | 'reel' | 'video' | 'short';
  description: string;
  basePrice?: number;
}

interface Proposal {
  id: string;
  brandId: string;
  brandName: string;
  influencerId: string;
  influencerName: string;
  services: {
    serviceId: string;
    quantity: number;
    customPrice?: number;
    notes?: string;
  }[];
  totalValue: number;
  message: string;
  status: 'rascunho' | 'enviada' | 'aceita' | 'rejeitada' | 'negociando';
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  // Novos campos adicionados
  dataEnvio?: Date;
  grupo?: string;
}

const mockInfluencers: Influencer[] = [
  {
    id: '1',
    nome: 'Ana Silva',
    verified: true,
    pais: 'Brasil',
    cidade: 'São Paulo',
    estado: 'SP',
    idade: 28,
    categoria: 'Lifestyle',
    divulgaTrader: true,
    genero: 'Feminino',
    whatsapp: '+5511999999999',
    redesSociais: {
      instagram: {
        username: '@anasilva',
        seguidores: 150000,
        engajamento: 4.2
      },
      youtube: {
        username: 'Ana Silva',
        seguidores: 85000,
        visualizacoes: 2500000
      }
    },
    servicos: {
      postFeed: 2500,
      stories: 800,
      reels: 1200,
      videoYoutube: 3500,
      videoTiktok: 1800
    },
    avatar: '/avatars/ana.jpg',
    audienceGender: {
      instagram: {
        female: 65,
        male: 30,
        other: 5
      },
      youtube: {
        female: 55,
        male: 40,
        other: 5
      }
    },
    audienceAgeRanges: {
      instagram: [
        { ageRange: "13-17", percentage: 8 },
        { ageRange: "18-24", percentage: 35 },
        { ageRange: "25-34", percentage: 28 },
        { ageRange: "35-44", percentage: 18 },
        { ageRange: "45-54", percentage: 8 },
        { ageRange: "55-64", percentage: 2 },
        { ageRange: "65+", percentage: 1 }
      ],
      youtube: [
        { ageRange: "13-17", percentage: 5 },
        { ageRange: "18-24", percentage: 25 },
        { ageRange: "25-34", percentage: 35 },
        { ageRange: "35-44", percentage: 22 },
        { ageRange: "45-54", percentage: 10 },
        { ageRange: "55-64", percentage: 2 },
        { ageRange: "65+", percentage: 1 }
      ]
    }
  },
  {
    id: '2',
    nome: 'Carlos Santos',
    verified: false,
    pais: 'Brasil',
    cidade: 'Rio de Janeiro',
    estado: 'RJ',
    idade: 32,
    categoria: 'Fitness',
    divulgaTrader: false,
    genero: 'Masculino',
    whatsapp: '+5521888888888',
    redesSociais: {
      instagram: {
        username: '@carlosfit',
        seguidores: 89000,
        engajamento: 3.8
      },
      tiktok: {
        username: '@carlosfit',
        seguidores: 120000,
        curtidas: 1800000
      }
    },
    servicos: {
      postFeed: 1800,
      stories: 600,
      reels: 950,
      videoYoutube: 2800,
      videoTiktok: 1500
    },
    audienceGender: {
      instagram: {
        female: 45,
        male: 50,
        other: 5
      },
      tiktok: {
        female: 40,
        male: 55,
        other: 5
      }
    },
    audienceAgeRanges: {
      instagram: [
        { ageRange: "13-17", percentage: 12 },
        { ageRange: "18-24", percentage: 30 },
        { ageRange: "25-34", percentage: 32 },
        { ageRange: "35-44", percentage: 15 },
        { ageRange: "45-54", percentage: 8 },
        { ageRange: "55-64", percentage: 2 },
        { ageRange: "65+", percentage: 1 }
      ],
      tiktok: [
        { ageRange: "13-17", percentage: 18 },
        { ageRange: "18-24", percentage: 42 },
        { ageRange: "25-34", percentage: 25 },
        { ageRange: "35-44", percentage: 10 },
        { ageRange: "45-54", percentage: 4 },
        { ageRange: "55-64", percentage: 1 },
        { ageRange: "65+", percentage: 0 }
      ]
    }
  },
  {
    id: '3',
    nome: 'Mariana Costa',
    verified: true,
    pais: 'Brasil',
    cidade: 'Belo Horizonte',
    estado: 'MG',
    idade: 25,
    categoria: 'Beleza',
    divulgaTrader: true,
    genero: 'Feminino',
    whatsapp: '+5531777777777',
    redesSociais: {
      instagram: {
        username: '@maribeleza',
        seguidores: 220000,
        engajamento: 5.1
      },
      youtube: {
        username: 'Mari Beleza',
        seguidores: 95000,
        visualizacoes: 1800000
      },
      tiktok: {
        username: '@maribeleza',
        seguidores: 180000,
        curtidas: 2200000
      }
    },
    servicos: {
      postFeed: 3200,
      stories: 1000,
      reels: 1500,
      videoYoutube: 4200,
      videoTiktok: 2100
    },
    avatar: '/avatars/mariana.jpg',
    audienceGender: {
      instagram: {
        female: 70,
        male: 25,
        other: 5
      },
      youtube: {
        female: 60,
        male: 35,
        other: 5
      },
      tiktok: {
        female: 75,
        male: 20,
        other: 5
      }
    },
    audienceAgeRanges: {
      instagram: [
        { ageRange: "13-17", percentage: 15 },
        { ageRange: "18-24", percentage: 45 },
        { ageRange: "25-34", percentage: 25 },
        { ageRange: "35-44", percentage: 10 },
        { ageRange: "45-54", percentage: 4 },
        { ageRange: "55-64", percentage: 1 },
        { ageRange: "65+", percentage: 0 }
      ],
      youtube: [
        { ageRange: "13-17", percentage: 10 },
        { ageRange: "18-24", percentage: 38 },
        { ageRange: "25-34", percentage: 30 },
        { ageRange: "35-44", percentage: 15 },
        { ageRange: "45-54", percentage: 5 },
        { ageRange: "55-64", percentage: 2 },
        { ageRange: "65+", percentage: 0 }
      ],
      tiktok: [
        { ageRange: "13-17", percentage: 22 },
        { ageRange: "18-24", percentage: 48 },
        { ageRange: "25-34", percentage: 20 },
        { ageRange: "35-44", percentage: 7 },
        { ageRange: "45-54", percentage: 2 },
        { ageRange: "55-64", percentage: 1 },
        { ageRange: "65+", percentage: 0 }
      ]
    }
  }
];

const categorias = ['Todas', 'Lifestyle', 'Fitness', 'Beleza', 'Tecnologia', 'Viagem', 'Culinária', 'Moda'];
const paises = ['Todos', 'Brasil', 'Argentina', 'Chile', 'Colômbia', 'México'];
const generos = ['Todos', 'Masculino', 'Feminino', 'Outro'];

// Componente que usa useSearchParams envolvido em Suspense
function MarcasDashboardContent() {
  const searchParams = useSearchParams();
  const brandId = searchParams.get('brandId') || searchParams.get('brand');
  const brandName = searchParams.get('brandName');
  const influencerIds = searchParams.get('influencers')?.split(',') || [];
  
  // ✅ CORREÇÃO: Hook de autenticação Firebase
  const { currentUser, firebaseUser, isLoading: authLoading } = useFirebaseAuth();
  
  const [brandInfluencers, setBrandInfluencers] = useState<BrandInfluencer[]>([]);
  const [influencers, setInfluencers] = useState<Influencer[]>([]);
  const [filteredInfluencers, setFilteredInfluencers] = useState<Influencer[]>([]);
  const [selectedInfluencers, setSelectedInfluencers] = useState<string[]>([]);
  const [selectedInfluencer, setSelectedInfluencer] = useState<Influencer | null>(null);
  const [showDetailsPanel, setShowDetailsPanel] = useState(false);
  const [showProposalDialog, setShowProposalDialog] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'all' | 'sent' | 'proposals'>('all');
  const [selectionMode, setSelectionMode] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'card'>('list');
  const [createdGroups, setCreatedGroups] = useState<Array<{id: string, name: string, description: string, influencers: string[]}>>([]);
  const [showGroupDialog, setShowGroupDialog] = useState(false);
  const [groupInfluencers, setGroupInfluencers] = useState<string[]>([]);
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [groupLoading, setGroupLoading] = useState(false);


  const [expandedRows, setExpandedRows] = useState<string[]>([]);
  const [customBudgets, setCustomBudgets] = useState<CustomBudget[]>([]);

  const [filters, setFilters] = useState<FilterState>({
    busca: '',
    pais: 'Todos',
    categoria: 'Todas',
    genero: 'Todos',
    verificado: null,
    divulgaTrader: null,
    minSeguidores: 0,
    maxSeguidores: 1000000
  });
  const [sort, setSort] = useState<SortState>({
    campo: 'nome',
    direcao: 'asc'
  });

  // Função para carregar grupos
  const loadGroups = async () => {
    try {
      if (!firebaseUser) {
        console.warn('Usuário não autenticado para carregar grupos');
        return;
      }

      const token = await firebaseUser.getIdToken();
      const response = await fetch(`/api/groups?brandId=${brandId}&createdBy=current-user`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        console.warn(`Erro ao carregar grupos: ${response.status} ${response.statusText}`);
        return;
      }
      
      let result;
      try {
        result = await response.json();
      } catch (parseError) {
        console.warn('Erro ao fazer parse da resposta dos grupos:', parseError);
        return;
      }
      
      if (result && result.success && Array.isArray(result.data)) {
        setCreatedGroups(result.data.map((group: any) => ({
          id: group.id || '',
          name: group.name || 'Grupo sem nome',
          description: group.description || '',
          influencers: Array.isArray(group.influencerIds) ? group.influencerIds : []
        })));
      } else {
        console.warn('Resposta inválida da API de grupos:', result);
      }
    } catch (error) {
      console.warn('Erro na requisição de grupos:', error);
      // Não mostrar toast de erro aqui para não incomodar o usuário
      // A funcionalidade de grupos é secundária e não deve quebrar o dashboard
    }
  };

  // ✅ CORREÇÃO: Carregar dados apenas quando autenticado
  useEffect(() => {
    const loadData = async () => {
      // Aguardar autenticação estar completa
      if (authLoading) return;
      
      try {
        // Se há influenciadores específicos na URL, enviar para a marca
        if (influencerIds.length > 0 && brandId && firebaseUser) {
          await sendInfluencersToBrand();
        }

        // Carregar influenciadores da marca se há brandId e usuário autenticado
        if (brandId && firebaseUser) {
          await loadBrandInfluencers();
          await loadGroups();
        } else {
          // Se não há brandId, usar dados mockados para demonstração
          setInfluencers(mockInfluencers);
          setBrandInfluencers([]);
        }
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        toast({
          title: "Erro",
          description: "Erro ao carregar dados da marca.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [brandId, firebaseUser, authLoading]); // ✅ Incluir dependências de autenticação

  // Função para enviar influenciadores para a marca (via URL params)
  const sendInfluencersToBrand = async () => {
    if (!brandId || !brandName || influencerIds.length === 0) return;

    try {
      if (!firebaseUser) {
        console.warn('Usuário não autenticado para enviar influenciadores');
        return;
      }

      const token = await firebaseUser.getIdToken();
      
      // Enviar apenas os IDs dos influenciadores
      const response = await fetch('/api/campanhas/influencers', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brandId,
          brandName,
          influencerIds, // Enviar apenas os IDs
          createdBy: currentUser?.id || firebaseUser.uid // ✅ Usar ID do usuário autenticado
        }),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error('Erro ao fazer parse da resposta de erro:', parseError);
          errorData = { error: `Erro HTTP ${response.status}: ${response.statusText}` };
        }
        console.error('Erro da API:', errorData);
        throw new Error(errorData.error || `Erro ao enviar influenciadores (${response.status})`);
      }

      const result = await response.json();
      
      toast({
        title: "Sucesso",
        description: result.message || `${influencerIds.length} influenciador(es) enviado(s) com sucesso para ${brandName}!`,
      });
    } catch (error) {
      console.error('Erro ao enviar influenciadores:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : 'Erro de conexão ao enviar influenciadores',
        variant: "destructive"
      });
      throw error;
    }
  };

  // Função para carregar influenciadores da marca
  const loadBrandInfluencers = async () => {
    if (!brandId) return;

    try {
      if (!firebaseUser) {
        console.warn('Usuário não autenticado para carregar influenciadores da marca');
        return;
      }

      const token = await firebaseUser.getIdToken();
      const response = await fetch(`/api/campanhas/influencers?brandId=${brandId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error('Erro ao fazer parse da resposta de erro:', parseError);
          errorData = { error: `Erro HTTP ${response.status}: ${response.statusText}` };
        }
        throw new Error(errorData.error || `Erro ao carregar influenciadores (${response.status})`);
      }

      const result = await response.json();
      
      // A API retorna { success: true, data: brandInfluencers, count: ... }
      const data = result.data || [];
      setBrandInfluencers(data);
      
      // Extrair dados dos influenciadores para a lista
      const influencerData = data.map((bi: BrandInfluencer) => bi.influencerData);
      setInfluencers(influencerData);
    } catch (error) {
      console.error('Erro ao carregar influenciadores da marca:', error);
      throw error;
    }
  };

  // Aplicar filtros
  useEffect(() => {
    let filtered = influencers.filter(influencer => {
      // Verificar se o influencer não é null ou undefined
      if (!influencer) return false;
      
      const matchBusca = (influencer.nome?.toLowerCase() || '').includes(filters.busca.toLowerCase()) ||
                        (influencer.cidade?.toLowerCase() || '').includes(filters.busca.toLowerCase()) ||
                        (influencer.categoria?.toLowerCase() || '').includes(filters.busca.toLowerCase());
      
      const matchPais = filters.pais === 'Todos' || influencer.pais === filters.pais;
      const matchCategoria = filters.categoria === 'Todas' || influencer?.categoria === filters.categoria;
      const matchGenero = filters.genero === 'Todos' || influencer?.genero === filters.genero;
      const matchVerificado = filters.verificado === null || influencer?.verified === filters.verificado;
      const matchTrader = filters.divulgaTrader === null || influencer.divulgaTrader === filters.divulgaTrader;
      
      const seguidores = influencer.redesSociais?.instagram?.seguidores || 0;
      const matchSeguidores = seguidores >= filters.minSeguidores && seguidores <= filters.maxSeguidores;

      return matchBusca && matchPais && matchCategoria && matchGenero && matchVerificado && matchTrader && matchSeguidores;
    });

    // Aplicar ordenação
    filtered.sort((a, b) => {
      let valueA: any, valueB: any;
      
      switch (sort.campo) {
        case 'seguidores':
          valueA = a.redesSociais?.instagram?.seguidores || 0;
          valueB = b.redesSociais?.instagram?.seguidores || 0;
          break;
        case 'engajamento':
          valueA = a.redesSociais?.instagram?.engajamento || 0;
          valueB = b.redesSociais?.instagram?.engajamento || 0;
          break;
        default:
          valueA = a[sort.campo as keyof Influencer];
          valueB = b[sort.campo as keyof Influencer];
      }

      if (typeof valueA === 'string' && typeof valueB === 'string') {
        valueA = (valueA || '').toLowerCase();
        valueB = (valueB || '').toLowerCase();
      }

      if (sort.direcao === 'asc') {
        return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
      } else {
        return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
      }
    });

    setFilteredInfluencers(filtered);
  }, [influencers, filters, sort]);

  const handleSort = (campo: SortState['campo']) => {
    setSort(prev => ({
      campo,
      direcao: prev.campo === campo && prev.direcao === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectInfluencer = (id: string) => {
    setSelectedInfluencers(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedInfluencers.length === filteredInfluencers.length) {
      setSelectedInfluencers([]);
    } else {
      setSelectedInfluencers(filteredInfluencers.filter(inf => inf?.id).map(inf => inf.id));
    }
  };

  // Função para alternar modo de seleção
  const toggleSelectionMode = () => {
    setSelectionMode(!selectionMode);
    if (selectionMode) {
      setSelectedInfluencers([]);
    }
  };

  // Funções para gerenciar orçamentos personalizados
  const addCustomBudget = (influencerId: string, serviceType: CustomBudget['serviceType'], customPrice: number, notes?: string) => {
    const newBudget: CustomBudget = {
      influencerId,
      serviceType,
      customPrice,
      notes,
      createdAt: new Date()
    };
    
    setCustomBudgets(prev => {
      // Remove orçamento existente para o mesmo influenciador e serviço
      const filtered = prev.filter(budget => 
        !(budget.influencerId === influencerId && budget.serviceType === serviceType)
      );
      return [...filtered, newBudget];
    });
  };

  const getCustomBudget = (influencerId: string, serviceType: CustomBudget['serviceType']): CustomBudget | null => {
    return customBudgets.find(budget => 
      budget.influencerId === influencerId && budget.serviceType === serviceType
    ) || null;
  };

  const removeCustomBudget = (influencerId: string, serviceType: CustomBudget['serviceType']) => {
    setCustomBudgets(prev => 
      prev.filter(budget => 
        !(budget.influencerId === influencerId && budget.serviceType === serviceType)
      )
    );
  };

  // Função para obter status do influenciador
  const getInfluencerStatus = (influencerId: string): BrandInfluencer | null => {
    return brandInfluencers.find(bi => bi.influencerId === influencerId) || null;
  };

  // Função para abrir sheet de proposta
  const openProposalSheet = (influencer: Influencer) => {
    setSelectedInfluencer(influencer);
    setShowProposalDialog(true);
  };

  // Função para criar grupo
  const handleCreateGroup = async () => {
    if (!groupName.trim()) {
      toast({
        title: "Erro",
        description: "Nome do grupo é obrigatório",
        variant: "destructive"
      });
      return;
    }

    setGroupLoading(true);
    try {
      if (!firebaseUser) {
        toast({
          title: "Erro",
          description: "Usuário não autenticado",
          variant: "destructive"
        });
        return;
      }

      const token = await firebaseUser.getIdToken();
      const response = await fetch('/api/groups', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: groupName,
          description: groupDescription,
          influencerIds: groupInfluencers,
          brandId: brandId,
          createdBy: currentUser?.id || firebaseUser.uid // ✅ Usar ID do usuário autenticado
        }),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (parseError) {
          console.error('Erro ao fazer parse da resposta de erro:', parseError);
          errorData = { error: `Erro HTTP ${response.status}: ${response.statusText}` };
        }
        throw new Error(errorData.error || `Erro ao criar grupo (${response.status})`);
      }

      const result = await response.json();
      
      // Atualizar lista local de grupos
      const newGroup = {
        id: result.groupId,
        name: groupName,
        description: groupDescription,
        influencers: groupInfluencers
      };
      
      setCreatedGroups(prev => [...prev, newGroup]);
      
      // Limpar formulário
      setGroupName('');
      setGroupDescription('');
      setGroupInfluencers([]);
      setShowGroupDialog(false);
      setSelectedInfluencers([]);
      setSelectionMode(false);
      
      toast({
        title: "Sucesso",
        description: result.message || `Grupo "${groupName}" criado com ${groupInfluencers.length} influenciador(es)`,
      });
    } catch (error) {
      console.error('Erro ao criar grupo:', error);
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao criar grupo",
        variant: "destructive"
      });
    } finally {
      setGroupLoading(false);
    }
  };



  // Função para lidar com seleção de linhas
  const handleRowSelection = (selectedRows: Influencer[]) => {
    setSelectedInfluencers(selectedRows.filter(row => row?.id).map(row => row.id));
  };

  // Função para alternar expansão de linha
  const toggleRowExpansion = useCallback((influencerId: string) => {
    setExpandedRows(prev => 
      prev.includes(influencerId) 
        ? prev.filter(id => id !== influencerId)
        : [...prev, influencerId]
    );
  }, []);

  // Função para renderizar o botão de expandir separadamente
  const renderExpandButton = useCallback((influencer: Influencer) => {
    const isExpanded = expandedRows.includes(influencer.id);
    
    return (
      <Button
        variant="ghost"
        size="sm"
        onClick={(e) => {
          e.stopPropagation();
          toggleRowExpansion(influencer.id);
        }}
        className="h-6 w-6 p-0 hover:bg-muted/50 rounded-sm"
      >
        <motion.div
          animate={{ rotate: isExpanded ? 90 : 0 }}
          transition={{ duration: 0.2, ease: "easeInOut" }}
        >
          <ChevronRight className="h-3 w-3" />
        </motion.div>
      </Button>
    );
  }, [expandedRows, toggleRowExpansion]);

  const isRowExpanded = useCallback((id: string) => {
    return expandedRows.includes(id);
  }, [expandedRows]);

  // Definições das colunas para a DataTable - Memoizadas para evitar re-renderização
  const influencerColumns = useMemo(() => 
    createTableColumns({
      selectedInfluencers,
      selectionMode,
      activeTab,
      filteredInfluencers,
      handleSelectInfluencer,
      formatNumber,
      formatCurrency,
      getInfluencerStatus,
      renderStatusBadge,
      openProposalSheet,
      expandedRows,
      toggleRowExpansion,
      addCustomBudget,
      getCustomBudget,
      removeCustomBudget
    }),
    [selectedInfluencers, activeTab, filteredInfluencers, brandInfluencers, customBudgets, toggleRowExpansion]
  );

  // Componente para exibir informações demográficas expandidas
  const ExpandedRowContent = ({ influencer }: { influencer: Influencer }) => {
    const { audienceGender, audienceAgeRanges } = influencer;
    
    if (!audienceGender && !audienceAgeRanges) {
      return (
        <div className="p-6 bg-muted/20 border-t">
          <p className="text-muted-foreground text-center">Dados demográficos não disponíveis</p>
        </div>
      );
    }

    const genderChartConfig = {
      female: {
        label: "Feminino",
        color: "#ec003f",
      },
      male: {
        label: "Masculino",
        color: "#008eff",
      },
      other: {
        label: "Outros",
        color: "#9810fa",
      },
    };

    const ageChartConfig = {
      "13-17": { label: "13-17", color: "#8b5cf6" },
      "18-24": { label: "18-24", color: "#06b6d4" },
      "25-34": { label: "25-34", color: "#10b981" },
      "35-44": { label: "35-44", color: "#f59e0b" },
      "45-54": { label: "45-54", color: "#ef4444" },
      "55-64": { label: "55-64", color: "#8b5cf6" },
      "65+": { label: "65+", color: "#6b7280" },
    };

    const createGenderChartData = (data: { female: number; male: number; other: number }) => [
      { gender: "female", value: data.female, fill: genderChartConfig.female.color },
      { gender: "male", value: data.male, fill: genderChartConfig.male.color },
      { gender: "other", value: data.other, fill: genderChartConfig.other.color },
    ];

    const ageRangeChartConfig = {
      "13-17": { label: "13-17", color: "#8b5cf6" },
      "18-24": { label: "18-24", color: "#06b6d4" },
      "25-34": { label: "25-34", color: "#10b981" },
      "35-44": { label: "35-44", color: "#f59e0b" },
      "45-54": { label: "45-54", color: "#ef4444" },
      "55-64": { label: "55-64", color: "#8b5cf6" },
      "65+": { label: "65+", color: "#6b7280" },
    };

    const getAgeRangeColor = (range: string) => {
      return ageRangeChartConfig[range as keyof typeof ageRangeChartConfig]?.color || "#6b7280";
    };

    const createAgeRangeChartData = (data: { ageRange: string; percentage: number }[]) => {
      return data.map(({ ageRange, percentage }) => ({
        ageRange,
        value: percentage,
        fill: getAgeRangeColor(ageRange)
      }));
    };

    const createAgeChartData = (data: { ageRange: string; percentage: number }[]) => 
      data.map(({ ageRange, percentage }) => ({
        ageRange,
        value: percentage,
        fill: ageChartConfig[ageRange as keyof typeof ageChartConfig]?.color || "#6b7280"
      }));

    return (
      <div className="p-4 bg-muted/20 border-t">
        <div className="mb-4">
          <h4 className="text-base font-semibold mb-1">Dados Demográficos da Audiência</h4>
          <p className="text-xs text-muted-foreground">Distribuição de gênero e faixa etária por rede social</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Coluna de Gênero */}
          {audienceGender && (
            <Card className="p-4">
              <h5 className="text-sm font-medium mb-4 text-muted-foreground flex items-center justify-center gap-2">
                <Users className="h-4 w-4" />
                Distribuição por Gênero
              </h5>
              <div className="w-full max-w-3xl mx-auto">
                <ChartContainer config={genderChartConfig} className="h-[300px] w-full">
                  <BarChart
                    data={[
                      {
                        platform: 'Instagram',
                        feminino: audienceGender.instagram?.female || 0,
                        masculino: audienceGender.instagram?.male || 0,
                        outros: audienceGender.instagram?.other || 0,
                      },
                      {
                        platform: 'YouTube',
                        feminino: audienceGender.youtube?.female || 0,
                        masculino: audienceGender.youtube?.male || 0,
                        outros: audienceGender.youtube?.other || 0,
                      },
                      {
                        platform: 'TikTok',
                        feminino: audienceGender.tiktok?.female || 0,
                        masculino: audienceGender.tiktok?.male || 0,
                        outros: audienceGender.tiktok?.other || 0,
                      },
                      {
                        platform: 'Facebook',
                        feminino: audienceGender.facebook?.female || 0,
                        masculino: audienceGender.facebook?.male || 0,
                        outros: audienceGender.facebook?.other || 0,
                      },
                      {
                        platform: 'Twitch',
                        feminino: audienceGender.twitch?.female || 0,
                        masculino: audienceGender.twitch?.male || 0,
                        outros: audienceGender.twitch?.other || 0,
                      },
                      {
                        platform: 'Kwai',
                        feminino: audienceGender.kwai?.female || 0,
                        masculino: audienceGender.kwai?.male || 0,
                        outros: audienceGender.kwai?.other || 0,
                      },
                    ]}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="platform" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <ChartLegend content={<ChartLegendContent />} />
                    <Bar dataKey="feminino" fill="hsl(330 81% 60%)" name="Feminino" />
                    <Bar dataKey="masculino" fill="hsl(221 83% 53%)" name="Masculino" />
                    <Bar dataKey="outros" fill="hsl(262 83% 58%)" name="Outros" />
                  </BarChart>
                </ChartContainer>
              </div>
            </Card>
          )}

          {/* Coluna de Faixa Etária */}
          {audienceAgeRanges && (
            <Card className="p-4">
              <h5 className="text-sm font-medium mb-4 text-muted-foreground flex items-center justify-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Distribuição por Faixa Etária
              </h5>
              <div className="flex justify-center">
                <div className="w-full max-w-3xl">
                  <ChartContainer
                    config={{
                      instagram: { label: "Instagram", color: "hsl(323, 80%, 50%)" },
                      youtube: { label: "YouTube", color: "hsl(0, 80%, 55%)" },
                      tiktok: { label: "TikTok", color: "hsl(0, 0%, 50%)" },
                      facebook: { label: "Facebook", color: "hsl(220, 80%, 50%)" },
                      twitch: { label: "Twitch", color: "hsl(260, 80%, 50%)" },
                      kwai: { label: "Kwai", color: "hsl(32, 80%, 50%)" }
                    }}
                    className="h-[300px] w-full"
                  >
                    <BarChart
                      data={(() => {
                        const allAgeRanges = new Set();
                        const platforms = ['instagram', 'youtube', 'tiktok', 'facebook', 'twitch', 'kwai'] as const;
                        
                        // Coletar todas as faixas etárias
                        platforms.forEach(platform => {
                          const platformData = audienceAgeRanges[platform];
                          if (platformData) {
                            platformData.forEach((item: { ageRange: string; percentage: number }) => {
                              if (item.ageRange && item.percentage > 0) {
                                allAgeRanges.add(item.ageRange);
                              }
                            });
                          }
                        });
                        
                        // Criar dados do gráfico
                        return Array.from(allAgeRanges)
                          .sort((a, b) => {
                            const getMinAge = (range: string) => parseInt(range.split('-')[0]) || 0;
                            return getMinAge(a as string) - getMinAge(b as string);
                          })
                          .map(ageRange => {
                            const item: any = { ageRange };
                            platforms.forEach(platform => {
                              const platformData = audienceAgeRanges[platform];
                              const found = platformData?.find((d: { ageRange: string; percentage: number }) => d.ageRange === ageRange);
                              item[platform] = found?.percentage || 0;
                            });
                            return item;
                          });
                      })()} 
                      margin={{ top: 40, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis 
                        dataKey="ageRange" 
                        className="text-xs fill-muted-foreground"
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis 
                        className="text-xs fill-muted-foreground"
                        tick={{ fontSize: 12 }}
                      />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <ChartLegend content={<ChartLegendContent />} />
                      {audienceAgeRanges.instagram && (
                        <Bar 
                          dataKey="instagram" 
                          fill="hsl(330 81% 60%)" 
                          radius={[2, 2, 0, 0]}
                          name="Instagram"
                        />
                      )}
                      {audienceAgeRanges.youtube && (
                        <Bar 
                          dataKey="youtube" 
                          fill="hsl(0 72% 51%)" 
                          radius={[2, 2, 0, 0]}
                          name="YouTube"
                        />
                      )}
                      {audienceAgeRanges.tiktok && (
                        <Bar 
                          dataKey="tiktok" 
                          fill="hsl(215 20% 65%)" 
                          radius={[2, 2, 0, 0]}
                          name="TikTok"
                        />
                      )}
                      {audienceAgeRanges.facebook && (
                        <Bar 
                          dataKey="facebook" 
                          fill="hsl(221 83% 53%)" 
                          radius={[2, 2, 0, 0]}
                          name="Facebook"
                        />
                      )}
                      {audienceAgeRanges.twitch && (
                        <Bar 
                          dataKey="twitch" 
                          fill="hsl(262 83% 58%)" 
                          radius={[2, 2, 0, 0]}
                          name="Twitch"
                        />
                      )}
                      {audienceAgeRanges.kwai && (
                        <Bar 
                          dataKey="kwai" 
                          fill="hsl(32 95% 44%)" 
                          radius={[2, 2, 0, 0]}
                          name="Kwai"
                        />
                      )}
                    </BarChart>
                  </ChartContainer>
                </div>
              </div>
            </Card>
          )}


        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="h-full p-6 space-y-6 overflow-y-auto">
       

          {/* Header com Filtros */}
          <div className="space-y-4">
            {/* Segunda Linha - Filtros */}
            <div className="space-y-4">
                {/* Busca Principal */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Buscar por nome, cidade ou categoria..."
                    value={filters.busca}
                    onChange={(e) => setFilters(prev => ({ ...prev, busca: e.target.value }))}
                    className="pl-10 h-10 bg-background/50 border-border/50 focus:border-[#ec003f]/50 focus:ring-[#ec003f]/20 transition-all duration-200"
                  />
                </div>

          {/* Grid de Filtros */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-3 items-end">
            {/* Categoria */}
            <div className="space-y-1">
              <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <Filter className="h-3 w-3" />
                Categoria
              </label>
              <Select
                value={filters.categoria}
                onValueChange={(value) => setFilters(prev => ({ ...prev, categoria: value }))}
              >
                <SelectTrigger className="h-9 text-sm bg-white border-border/50 hover:border-[#ec003f]/50 transition-colors">
                  <SelectValue placeholder="Todas" />
                </SelectTrigger>
                <SelectContent>
                  {categorias.map(categoria => (
                    <SelectItem key={categoria} value={categoria}>
                      {categoria}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* País */}
            <div className="space-y-1">
              <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                País
              </label>
              <Select
                value={filters.pais}
                onValueChange={(value) => setFilters(prev => ({ ...prev, pais: value }))}
              >
                <SelectTrigger className="h-9 text-sm bg-white border-border/50 hover:border-[#ec003f]/50 transition-colors">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  {paises.map(pais => (
                    <SelectItem key={pais} value={pais}>
                      {pais}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Gênero */}
            <div className="space-y-1">
              <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <User className="h-3 w-3" />
                Gênero
              </label>
              <Select
                value={filters.genero}
                onValueChange={(value) => setFilters(prev => ({ ...prev, genero: value }))}
              >
                <SelectTrigger className="h-9 text-sm bg-white border-border/50 hover:border-[#ec003f]/50 transition-colors">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  {generos.map(genero => (
                    <SelectItem key={genero} value={genero}>
                      {genero}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Verificado */}
            <div className="space-y-1">
              <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <CheckSquare className="h-3 w-3" />
                Verificado
              </label>
              <Select
                value={filters.verificado === null ? 'todos' : filters.verificado.toString()}
                onValueChange={(value) => {
                  if (value === 'todos') {
                    setFilters(prev => ({ ...prev, verificado: null }));
                  } else {
                    setFilters(prev => ({ ...prev, verificado: value === 'true' }));
                  }
                }}
              >
                <SelectTrigger className="h-9 text-sm bg-white border-border/50 hover:border-[#ec003f]/50 transition-colors">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  <SelectItem value="true">Verificados</SelectItem>
                  <SelectItem value="false">Não Verificados</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Trader */}
            <div className="space-y-1">
              <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                Trader
              </label>
              <Select
                value={filters.divulgaTrader === null ? 'todos' : filters.divulgaTrader.toString()}
                onValueChange={(value) => {
                  if (value === 'todos') {
                    setFilters(prev => ({ ...prev, divulgaTrader: null }));
                  } else {
                    setFilters(prev => ({ ...prev, divulgaTrader: value === 'true' }));
                  }
                }}
              >
                <SelectTrigger className="h-9 text-sm bg-white border-border/50 hover:border-[#ec003f]/50 transition-colors">
                  <SelectValue placeholder="Todos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todos">Todos</SelectItem>
                  <SelectItem value="true">Divulga Trader</SelectItem>
                  <SelectItem value="false">Não Divulga</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Faixa de Seguidores */}
            <div className="space-y-1 col-span-2">
              <label className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                <Users className="h-3 w-3" />
                Seguidores
              </label>
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  placeholder="Min"
                  value={filters.minSeguidores || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, minSeguidores: parseInt(e.target.value) || 0 }))}
                  className="h-9 text-sm bg-background/50 border-border/50 focus:border-[#ec003f]/50 focus:ring-[#ec003f]/20 transition-all duration-200"
                />
                <span className="text-muted-foreground text-sm font-medium">até</span>
                <Input
                  type="number"
                  placeholder="Max"
                  value={filters.maxSeguidores === 1000000 ? '' : filters.maxSeguidores}
                  onChange={(e) => setFilters(prev => ({ ...prev, maxSeguidores: parseInt(e.target.value) || 1000000 }))}
                  className="h-9 text-sm bg-background/50 border-border/50 focus:border-[#ec003f]/50 focus:ring-[#ec003f]/20 transition-all duration-200"
                />
              </div>
            </div>

            {/* Botão Limpar Filtros */}
            <div className="space-y-1">
              <label className="text-xs font-medium text-transparent">Ações</label>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFilters({
                  busca: '',
                  pais: 'Todos',
                  categoria: 'Todas',
                  genero: 'Todos',
                  verificado: null,
                  divulgaTrader: null,
                  minSeguidores: 0,
                  maxSeguidores: 1000000
                })}
                className="h-9 w-full text-sm hover:bg-destructive/10 hover:text-destructive hover:border-destructive/50 transition-all duration-200"
              >
                <X className="h-4 w-4 mr-1" />
                Limpar
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Layout Principal: Tabela */}
      <div className="flex gap-6 h-[calc(100vh-350px)]">
        {/* Tabela de Influenciadores */}
        <div className={showDetailsPanel ? "flex-1" : "w-full"}>
          <Card className="h-full">
            <CardHeader>
              <div className="flex flex-col gap-4">
                {/* Título */}
                <CardTitle className="flex items-center gap-2">
                  {activeTab === 'all' ? (
                    <>
                      <Users className="h-5 w-5" />
                      Todos os Influenciadores
                    </>
                  ) : (
                    <>
                      <MessageCircle className="h-5 w-5" />
                      Influenciadores Enviados para {brandName || 'a Marca'}
                    </>
                  )}
                </CardTitle>
                
                {/* Controles Principais */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex items-center gap-3">
                    {/* Tabs para alternar entre todas, enviadas e propostas */}
                    <div className="flex items-center gap-3">
                      <Button
                        variant={activeTab === 'all' ? 'default' : 'outline'}
                        onClick={() => setActiveTab('all')}
                        className="flex bg-[#008eff] text-white items-center gap-2"
                      >
                        <Users className="h-4 w-4" />
                        Todos os Influenciadores ({filteredInfluencers.length})
                      </Button>
                      <Button
                        variant={activeTab === 'sent' ? 'default' : 'outline'}
                        onClick={() => setActiveTab('sent')}
                        className="flex  dark:text-white items-center gap-2"
                      >
                        <MessageCircle className="h-4 w-4" />
                        Influenciadores Enviados ({brandInfluencers.length})
                      </Button>
                    </div>

                    {/* Controles de Visualização e Seleção */}
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={toggleSelectionMode}
                        className={cn(
                          "transition-all duration-200",
                          selectionMode 
                            ? "bg-[#ec003f] hover:bg-[#ec003f]/90 text-white border-[#ec003f]" 
                            : "hover:bg-accent"
                        )}
                      >
                        {selectionMode ? (
                          <>
                            <CheckSquare className="h-4 w-4 mr-2" />
                            Sair da Seleção
                          </>
                        ) : (
                          <>
                            <Square className="h-4 w-4 mr-2" />
                            Modo Seleção
                          </>
                        )}
                      </Button>

                      {/* Toggle de Visualização */}
                      <div className="flex gap-0 bg-white rounded-lg p-1 border">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setViewMode('list')}
                          className={cn(
                            "h-8 px-3 rounded-md transition-all duration-200",
                            viewMode === 'list' 
                              ? "bg-[#ec003f] hover:bg-[#ec003f]/90 text-white shadow-sm" 
                              : "hover:bg-background/80"
                          )}
                        >
                          <BarChart3 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setViewMode('card')}
                          className={cn(
                            "h-8 px-3 rounded-md transition-all duration-200",
                            viewMode === 'card' 
                              ? "bg-[#ec003f] hover:bg-[#ec003f]/90 text-white shadow-sm" 
                              : "hover:bg-background/80"
                          )}
                        >
                          <User className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Badge de Seleção e Botões de Ação */}
                  {selectionMode && (
                    <div className="flex items-center gap-3">
                      <Badge 
                        variant="secondary" 
                        className="bg-[#ec003f]/10 text-[#ec003f] border-[#ec003f]/20 animate-in slide-in-from-right-5 duration-300"
                      >
                        {selectedInfluencers.length} selecionado(s)
                      </Badge>
                      
                      {/* Botão de Criar Grupo */}
                      <Button
                        variant="default"
                        size="sm"
                        className="bg-[#ec003f] hover:bg-[#ec003f]/90 text-white transition-all duration-200 hover:scale-105 shadow-md"
                        onClick={() => {
                          if (selectedInfluencers.length === 0) {
                            toast({
                              title: "Erro",
                              description: "Selecione pelo menos um influenciador para criar um grupo",
                              variant: "destructive"
                            });
                            return;
                          }
                          setGroupInfluencers(selectedInfluencers);
                          setShowGroupDialog(true);
                        }}
                        disabled={selectedInfluencers.length === 0}
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Criar Grupo ({selectedInfluencers.length})
                      </Button>
                      
                      {/* Botão de Criar Proposta */}
                      <Button
                        variant="default"
                        size="sm"
                        className="bg-[#9810fa] hover:bg-[#9810fa]/90 text-white transition-all duration-200 hover:scale-105 shadow-md"
                        onClick={() => {
                          if (selectedInfluencers.length === 0) {
                            toast({
                              title: "Erro",
                              description: "Selecione pelo menos um influenciador para criar uma proposta",
                              variant: "destructive"
                            });
                            return;
                          }
                          setShowProposalDialog(true);
                        }}
                        disabled={selectedInfluencers.length === 0}
                      >
                        <Send className="h-4 w-4 mr-2" />
                        Criar Proposta ({selectedInfluencers.length})
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="h-[calc(100%-80px)] overflow-y-auto">
              {viewMode === 'list' ? (
                <DataTable
                  columns={influencerColumns}
                  data={activeTab === 'all' ? filteredInfluencers : brandInfluencers.map(bi => bi.influencerData)}
                  enableRowSelection={false}
                  enableFiltering={false}
                  expandedRows={expandedRows}
                  renderExpandedRow={(influencer) => <ExpandedRowContent influencer={influencer} />}
                  renderExpandButton={renderExpandButton}
                  selectionMode={selectionMode}
                  selectedRows={selectedInfluencers}
                  onRowSelect={handleSelectInfluencer}
                  onRowClick={(row) => {
                    if (selectionMode) {
                      // No modo de seleção, clicar em qualquer lugar da linha seleciona/desseleciona
                      if (row?.id) {
                        if (selectedInfluencers.includes(row.id)) {
                          setSelectedInfluencers(selectedInfluencers.filter(id => id !== row.id));
                        } else {
                          setSelectedInfluencers([...selectedInfluencers, row.id]);
                        }
                      }
                    } else {
                      // Se clicar no mesmo influenciador que já está selecionado, fecha o painel
                      if (selectedInfluencer?.id === row?.id && showDetailsPanel) {
                        setShowDetailsPanel(false);
                        setSelectedInfluencer(null);
                      } else {
                        setSelectedInfluencer(row);
                        setShowDetailsPanel(true);
                      }
                    }
                  }}
                />
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {(activeTab === 'all' ? filteredInfluencers : brandInfluencers.map(bi => bi.influencerData)).map((influencer) => {
                    const isSelected = influencer?.id ? selectedInfluencers.includes(influencer.id) : false;
                    
                    return (
                      <div key={influencer?.id || Math.random()}>
                        <Card 
                          className={cn(
                            "transition-all duration-200 overflow-hidden relative backdrop-blur-md cursor-pointer",
                            selectionMode 
                              ? (isSelected ? 'bg-white/10 dark:bg-black/10 border-[#ec003f]' : 'bg-white/5 dark:bg-black/5 border border-gray-200 dark:border-white/5')
                              : (isSelected ? 'group border border-purple-500/30' : 'bg-white/5 dark:bg-black/5 border border-gray-200 dark:border-white/5')
                          )}
                          style={{
                            backdropFilter: 'blur(2px)',
                            WebkitBackdropFilter: 'blur(2px)'
                          }}
                        >
                        {/* Controles do card */}
                        <div className="absolute top-3 right-3 z-10 flex gap-2">
                          {/* Checkbox de seleção (visível apenas no modo de seleção) */}
                          {selectionMode && (
                            <div 
                              onClick={(e) => {
                                e.stopPropagation();
                                if (influencer?.id) {
                                  if (selectedInfluencers.includes(influencer.id)) {
                                    setSelectedInfluencers(selectedInfluencers.filter(id => id !== influencer.id));
                                  } else {
                                    setSelectedInfluencers([...selectedInfluencers, influencer.id]);
                                  }
                                }
                              }}
                            >
                              {isSelected ? (
                                <CheckSquare className="h-5 w-5 text-[#ec003f]" />
                              ) : (
                                <Square className="h-5 w-5 text-muted-foreground" />
                              )}
                            </div>
                          )}
                          
                          {/* Botão de expansão (sempre visível) */}
                          {!selectionMode && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (influencer?.id) {
                                  toggleRowExpansion(influencer.id);
                                }
                              }}
                              className="h-8 w-8 p-0 bg-white/80 hover:bg-white/90 text-gray-700"
                            >
                              {influencer?.id && expandedRows.includes(influencer.id) ? (
                                <X className="h-4 w-4" />
                              ) : (
                                <BarChart3 className="h-4 w-4" />
                              )}
                            </Button>
                          )}
                          
                          {/* Botão de detalhes (sempre visível) */}
                          {!selectionMode && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                // Se clicar no mesmo influenciador que já está selecionado, fecha o painel
                                if (selectedInfluencer?.id === influencer?.id && showDetailsPanel) {
                                  setShowDetailsPanel(false);
                                  setSelectedInfluencer(null);
                                } else {
                                  setSelectedInfluencer(influencer);
                                  setShowDetailsPanel(true);
                                }
                              }}
                              className="h-8 w-8 p-0 bg-white/80 hover:bg-white/90 text-gray-700"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          )}
                        </div>

                        {/* Conteúdo principal do card */}
                        <div className="p-4 flex flex-col">
                          {/* Header com avatar e informações */}
                          <div className="flex flex-col items-center w-full">
                            {/* Avatar e informações em coluna centralizada */}
                            <div className="flex flex-col items-center gap-3 w-full">
                              {/* Avatar Otimizado */}
                              <div className="relative">
                                <OptimizedAvatar
                                  src={influencer?.avatar}
                                  alt={influencer?.nome || 'Influencer'}
                                  size="xl"
                                  loading="lazy"
                                  fallbackText={influencer?.nome}
                                  className="border-2 border-foreground/5"
                                />
                                {influencer?.verified && (
                                  <div className="absolute -bottom-[-5px] -right-[-3px] bg-transparent text-white rounded-full h-5 w-5 flex items-center justify-center">
                                    <svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 156.61 189.98" width="24" height="24">
                                      <defs>
                                        <linearGradient id="Gradiente_sem_nome_147" data-name="Gradiente sem nome 147" x1="52.12" y1="184.26" x2="115.13" y2="9.61" gradientUnits="userSpaceOnUse">
                                          <stop offset="0" stopColor="#ec003f"/>
                                          <stop offset="1" stopColor="#ec003f"/>
                                        </linearGradient>
                                      </defs>
                                      <g id="Camada_1-2" data-name="Camada 1">
                                        <path className="cls-1" fill="url(#Gradiente_sem_nome_147)" d="m155.35,97.66h0c-.82-4.58-2.05-9.01-3.63-13.28-5.77-15.52-16.32-28.72-29.87-37.79,1.72,4.96,2.66,10.29,2.66,15.84,0,4.5-.61,8.86-1.77,12.99-12.01-8.56-19.95-22.48-20.31-38.27-.01-.39-.02-.78-.02-1.16,0-4.5.61-8.86,1.77-12.99,1.02-3.68,2.46-7.18,4.28-10.44,2.62-4.73,6.01-8.97,10-12.56-9.86.78-19.21,3.38-27.71,7.47-3.97,1.91-7.75,4.15-11.32,6.68-8.21,5.82-15.25,13.19-20.7,21.68-7.82,12.18-12.35,26.68-12.35,42.22,0,3.88.28,7.68.83,11.41.71,4.87,1.87,9.59,3.43,14.12-3.55-2.2-6.8-4.85-9.67-7.87-8.22-8.67-13.26-20.39-13.26-33.29,0-4.04.5-7.96,1.43-11.7C14.54,62.52,4.26,79.45,1.06,98.78c-.7,4.2-1.06,8.51-1.06,12.9,0,43.25,35.06,78.3,78.3,78.3,27.69,0,52.03-14.38,65.95-36.08,7.82-12.19,12.35-26.68,12.35-42.23,0-4.78-.43-9.47-1.25-14.02Zm-77.15,77.38c-17.82,0-33.39-9.63-41.79-23.98,12.07,7.61,26.37,12.01,41.69,12.01,12.16,0,23.68-2.77,33.94-7.72,2.79-1.35,5.48-2.85,8.07-4.49-1.03,1.78-2.17,3.48-3.41,5.11-8.84,11.59-22.79,19.07-38.5,19.07Z"/>
                                      </g>
                                    </svg>
                                  </div>
                                )}
                              </div>

                              {/* Informações principais */}
                              <div className="flex flex-col justify-center items-center mt-3">
                                {/* Nome do influenciador */}
                                <h3 className="font-medium text-base">{influencer?.nome || 'Nome não disponível'}</h3>
                                
                                {/* Localização */}
                                <div className="flex items-center justify-center text-sm text-foreground/60 dark:text-gray-400 mt-1">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" className="mr-1" viewBox="0 0 16 16">
                                    <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10m0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6" />
                                  </svg>
                                  {influencer?.cidade || 'Cidade não informada'}, {influencer?.pais || 'País não informado'}
                                </div>
                                
                                {/* Categoria */}
                                <div className="flex flex-wrap justify-center gap-1.5 mt-2 mb-2 w-full">
                                  <Badge 
                                    variant="outline" 
                                    className="bg-[#ec003f] text-white dark:text-white border-gray-850 text-[10px] py-0 px-2 h-5"
                                  >
                                    <div className="w-1.5 h-1.5 bg-[#9810fa] rounded-full mr-1"></div>
                                    {influencer?.categoria || 'Categoria não informada'}
                                  </Badge>
                                </div>
                                
                                {/* Redes sociais */}
                                {(() => {
                                  const availableNetworks = [];
                                  
                                  // Instagram
                                  if (influencer.redesSociais?.instagram) {
                                    availableNetworks.push({
                                      name: 'instagram',
                                      icon: (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                          <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                                        </svg>
                                      ),
                                      followers: formatNumber(influencer.redesSociais.instagram.seguidores),
                                      label: 'SEGUIDORES'
                                    });
                                  }
                                  
                                  // YouTube
                                  if (influencer.redesSociais?.youtube) {
                                    availableNetworks.push({
                                      name: 'youtube',
                                      icon: (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                          <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
                                        </svg>
                                      ),
                                      followers: formatNumber(influencer.redesSociais.youtube.seguidores),
                                      label: 'SEGUIDORES'
                                    });
                                  }
                                  
                                  // TikTok
                                  if (influencer.redesSociais?.tiktok) {
                                    availableNetworks.push({
                                      name: 'tiktok',
                                      icon: (
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 448 512">
                                          <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
                                        </svg>
                                      ),
                                      followers: formatNumber(influencer.redesSociais.tiktok.seguidores),
                                      label: 'SEGUIDORES'
                                    });
                                  }
                                  
                                  const networkCount = availableNetworks.length;
                                  let gridCols = 'grid-cols-1';
                                  
                                  if (networkCount === 2) gridCols = 'grid-cols-2';
                                  else if (networkCount === 3) gridCols = 'grid-cols-3';
                                  else if (networkCount >= 4) gridCols = 'grid-cols-4';
                                  
                                  return availableNetworks.length > 0 ? (
                                    <div className={`grid ${gridCols} gap-4 mt-3 w-full`}>
                                      {availableNetworks.map((network) => (
                                        <div 
                                          key={network.name}
                                          className="flex flex-col items-center"
                                        >
                                          {network.icon}
                                          <p className="text-base font-medium mt-1">{network.followers}</p>
                                          <p className="text-xs font-semibold text-gray-500">{network.label}</p>
                                        </div>
                                      ))}
                                    </div>
                                  ) : null;
                                })()}
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        {/* Seção expandida com dados demográficos */}
                        {influencer?.id && expandedRows.includes(influencer.id) && (
                          <ExpandedRowContent influencer={influencer} />
                        )}
                      </Card>
                    </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Dialog para Criar Grupo */}
      <CreateGroupDialog
        open={showGroupDialog}
        onOpenChange={setShowGroupDialog}
        selectedInfluencers={groupInfluencers}
        influencers={influencers}
        groupName={groupName}
        setGroupName={setGroupName}
        groupDescription={groupDescription}
        setGroupDescription={setGroupDescription}
        onCreateGroup={handleCreateGroup}
        loading={groupLoading}
      />
    </div>
  );
}

// Componente principal com Suspense boundary robusto
export default function MarcasDashboard() {
  return (
    <Protect role="org:admin">
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen bg-background">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-[#ec003f]" />
            <p className="text-muted-foreground">Carregando dashboard de marcas...</p>
          </div>
        </div>
      }>
        <MarcasDashboardContent />
      </Suspense>
    </Protect>
  );
}