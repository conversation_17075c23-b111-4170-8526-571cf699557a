{"common": {"loading": "Loading...", "save": "Save", "new": "New", "edit": "Edit", "counter": "Counter", "cancel": "Cancel", "delete": "Delete", "add": "Add", "add_influencer": "Add Influencer", "search": "Search", "filter": "Filter", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "all": "All", "none": "None", "responsible": "Responsible", "agency": "Agency", "capturer": "Capturer", "last_30_days": "Last 30 days", "found": "Found", "exclusive": "Exclusive", "cancel_selection": "Cancel Selection", "select_multiple": "Select Multiple", "view_notes": "View Notes", "filter_by_brand": "Filter by Brand", "more": "More", "less": "Less", "view": "View", "download": "Download", "upload": "Upload", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "refresh": "Refresh", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "report": "Report", "export": "Export", "import": "Import", "print": "Print", "share": "Share", "send": "Send", "receive": "Receive", "create": "Create", "update": "Update", "remove": "Remove", "duplicate": "Duplicate", "move": "Move", "copy_link": "Copy link", "open": "Open", "close_modal": "Close modal", "expand": "Expand", "collapse": "Collapse", "minimize": "Minimize", "maximize": "Maximize", "fullscreen": "Fullscreen", "exit_fullscreen": "Exit fullscreen", "user": "User"}, "navigation": {"dashboard": "Dashboard", "influencers": "Influencers", "creators": "Creators", "proposals": "Proposals", "campaigns": "Campaigns", "brands": "Brands", "negotiations": "Negotiations", "budget": "Budget", "analytics": "Analytics", "reports": "Reports", "notifications": "Notifications", "settings": "Settings", "help": "Help", "profile": "Profile", "logout": "Logout", "all_proposals": "All Proposals", "lists": "Lists", "backlog": "Backlog"}, "sidebar": {"expand": "Expand sidebar", "collapse": "Collapse sidebar", "workspace_switcher": "Switch workspace", "theme_toggle": "Toggle theme", "language_toggle": "Toggle language", "light_mode": "Light Mode", "dark_mode": "Dark Mode", "portuguese": "Português", "english": "English", "spanish": "Español"}, "auth": {"sign_in": "Sign In", "sign_up": "Sign Up", "sign_out": "Sign Out", "welcome_back": "Welcome back!", "create_account": "Create your account", "forgot_password": "Forgot your password?", "remember_me": "Remember me", "email": "Email", "password": "Password", "confirm_password": "Confirm password", "first_name": "First name", "last_name": "Last name", "full_name": "Full name", "username": "Username", "phone": "Phone", "company": "Company", "role": "Role", "terms_and_conditions": "Terms and Conditions", "privacy_policy": "Privacy Policy", "agree_to_terms": "I agree to the terms", "already_have_account": "Already have an account?", "dont_have_account": "Don't have an account?", "invalid_credentials": "Invalid credentials", "account_created": "Account created successfully", "password_reset_sent": "Password reset email sent", "password_updated": "Password updated successfully"}, "forms": {"required_field": "Required field", "invalid_email": "Invalid email", "password_too_short": "Password too short", "passwords_dont_match": "Passwords don't match", "invalid_phone": "Invalid phone", "invalid_url": "Invalid URL", "invalid_date": "Invalid date", "invalid_number": "Invalid number", "min_length": "Minimum {count} characters", "max_length": "Maximum {count} characters", "min_value": "Minimum value: {value}", "max_value": "Maximum value: {value}", "select_option": "Select an option", "upload_file": "Upload file", "drag_drop_files": "Drag and drop files here", "file_too_large": "File too large", "invalid_file_type": "Invalid file type", "max_files": "Maximum {count} files"}, "status": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "draft": "Draft", "published": "Published", "archived": "Archived", "completed": "Completed", "in_progress": "In Progress", "cancelled": "Cancelled", "expired": "Expired", "scheduled": "Scheduled", "paused": "Paused", "running": "Running", "stopped": "Stopped", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "messages": {"success": "Operation completed successfully", "error": "An error occurred", "warning": "Warning", "info": "Information", "confirm_delete": "Are you sure you want to delete?", "confirm_action": "Are you sure you want to continue?", "unsaved_changes": "You have unsaved changes", "no_data": "No data found", "loading_data": "Loading data...", "connection_error": "Connection error", "permission_denied": "Permission denied", "not_found": "Not found", "server_error": "Server error", "validation_error": "Validation error", "timeout_error": "Timeout error", "network_error": "Network error", "file_uploaded": "File uploaded successfully", "file_deleted": "File deleted successfully", "changes_saved": "Changes saved", "item_created": "Item created successfully", "item_updated": "Item updated successfully", "item_deleted": "Item deleted successfully"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "this_week": "This week", "last_week": "Last week", "next_week": "Next week", "this_month": "This month", "last_month": "Last month", "next_month": "Next month", "this_year": "This year", "last_year": "Last year", "next_year": "Next year", "minutes_ago": "{count} minutes ago", "hours_ago": "{count} hours ago", "days_ago": "{count} days ago", "weeks_ago": "{count} weeks ago", "months_ago": "{count} months ago", "years_ago": "{count} years ago"}, "INFLUENCERS": {"TITLE": "Influencers"}, "influencers": {"title": "Influencers", "add_influencer": "Add Influencer", "edit_influencer": "Edit Influencer", "delete_influencer": "Delete Influencer", "influencer_details": "Influencer Details", "social_media": "Social Media", "followers": "Followers", "engagement": "Engagement", "category": "Category", "location": "Location", "contact": "Contact", "bio": "Bio", "rates": "Rates", "portfolio": "Portfolio", "statistics": "Statistics", "performance": "Performance", "campaigns_history": "Campaigns History", "welcome_message": "Hello, {name}!", "loading_budgets": "Loading budgets...", "loading_budgets_description": "Fetching latest budgets and counter proposals", "no_budgets": "No budgets found", "budget_details": "Budget Details", "counter_proposals": "Counter Proposals", "accept_budget": "Accept Budget", "reject_budget": "Reject Budget", "create_counter_proposal": "Create Counter Proposal", "budget_accepted": "Budget accepted successfully", "budget_rejected": "Budget rejected", "counter_proposal_created": "Counter proposal created successfully", "view_profile": "View Profile", "edit_profile": "Edit Profile", "delete_profile": "Delete Profile", "duplicate_profile": "Duplicate Profile", "profile_actions": "Profile Actions", "filter_by_brand": "Filter by Brand", "filter_by_location": "Filter by Location", "filter_by_followers": "Filter by Followers", "filter_by_rating": "Filter by Rating", "verified_only": "Verified Only", "available_only": "Available Only", "grid_view": "Grid View", "list_view": "List View", "selection_mode": "Selection Mode", "bulk_actions": "Bulk Actions", "select_all": "Select All", "deselect_all": "Deselect All", "selected_count": "{count} selected", "no_influencers_found": "No influencers found", "loading_influencers": "Loading influencers...", "refresh_data": "Refresh Data", "export_data": "Export Data", "import_data": "Import Data", "location_not_informed": "Location not informed", "not_informed": "Not informed", "attention": "Attention!", "fill_required_fields": "Please fill in all required fields.", "select_valid_brand": "Please select a valid brand or create a proposal with a specific brand.", "select_influencer_first": "Select an influencer first.", "click_to_edit_budget": "Click to edit budget", "edit_budget": "Edit budget", "no_documents_available": "No documents available", "select_influencer": "Select an influencer", "audience_interests": "Audience Interests", "views": "Views", "tiktok_videos": "TikTok Videos", "service_details": "Service details...", "popular_locations": ["New York", "Los Angeles", "Chicago", "Miami", "San Francisco"], "table_headers": {"name": "NAME", "category": "CATEGORY", "location": "LOCATION", "instagram": "IG", "youtube": "YT", "tiktok": "TT", "value": "VALUE"}, "selected_via_url": "Selected via URL", "search_placeholder": "Search influencer...", "all_loaded": "All influencers loaded ({count} total)", "no_active_campaigns": "{name} is not participating in any active campaigns", "no_active_campaigns_generic": "This influencer is not participating in any active campaigns", "social_screenshots": "Social Media Screenshots", "more_screenshots": "+{count} more screenshot{plural}", "show_less": "Show less", "networks_count_singular": "1 network", "networks_count_plural": "{count} networks", "no_interests_available": "No interests available", "engagement_rate_by_network": "Engagement Rate by Social Network", "how_engagement_rate_calculated": "How Engagement Rate is calculated:", "overall_engagement_rate": "Overall Engagement Rate:", "based_on_networks": "Based on {count} social network{plural}", "badges": {"approved": "Approved", "rejected": "Rejected", "discarded": "Discarded", "pending": "Pending", "accepted": "Accepted", "declined": "Declined", "snapshot": "SNAPSHOT", "main_network": "Main Social Network", "verified": "Verified", "available": "Available", "unavailable": "Unavailable"}, "card": {"no_name": "Name not available", "no_category": "No category", "loading_category": "Loading...", "show_more": "Show more", "show_less": "Show less", "more_networks": "more networks", "no_location": "Location not provided", "followers_label": "followers", "subscribers_label": "subscribers", "add_to_campaign": "Add to campaign", "select_campaign": "Select campaign", "campaign_added_success": "Influencer successfully added to campaign!", "campaign_add_error": "Error adding influencer to campaign", "bookmark": "Bookmark", "duplicate": "Duplicate", "edit": "Edit", "delete": "Delete"}}, "proposals": {"title": "Proposals", "create_proposal": "Create Proposal", "edit_proposal": "Edit Proposal", "delete_proposal": "Delete Proposal", "proposal_details": "Proposal Details", "budget": "Budget", "deadline": "Deadline", "deliverables": "Deliverables", "requirements": "Requirements", "terms": "Terms", "status": "Status", "client": "Client", "brand": "Brand", "campaign": "Campaign", "description": "Description", "objectives": "Objectives", "target_audience": "Target Audience", "timeline": "Timeline", "payment_terms": "Payment Terms", "revision_rounds": "Revision Rounds", "no_proposals_found": "No proposals found", "create_to_manage": "Create proposals to manage relationships", "status_label": "Proposal Status:", "rejected_status": "Rejected"}, "panels": {"relationship": {"title": "Relationship", "proposal_selector": "Proposal Selector", "budget_section": "Service Budget", "service_packages": "Service Packages", "campaigns": "Campaigns", "budget_management": "Budget Management", "view_all_proposals": "View all proposals", "budget_details": "Budget Details", "documents": "Documents", "counter_proposals": "Counter Proposals", "no_influencer_selected": "Select an influencer to manage budgets", "upload_document": "Upload Document", "create_budget": "Create Budget", "edit_budget": "Edit Budget", "new_budget": "New Budget", "initial_value": "initial value", "accepted_value": "accepted value", "in_negotiation": "in negotiation", "accepted": "Accepted", "rejected": "Rejected", "pending": "Pending", "edit": "Edit", "counter_proposal": "Counter Proposal", "no_budgets_found": "No budgets found", "approved_total": "{total} approved", "accept_initial_value": "Accept initial value", "reject_initial_value": "Reject initial value", "create_specific_proposal": "Create specific budgets for this proposal", "accept_counter_proposal": "Accept counter proposal", "reject_counter_proposal": "Reject counter proposal", "make_counter_proposal": "Make counter proposal", "create_budgets_influencer": "Create budgets for this influencer", "notes": "Notes", "lists": "Lists", "add_to_list": "Add", "no_lists_yet": "This influencer has not been saved to any list yet.", "manage_notes": "Manage notes"}, "contact": {"title": "Contact Profile", "profile_header": "Profile", "basic_info": "Basic Information", "contact_info": "Contact Information", "demographics": "Demographics", "social_platforms": "Social Platforms", "analytics": "Analytics", "engagement_metrics": "Engagement Metrics", "audience_insights": "Audience Insights", "select_influencer": "Select an influencer", "click_card_details": "Click on a card to see details", "years_old": "{age} years old", "male": "Male", "female": "Female", "whatsapp": "WhatsApp", "email": "Email", "instagram": "Instagram", "youtube": "YouTube", "tiktok": "TikTok", "followers": "Followers", "following": "Following", "posts": "Posts", "avg_likes": "Average Likes", "avg_comments": "Average Comments", "engagement_rate": "Engagement Rate", "views": "Views", "subscribers": "Subscribers", "videos": "Videos", "likes": "<PERSON>s", "comments": "Comments", "shares": "Shares", "created_at": "Created at", "location_not_informed": "Location not informed", "not_informed": "Not informed", "online": "Online", "gender_distribution": "Gender Distribution", "age_distribution": "Age Distribution", "location_distribution": "Location Distribution", "interests": "Audience Interests", "no_demographic_data": "No demographic data available", "audience_demographics": "Audience Demographics", "total_views": "Total Views", "total_followers": "Total Followers", "views_by_content_type": "Views by Content Type", "based_on_content_types": "Based on {count} content type{plural}"}}, "dashboard": {"select_influencer_budgets": "Select an influencer to view budgets", "campaigns": "Campaigns", "select_influencer_campaigns": "Select an influencer to view their active campaigns and participation history", "documents": "Documents"}, "audience": {"age_ranges": "Audience Age Ranges", "no_age_data": "No age range data available", "countries": "Audience Countries", "no_country_data": "No country data available", "cities": "Audience Cities", "no_city_data": "No city data available"}}