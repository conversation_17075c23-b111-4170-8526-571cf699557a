'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Protect } from '@clerk/nextjs';
import { useUser, useAuth } from '@clerk/nextjs';
import { useLists } from '@/hooks/use-lists';
import { useBrands } from '@/hooks/use-brands';
import { ColumnDef, ColumnOrderState } from "@tanstack/react-table";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useDataTable } from '@/hooks/use-data-table';
import { createListColumns } from '@/lib/table-columns/lists';
import { Lista, CriarListaData } from '@/types/list';

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DataTable } from '@/components/ui/data-table';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { List, Plus, Search, Filter, MoreHorizontal, Edit, Trash2, Shield, Users, Tag, Calendar, HelpCircle, X, Building2, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Loader, useGlobalLoader } from '@/components/ui/loader';

interface PageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function CreatorListsPage({ params }: PageProps) {
  const { user: currentUser, isLoaded: userLoaded } = useUser();
  const { getToken } = useAuth();
  const { isLoading: globalLoading, showLoader, hideLoader } = useGlobalLoader();
  const router = useRouter();
  const [userId, setUserId] = useState<string | null>(null);

  useEffect(() => {
    const resolveParams = async () => {
      showLoader();
      try {
        const resolvedParams = await params;
        setUserId(resolvedParams.userId);
      } catch (error) {
        console.error('Erro ao resolver parâmetros:', error);
        hideLoader();
      }
    };
    resolveParams();
  }, [params, showLoader, hideLoader]);

  const isOwnProfile = currentUser?.id === userId;
  const canAccess = isOwnProfile;

  // Hook de integração com API
  const {
    listas,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,
    pagination,
    criarLista,
    atualizarLista,
    deletarLista,
    buscarListas,
    clearError
  } = useLists({
    autoLoad: canAccess && !!currentUser
  });

  // Controlar loader global baseado nos estados de loading
  useEffect(() => {
    if (!userLoaded || isLoading || isCreating || isUpdating || isDeleting) {
      showLoader();
    } else if (currentUser && userId) {
      hideLoader();
    }
  }, [
    userLoaded, 
    isLoading, 
    isCreating, 
    isUpdating, 
    isDeleting, 
    currentUser, 
    userId,
    showLoader,
    hideLoader
  ]);
  
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [editingLista, setEditingLista] = useState<Lista | null>(null);
  const [formData, setFormData] = useState<CriarListaData>({
    nome: '',
    tipoLista: 'estática',
    tipoObjeto: 'influenciadores',
    descricao: '',
    tags: [],
    criterios: [],
    marcasAssociadas: [],
    autoImportBrands: []
  });

  // 🛑 TEMPORÁRIO: Desabilitar carregamento de marcas do usuário para evitar loop
  const brands: any[] = [];
  const brandsLoading = false;
  const brandsError = null;

  // ✅ CORREÇÃO: Carregar marcas das associações também
  const [associatedBrands, setAssociatedBrands] = useState<any[]>([]);
  const [associationsLoading, setAssociationsLoading] = useState(false);

  // Função para carregar marcas das associações
  const loadAssociatedBrands = useCallback(async () => {
    console.log('🔄 [ASSOCIATIONS_DEBUG] Iniciando carregamento de associações:', {
      currentUser: !!currentUser,
      currentUserId: currentUser?.id,
      canAccess
    });

    if (!currentUser || !canAccess) {
      console.log('❌ [ASSOCIATIONS_DEBUG] Condições não atendidas para carregar associações');
      return;
    }

    try {
      setAssociationsLoading(true);
      console.log('📡 [ASSOCIATIONS_DEBUG] Fazendo requisição para /api/brand-influencers');
      
      const response = await fetch('/api/brand-influencers', {
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': currentUser.id
        }
      });

      console.log('📡 [ASSOCIATIONS_DEBUG] Resposta recebida:', {
        status: response.status,
        ok: response.ok,
        statusText: response.statusText
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ [ASSOCIATIONS_DEBUG] Erro na resposta:', errorData);
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ [ASSOCIATIONS_DEBUG] Dados recebidos:', {
        hasAssociations: !!data.associations,
        associationsCount: data.associations?.length || 0,
        data
      });
      
      if (data.associations && Array.isArray(data.associations)) {
        // Extrair marcas únicas das associações
        const brandMap = new Map();
        data.associations.forEach((assoc: any) => {
          if (!brandMap.has(assoc.brandId)) {
            brandMap.set(assoc.brandId, {
              id: assoc.brandId,
              name: assoc.brandName,
              logo: assoc.brandLogo,
              count: 1
            });
          } else {
            const existing = brandMap.get(assoc.brandId);
            existing.count += 1;
          }
        });
        
        const brandsArray = Array.from(brandMap.values());
        console.log('✅ [ASSOCIATIONS_DEBUG] Marcas extraídas das associações:', brandsArray);
        setAssociatedBrands(brandsArray);
      } else {
        console.log('ℹ️ [ASSOCIATIONS_DEBUG] Nenhuma associação encontrada');
        setAssociatedBrands([]);
      }
    } catch (error) {
      console.error('❌ [ASSOCIATIONS_DEBUG] Erro ao carregar marcas das associações:', error);
      setAssociatedBrands([]);
    } finally {
      setAssociationsLoading(false);
      console.log('🔄 [ASSOCIATIONS_DEBUG] Carregamento de associações finalizado');
    }
  }, [currentUser, canAccess]);

  // Carregar marcas das associações
  useEffect(() => {
    loadAssociatedBrands();
  }, [loadAssociatedBrands]);

  // ✅ TEMPORÁRIO: Usar apenas marcas das associações
  const allBrands = useMemo(() => {
    return associatedBrands.map((brand: any) => ({
      id: brand.id,
      name: brand.name,
      logo: brand.logo,
      industry: brand.industry || undefined
    }));
  }, [associatedBrands]);

  const finalBrandsLoading = associationsLoading;

  // Estado para auto-importação de influenciadores
  const [autoImportInfluencers, setAutoImportInfluencers] = useState<Record<string, any[]>>({});
  const [loadingBrandInfluencers, setLoadingBrandInfluencers] = useState<Record<string, boolean>>({});

  const [searchTerm, setSearchTerm] = useState('');
  const [tipoListaFilter, setTipoListaFilter] = useState<string>('all');
  const [tipoObjetoFilter, setTipoObjetoFilter] = useState<string>('all');
  
  // Usando estrutura centralizada
  const { dataTableProps, selectedItems, handleRowSelectionChange, handleColumnOrderChange, columnOrder, resetSelection } = useDataTable<Lista>({
    enableColumnOrdering: true,
    enableRowSelection: true,
    pageSize: 10
  });

  // Manter compatibilidade com o código existente  
  const selectedListas = selectedItems;

  const filteredListas = useMemo(() => {
    return listas.filter(lista => {
      const matchesSearch = lista.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lista.descricao?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lista.criadoPorNome.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesTipoLista = tipoListaFilter === 'all' || lista.tipoLista === tipoListaFilter;
      const matchesTipoObjeto = tipoObjetoFilter === 'all' || lista.tipoObjeto === tipoObjetoFilter;
      
      return matchesSearch && matchesTipoLista && matchesTipoObjeto;
    });
  }, [listas, searchTerm, tipoListaFilter, tipoObjetoFilter]);

  const handleInputChange = (field: keyof CriarListaData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Se mudou para estática, limpar critérios
      if (field === 'tipoLista' && value === 'estática') {
        newData.criterios = [];
      }
      
      return newData;
    });
  };

  const addCriterio = () => {
    setFormData(prev => ({
      ...prev,
      criterios: [...(prev.criterios || []), { campo: '', operador: '=', valor: '' }]
    }));
  };

  const removeCriterio = (index: number) => {
    setFormData(prev => ({
      ...prev,
      criterios: prev.criterios?.filter((_, i) => i !== index) || []
    }));
  };

  const updateCriterio = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      criterios: prev.criterios?.map((criterio, i) => 
        i === index ? { ...criterio, [field]: value } : criterio
      ) || []
    }));
  };

  const getCamposDisponiveis = (tipoObjeto: string) => {
    switch (tipoObjeto) {
      case 'influenciadores':
        return [
          { value: 'seguidores', label: 'Seguidores' },
          { value: 'engajamento', label: 'Taxa de Engajamento (%)' },
          { value: 'nicho', label: 'Nicho/Categoria' },
          { value: 'localizacao', label: 'Localização' },
          { value: 'idade', label: 'Idade' }
        ];
      case 'marcas':
        return [
          { value: 'orcamento', label: 'Orçamento Mensal (R$)' },
          { value: 'setor', label: 'Setor' },
          { value: 'status', label: 'Status' },
          { value: 'funcionarios', label: 'Número de Funcionários' }
        ];
      case 'campanhas':
        return [
          { value: 'budget', label: 'Budget (R$)' },
          { value: 'status', label: 'Status' },
          { value: 'dataFim', label: 'Data de Fim' },
          { value: 'alcance', label: 'Alcance Esperado' }
        ];
      case 'conteúdo':
        return [
          { value: 'formato', label: 'Formato' },
          { value: 'tamanho', label: 'Tamanho (MB)' },
          { value: 'dataUpload', label: 'Data de Upload' },
          { value: 'engajamento', label: 'Engajamento' }
        ];
      default:
        return [];
    }
  };

  const getOperadorNome = useCallback((operador: string) => {
    switch (operador) {
      case '=': return 'igual a';
      case '>': return 'maior que';
      case '<': return 'menor que';
      case '>=': return 'maior ou igual';
      case '<=': return 'menor ou igual';
      case '!=': return 'diferente de';
      default: return operador;
    }
  }, []);

  const getCampoNome = useCallback((campo: string, tipoObjeto: string) => {
    const campos = getCamposDisponiveis(tipoObjeto);
    const encontrado = campos.find(c => c.value === campo);
    return encontrado ? encontrado.label : campo;
  }, []);

  const handleCreateLista = async () => {
    if (!formData.nome.trim()) {
      toast.error('Nome da lista é obrigatório');
      return;
    }

    if (formData.tipoLista === 'dinâmica') {
      if (!formData.criterios || formData.criterios.length === 0) {
        toast.error('Listas dinâmicas precisam de pelo menos um critério');
        return;
      }
      
      // Validar se todos os critérios estão preenchidos
      for (const criterio of formData.criterios) {
        if (!criterio.campo || !criterio.operador || !criterio.valor.trim()) {
          toast.error('Todos os critérios devem estar completamente preenchidos');
          return;
        }
      }
    }

    console.log('[FRONTEND_CREATE_LISTA] Iniciando criação com dados:', {
      nome: formData.nome,
      marcasAssociadas: formData.marcasAssociadas,
      totalMarcas: formData.marcasAssociadas?.length || 0
    });

    try {
      const dadosParaEnviar = {
        nome: formData.nome.trim(),
        tipoLista: formData.tipoLista,
        tipoObjeto: formData.tipoObjeto,
        descricao: formData.descricao?.trim(),
        tags: formData.tags || [],
        criterios: formData.criterios || [],
        marcasAssociadas: formData.marcasAssociadas || [],
        autoImportBrands: formData.autoImportBrands || []
      };

      console.log('[FRONTEND_CREATE_LISTA] Enviando dados para API:', dadosParaEnviar);

      const novaLista = await criarLista(dadosParaEnviar);

      console.log('[FRONTEND_CREATE_LISTA] Lista criada com sucesso:', {
        id: novaLista?.id,
        nome: novaLista?.nome,
        marcasAssociadas: novaLista?.marcasAssociadas?.length || 0
      });

      if (novaLista) {
        // 🚀 Auto-importar influenciadores se houver marcas selecionadas para importação
        if (formData.autoImportBrands && formData.autoImportBrands.length > 0) {
          console.log('[AUTO_IMPORT] Iniciando auto-importação para marcas:', formData.autoImportBrands);
          
          try {
            let totalImportados = 0;
            const token = await getToken();

            for (const brandId of formData.autoImportBrands) {
              const influencers = autoImportInfluencers[brandId] || [];
              
              console.log('[AUTO_IMPORT] Importando influenciadores da marca:', {
                brandId,
                totalInfluencers: influencers.length
              });

              // Adicionar cada influenciador à lista
              for (const influencer of influencers) {
                try {
                  const response = await fetch(`/api/lists/${novaLista.id}/items`, {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${token}`,
                    },
                    body: JSON.stringify({
                      listaId: novaLista.id,
                      itemId: influencer.influencerId,
                      tipoItem: 'influenciador'
                    }),
                  });

                  if (response.ok) {
                    totalImportados++;
                    console.log('[AUTO_IMPORT] Influenciador importado:', influencer.influencerName);
                  } else {
                    console.warn('[AUTO_IMPORT] Falha ao importar influenciador:', influencer.influencerName);
                  }
                } catch (error) {
                  console.error('[AUTO_IMPORT] Erro ao importar influenciador:', error);
                }
              }
            }

            console.log('[AUTO_IMPORT] Auto-importação concluída:', {
              totalImportados,
              marcas: formData.autoImportBrands.length
            });

            if (totalImportados > 0) {
              toast.success(`Lista criada com sucesso! ${totalImportados} influenciador(es) importado(s) automaticamente.`);
            } else {
              toast.success('Lista criada com sucesso!');
            }

          } catch (error) {
            console.error('[AUTO_IMPORT] Erro na auto-importação:', error);
            toast.success('Lista criada com sucesso! Houve um problema na importação automática de alguns influenciadores.');
          }
        } else {
          toast.success('Lista criada com sucesso!');
        }

        setFormData({
          nome: '',
          tipoLista: 'estática',
          tipoObjeto: 'influenciadores',
          descricao: '',
          tags: [],
          criterios: [],
          marcasAssociadas: [],
          autoImportBrands: []
        });
        setAutoImportInfluencers({});
        setLoadingBrandInfluencers({});
        setIsPanelOpen(false);
        setEditingLista(null);
      }
    } catch (error) {
      console.error('[FRONTEND_CREATE_LISTA] Erro ao criar lista:', error);
      toast.error('Erro ao criar lista. Tente novamente.');
    }
  };

  const handleEditLista = useCallback((lista: Lista) => {
    setEditingLista(lista);
    setFormData({
      nome: lista.nome,
      tipoLista: lista.tipoLista,
      tipoObjeto: lista.tipoObjeto,
      descricao: lista.descricao || '',
      tags: lista.tags || [],
      criterios: lista.criterios || [],
      marcasAssociadas: lista.marcasAssociadas?.map(m => m.id) || [],
      autoImportBrands: [] // Reset auto-import when editing
    });
    setIsPanelOpen(true);
  }, []);

  const handleUpdateLista = async () => {
    if (!editingLista || !formData.nome.trim()) {
      toast.error('Nome da lista é obrigatório');
      return;
    }

    if (formData.tipoLista === 'dinâmica') {
      if (!formData.criterios || formData.criterios.length === 0) {
        toast.error('Listas dinâmicas precisam de pelo menos um critério');
        return;
      }
      
      // Validar se todos os critérios estão preenchidos
      for (const criterio of formData.criterios) {
        if (!criterio.campo || !criterio.operador || !criterio.valor.trim()) {
          toast.error('Todos os critérios devem estar completamente preenchidos');
          return;
        }
      }
    }

    try {
      const listaAtualizada = await atualizarLista(editingLista.id, {
        nome: formData.nome.trim(),
        descricao: formData.descricao?.trim(),
        tags: formData.tags || [],
        criterios: formData.criterios || [],
        marcasAssociadas: formData.marcasAssociadas || []
      });

      if (listaAtualizada) {
        setEditingLista(null);
        setFormData({
          nome: '',
          tipoLista: 'estática',
          tipoObjeto: 'influenciadores',
          descricao: '',
          tags: [],
          criterios: [],
          marcasAssociadas: []
        });
        setIsPanelOpen(false);
        toast.success('Lista atualizada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao atualizar lista:', error);
      toast.error('Erro ao atualizar lista. Tente novamente.');
    }
  };

  const handleDeleteLista = useCallback(async (lista: Lista) => {
    if (!confirm(`Tem certeza que deseja deletar a lista "${lista.nome}"? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      const sucesso = await deletarLista(lista.id);
      if (sucesso) {
        toast.success('Lista deletada com sucesso!');
      }
    } catch (error) {
      console.error('Erro ao deletar lista:', error);
      toast.error('Erro ao deletar lista. Tente novamente.');
    }
  }, [deletarLista]);

  // Função para lidar com seleção de marcas
  const handleMarcaToggle = (brandId: string, checked: boolean) => {
    setFormData(prev => {
      const currentMarcas = prev.marcasAssociadas || [];
      if (checked) {
        return {
          ...prev,
          marcasAssociadas: [...currentMarcas, brandId]
        };
      } else {
        // Se desmarcou a marca, também remover da auto-importação
        const currentAutoImport = prev.autoImportBrands || [];
        return {
          ...prev,
          marcasAssociadas: currentMarcas.filter(id => id !== brandId),
          autoImportBrands: currentAutoImport.filter(id => id !== brandId)
        };
      }
    });
  };

  // Função para buscar influenciadores de uma marca
  const fetchBrandInfluencers = async (brandId: string) => {
    if (!currentUser) return;

    setLoadingBrandInfluencers(prev => ({ ...prev, [brandId]: true }));

    try {
      const token = await getToken();
      const response = await fetch(`/api/brands/${brandId}/influencers`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Erro HTTP: ${response.status}`);
      }

      const data = await response.json();
      
      console.log('[AUTO_IMPORT] Influenciadores da marca carregados:', {
        brandId,
        total: data.total,
        influencers: data.data
      });

      setAutoImportInfluencers(prev => ({
        ...prev,
        [brandId]: data.data || []
      }));

    } catch (error) {
      console.error('[AUTO_IMPORT] Erro ao buscar influenciadores da marca:', error);
      toast.error('Erro ao buscar influenciadores da marca');
    } finally {
      setLoadingBrandInfluencers(prev => ({ ...prev, [brandId]: false }));
    }
  };

  // Função para lidar com auto-importação de influenciadores
  const handleAutoImportToggle = (brandId: string, checked: boolean) => {
    setFormData(prev => {
      const currentAutoImport = prev.autoImportBrands || [];
      
      if (checked) {
        // Buscar influenciadores da marca
        fetchBrandInfluencers(brandId);
        
        return {
          ...prev,
          autoImportBrands: [...currentAutoImport, brandId]
        };
      } else {
        // Remover da auto-importação e limpar influenciadores carregados
        setAutoImportInfluencers(prev => {
          const newState = { ...prev };
          delete newState[brandId];
          return newState;
        });
        
        return {
          ...prev,
          autoImportBrands: currentAutoImport.filter(id => id !== brandId)
        };
      }
    });
  };

  const resetPanel = () => {
    setEditingLista(null);
    setFormData({
      nome: '',
      tipoLista: 'estática',
      tipoObjeto: 'influenciadores',
      descricao: '',
      tags: [],
      criterios: [],
      marcasAssociadas: [],
      autoImportBrands: []
    });
    // Limpar estados de auto-importação
    setAutoImportInfluencers({});
    setLoadingBrandInfluencers({});
    setIsPanelOpen(false);
  };

  const openCreatePanel = () => {
    resetPanel();
    setIsPanelOpen(true);
  };

  const formatDate = useCallback((date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date);
  }, []);

  const getTipoListaBadgeColor = useCallback((tipo: string) => {
    switch (tipo) {
      case 'estática': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'dinâmica': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
              default: return 'bg-gray-100 text-gray-800 dark:bg-[#080210] dark:text-gray-300';
    }
  }, []);

  const getTipoObjetoIcon = useCallback((tipo: string) => {
    switch (tipo) {
      case 'influenciadores': return <Users className="h-4 w-4" />;
      case 'marcas': return <Tag className="h-4 w-4" />;
      case 'campanhas': return <Calendar className="h-4 w-4" />;
      case 'conteúdo': return <List className="h-4 w-4" />;
      default: return <List className="h-4 w-4" />;
    }
  }, []);

  // Definições das colunas usando helper centralizado
  const columns = useMemo(() => createListColumns(
    {
      onEdit: handleEditLista,
      onDelete: handleDeleteLista,
      onNameClick: (lista) => router.push(`/creators/${currentUser!.id}/lists/${lista.id}`),
    },
    {
      enableClickableNames: true,
      showCriterios: true
    }
  ), [handleEditLista, handleDeleteLista, router, currentUser]);

  if (!userLoaded || !currentUser || !canAccess) {
    return (
      <div className="p-6">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            {!userLoaded 
              ? "Carregando informações do usuário..."
              : !currentUser 
              ? "Você precisa estar logado para acessar as listas."
              : "Você só pode acessar suas próprias listas."
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Loader Global */}
      <Loader isLoading={globalLoading} showLogo={true} message="" />
      
      <div className="flex bg-muted/30 dark:bg-[#080210] h-screen overflow-hidden">
      <div className={cn("flex-1 transition-all duration-300 flex flex-col", isPanelOpen ? "mr-96" : "mr-0")} style={{ height: '100%', maxWidth: '100%' }}>
        <div className="p-6 flex-none">
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div>
                  <h1 className="text-2xl font-bold text-[#270038] dark:text-white">
                    Minhas Listas
                  </h1>
                  <p className="text-muted-foreground">
                    Gerencie suas listas de influenciadores, marcas e campanhas
                  </p>
              </div>
            </div>

              {/* Controles de Filtro */}
                <div className="flex items-center gap-4 mb-6">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Pesquisar listas..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  <Select value={tipoListaFilter} onValueChange={setTipoListaFilter}>
                    <SelectTrigger className="w-48">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Tipo de lista" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os tipos</SelectItem>
                      <SelectItem value="estática">Estática</SelectItem>
                      <SelectItem value="dinâmica">Dinâmica</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={tipoObjetoFilter} onValueChange={setTipoObjetoFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Tipo de objeto" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os objetos</SelectItem>
                      <SelectItem value="influenciadores">Influenciadores</SelectItem>
                      <SelectItem value="marcas">Marcas</SelectItem>
                      <SelectItem value="campanhas">Campanhas</SelectItem>
                      <SelectItem value="conteúdo">Conteúdo</SelectItem>
                    </SelectContent>
                  </Select>

                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {filteredListas.length} lista(s) encontrada(s)
                    </Badge>
                  </div>

                  <Button 
                    className="bg-gradient-to-r from-[#9810fa] to-[#ff0074] hover:bg-gradient-to-r hover:from-[#9810fa] hover:to-[#ff0074] text-white"
                    onClick={openCreatePanel}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Nova Lista
                  </Button>

                  {selectedListas.length > 0 && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline">
                          Ações ({selectedListas.length})
                          <MoreHorizontal className="h-4 w-4 ml-2" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={resetSelection}>
                          Desmarcar todas
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          className="text-destructive"
                          onClick={async () => {
                            if (confirm(`Deletar ${selectedListas.length} lista(s) selecionada(s)?`)) {
                              const listasToDelete = listas.filter(lista => selectedListas.includes(lista.id));
                              
                              try {
                                // Deletar todas as listas selecionadas em paralelo
                                const deletePromises = listasToDelete.map(lista => deletarLista(lista.id));
                                await Promise.all(deletePromises);
                                
                                resetSelection();
                                toast.success(`${listasToDelete.length} lista(s) deletada(s) com sucesso!`);
                              } catch (error) {
                                console.error('Erro ao deletar listas:', error);
                                toast.error('Erro ao deletar algumas listas. Tente novamente.');
                              }
                            }
                          }}
                        >
                          Deletar selecionadas
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>

              {/* Mensagem de estado quando não há listas */}
              {filteredListas.length === 0 && (
                  <Card>
                    <CardContent className="p-12 text-center">
                      <List className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                      <p className="text-muted-foreground mb-2">
                        {searchTerm || tipoListaFilter !== 'all' || tipoObjetoFilter !== 'all'
                          ? 'Nenhuma lista encontrada com os filtros aplicados.'
                          : 'Nenhuma lista criada ainda.'
                        }
                      </p>
                      {!searchTerm && tipoListaFilter === 'all' && tipoObjetoFilter === 'all' && (
                        <Button
                          variant="outline"
                          onClick={openCreatePanel}
                          className="mt-4"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Criar primeira lista
                        </Button>
                      )}
                    </CardContent>
                  </Card>
              )}
          </div>
        </div>
        
          {/* DataTable */}
        <div className="px-6 pb-6 flex-1 overflow-hidden" style={{ height: 'calc(100vh - 200px)' }}>
            {filteredListas.length > 0 && (
            <div className="w-full h-full flex flex-col">
              <div className="flex-1 overflow-auto">
                  <DataTable
                    columns={columns}
                    data={filteredListas}
                    searchKey="nome"
                    searchPlaceholder=""
                    className="w-full"
                    {...dataTableProps}
                  />
              </div>
            </div>
            )}
                  </div>
                  </div>

        {/* Sheet para criar/editar lista */}
      <Sheet open={isPanelOpen} onOpenChange={setIsPanelOpen}>
        <SheetContent className="w-96 sm:max-w-none">
          <SheetHeader>
            <SheetTitle>
              {editingLista ? 'Editar Lista' : 'Nova Lista'}
            </SheetTitle>
            <SheetDescription>
              {editingLista 
                ? 'Atualize as informações da lista.' 
                : 'Preencha as informações para criar uma nova lista.'
              }
            </SheetDescription>
          </SheetHeader>

          <div className="mt-6 space-y-6">
            <div className="space-y-2">
              <Label htmlFor="nome">Nome da Lista *</Label>
              <Input
                id="nome"
                placeholder="Ex: Top Influenciadores Fashion"
                value={formData.nome}
                onChange={(e) => handleInputChange('nome', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="tipoLista">Tipo de Lista *</Label>
              <Select value={formData.tipoLista} onValueChange={(value) => handleInputChange('tipoLista', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="estática">Estática</SelectItem>
                  <SelectItem 
                    value="dinâmica" 
                    disabled
                    className="cursor-not-allowed opacity-50"
                  >
                    Dinâmica (em breve)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tipoObjeto">Tipo de Objeto *</Label>
              <Select value={formData.tipoObjeto} onValueChange={(value) => handleInputChange('tipoObjeto', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="influenciadores">Influenciadores</SelectItem>
                  <SelectItem value="marcas">Marcas</SelectItem>
                  <SelectItem value="campanhas">Campanhas</SelectItem>
                  <SelectItem value="conteúdo">Conteúdo</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="descricao">Descrição</Label>
              <Textarea
                id="descricao"
                placeholder="Descreva o propósito desta lista..."
                value={formData.descricao}
                onChange={(e) => handleInputChange('descricao', e.target.value)}
                rows={3}
              />
            </div>

            {/* Seção de Marcas Associadas */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-[#ec003f]" />
                <Label>Marcas Associadas</Label>
                <Badge variant="secondary" className="text-xs">
                  {formData.marcasAssociadas?.length || 0} selecionada(s)
                </Badge>
              </div>
              
                {finalBrandsLoading ? (
                <div className="text-sm text-muted-foreground">
                  Carregando marcas...
                </div>
                ) : allBrands.length === 0 ? (
                <div className="text-center py-6 border-2 border-dashed rounded-lg">
                  <Building2 className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground mb-2">
                    Nenhuma marca encontrada
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Crie uma marca primeiro para associá-la às suas listas
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto">
                    {allBrands.map((brand) => {
                    const isBrandSelected = formData.marcasAssociadas?.includes(brand.id) || false;
                    const isAutoImportEnabled = formData.autoImportBrands?.includes(brand.id) || false;
                    const brandInfluencers = autoImportInfluencers[brand.id] || [];
                    const isLoadingInfluencers = loadingBrandInfluencers[brand.id] || false;

                    return (
                      <div
                        key={brand.id}
                        className="border rounded-lg p-3 space-y-2 hover:bg-muted/30 transition-colors"
                      >
                        {/* Primeira linha: Checkbox da marca + Avatar + Nome */}
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id={`brand-${brand.id}`}
                            checked={isBrandSelected}
                            onCheckedChange={(checked) => handleMarcaToggle(brand.id, checked as boolean)}
                          />
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={brand.logo} alt={brand.name} />
                            <AvatarFallback className="text-xs bg-[#ec003f] text-white">
                              {brand.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <label
                              htmlFor={`brand-${brand.id}`}
                              className="text-sm font-medium cursor-pointer block truncate"
                              title={brand.name}
                            >
                              {brand.name}
                            </label>
                            {brand.industry && (
                              <p className="text-xs text-muted-foreground truncate">
                                {brand.industry}
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Segunda linha: Checkbox de auto-importação (só aparece se marca está selecionada) */}
                        {isBrandSelected && formData.tipoObjeto === 'influenciadores' && (
                          <div className="ml-11 space-y-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id={`auto-import-${brand.id}`}
                                checked={isAutoImportEnabled}
                                onCheckedChange={(checked) => handleAutoImportToggle(brand.id, checked as boolean)}
                                disabled={isLoadingInfluencers}
                                className="h-4 w-4"
                              />
                              <label
                                htmlFor={`auto-import-${brand.id}`}
                                className="text-xs text-[#9810fa] font-medium cursor-pointer"
                              >
                                Importar todos influenciadores dessa marca?
                              </label>
                            </div>

                            {isAutoImportEnabled && brandInfluencers.length > 0 && (
                              <div className="text-xs text-green-600 bg-green-50 p-2 rounded border">
                                ✅ {brandInfluencers.length} influenciador(es) serão importados automaticamente
                              </div>
                            )}

                            {isAutoImportEnabled && !isLoadingInfluencers && brandInfluencers.length === 0 && (
                              <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded border">
                                ⚠️ Nenhum influenciador associado a esta marca
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Seção de Critérios - só aparece para listas dinâmicas */}
            {formData.tipoLista === 'dinâmica' && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Critérios de Atualização Automática *</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCriterio}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Adicionar Critério
                  </Button>
                </div>
                
                {formData.criterios && formData.criterios.length > 0 ? (
                  <div className="space-y-3">
                    {formData.criterios.map((criterio, index) => (
                      <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                        <Select
                          value={criterio.campo}
                          onValueChange={(value) => updateCriterio(index, 'campo', value)}
                        >
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Campo" />
                          </SelectTrigger>
                          <SelectContent>
                            {getCamposDisponiveis(formData.tipoObjeto).map((campo) => (
                              <SelectItem key={campo.value} value={campo.value}>
                                {campo.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Select
                          value={criterio.operador}
                          onValueChange={(value) => updateCriterio(index, 'operador', value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="=">igual a</SelectItem>
                            <SelectItem value=">">maior que</SelectItem>
                            <SelectItem value="<">menor que</SelectItem>
                            <SelectItem value=">=">maior ou igual</SelectItem>
                            <SelectItem value="<=">menor ou igual</SelectItem>
                            <SelectItem value="!=">diferente de</SelectItem>
                          </SelectContent>
                        </Select>

                        <Input
                          placeholder="Valor"
                          value={criterio.valor}
                          onChange={(e) => updateCriterio(index, 'valor', e.target.value)}
                          className="flex-1"
                        />

                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeCriterio(index)}
                          className="text-destructive hover:text-destructive"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 border-2 border-dashed rounded-lg">
                    <p className="text-sm text-muted-foreground mb-2">
                      Nenhum critério definido ainda
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Listas dinâmicas precisam de pelo menos um critério para funcionar
                    </p>
                  </div>
                )}
              </div>
            )}

            <div className="flex gap-2 pt-4">
              <Button
                onClick={editingLista ? handleUpdateLista : handleCreateLista}
                disabled={
                  isCreating || 
                  !formData.nome.trim() || 
                  (formData.tipoLista === 'dinâmica' && (!formData.criterios || formData.criterios.length === 0))
                }
                className="flex-1  text-white bg-[#ec003f] hover:bg-[#d10037]"
              >
                {isCreating ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Processando...
                  </div>
                ) : editingLista ? (
                  'Atualizar Lista'
                ) : (
                  'Criar Lista'
                )}
              </Button>
              <Button
                variant="outline"
                onClick={resetPanel}
                disabled={isCreating}
              >
                Cancelar
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
    </>
  );
}
 