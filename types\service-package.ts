import { ServiceType } from './budget';

/**
 * Serviço individual dentro de um pacote
 */
export interface PackageService {
  serviceType: ServiceType;
  quantity: number;
  unitPrice: number; // Preço unitário do serviço
  totalPrice: number; // Preço total (unitPrice * quantity)
  originalPrice?: number; // Preço individual do serviço (para referência)
  serviceName?: string; // Nome amigável do serviço
  platform: string; // Plataforma do serviço (instagram, youtube, tiktok)
}

/**
 * Pacote de serviços pré-definido ou personalizado
 */
export interface ServicePackage {
  id: string;
  name: string;
  description?: string;
  services: PackageService[];
  totalPrice: number;
  isCustom: boolean; // Se é um pacote personalizado ou pré-definido
  isTemplate: boolean; // Se é um template que pode ser reutilizado
  
  // Metadados
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Configurações
  isActive: boolean;
  category?: string; // Categoria do pacote (ex: "Instagram Básico", "YouTube Premium")
  tags?: string[]; // Tags para filtros
  
  // Validações
  minFollowers?: number; // Mínimo de seguidores necessário
  platforms: string[]; // Plataformas necessárias ['instagram', 'youtube', 'tiktok']
}

/**
 * Pacote aplicado a um influenciador específico em uma proposta
 */
export interface ProposalPackage {
  id: string;
  packageId: string; // Referência ao ServicePackage
  influencerId: string;
  influencerName: string;
  proposalId: string;
  
  // Dados do pacote (snapshot para preservar histórico)
  packageSnapshot: {
    name: string;
    description?: string;
    services: PackageService[];
    originalTotalPrice: number;
  };
  
  // Negociação
  customPrice?: number; // Preço negociado diferente do pacote padrão
  discount?: number; // Desconto aplicado (%)
  finalPrice: number; // Preço final (customPrice ou originalTotalPrice)
  
  // Status e workflow
  status: 'pending' | 'sent' | 'accepted' | 'rejected' | 'negotiating';
  
  // Contrapropostas (similar ao sistema de budgets)
  counterProposals?: PackageCounterProposal[];
  hasCounterProposal: boolean;
  
  // Metadados
  addedAt: Date;
  addedBy: string;
  updatedAt: Date;
  updatedBy: string;
  
  // Validações
  isValid: boolean; // Se o influenciador oferece todos os serviços
  validationErrors?: string[]; // Erros de validação
}

/**
 * Contraproposta para um pacote
 */
export interface PackageCounterProposal {
  id: string;
  proposedPrice: number;
  proposedServices?: PackageService[]; // Serviços alternativos propostos
  message?: string;
  proposedBy: string; // ID de quem fez a contraproposta
  proposedAt: Date;
  status: 'pending' | 'accepted' | 'rejected';
  
  // Resposta
  responseMessage?: string;
  respondedBy?: string;
  respondedAt?: Date;
}

/**
 * Template de pacote pré-definido
 */
export interface PackageTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  services: Omit<PackageService, 'originalPrice'>[];
  suggestedPriceRange: {
    min: number;
    max: number;
  };
  targetAudience: {
    minFollowers: number;
    maxFollowers?: number;
    platforms: string[];
  };
  isPopular: boolean;
  usageCount: number; // Quantas vezes foi usado
}

/**
 * Input para criação de pacote
 */
export interface CreatePackageInput {
  name: string;
  description?: string;
  services: {
    serviceType: ServiceType;
    quantity: number;
  }[];
  totalPrice: number;
  isTemplate?: boolean;
  category?: string;
  tags?: string[];
  minFollowers?: number;
  platforms: string[];
}

/**
 * Input para aplicar pacote a um influenciador
 */
export interface ApplyPackageInput {
  packageId: string;
  influencerId: string;
  proposalId: string;
  customPrice?: number;
  discount?: number;
}

/**
 * Estatísticas de pacotes
 */
export interface PackageStats {
  totalPackages: number;
  activePackages: number;
  mostUsedPackages: {
    packageId: string;
    name: string;
    usageCount: number;
  }[];
  averagePackageValue: number;
  conversionRate: number; // Taxa de aceitação de pacotes
}

/**
 * Filtros para busca de pacotes
 */
export interface PackageFilters {
  category?: string;
  platforms?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  minFollowers?: number;
  isTemplate?: boolean;
  isActive?: boolean;
  tags?: string[];
  createdBy?: string;
}

/**
 * Resultado de validação de pacote para um influenciador
 */
export interface PackageValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  missingServices: ServiceType[];
  availableServices: ServiceType[];
  estimatedPrice?: number; // Preço estimado baseado nos preços individuais
  savings?: number; // Economia em relação aos preços individuais
}

/**
 * Configurações de exibição de pacotes
 */
export interface PackageDisplaySettings {
  showIndividualPrices: boolean;
  showSavings: boolean;
  showValidationStatus: boolean;
  groupByCategory: boolean;
  sortBy: 'name' | 'price' | 'popularity' | 'created';
  sortOrder: 'asc' | 'desc';
}
