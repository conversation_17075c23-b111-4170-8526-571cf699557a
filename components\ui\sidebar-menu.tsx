"use client";

import * as React from "react";
import { useState } from "react";
import { 
  Filter, 
  Sliders, 
  Users, 
  Star, 
  MapPin, 
  Tag, 
  Instagram, 
  Youtube, 
  Music, 
  Activity,
  ChevronDown,
  ChevronRight,
  BookmarkIcon,
  Save,
  Edit,
  Trash2,
  MoreVertical
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useSavedFiltersGraphQL } from "@/hooks/use-graphql-influencers";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { LanguageToggle } from "@/components/ui/language-toggle";
import { Protect, useAuth as useClerkAuth, useUser } from '@clerk/nextjs';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface SidebarMenuProps {
  onLocationFilter: (location: string) => void;
  onFollowersRangeChange: (min: number, max: number) => void;
  onRatingChange: (minRating: number) => void;
  onVerifiedOnlyChange: (verifiedOnly: boolean) => void;
  onAvailableOnlyChange: (availableOnly: boolean) => void;
  onResetFilters: () => void;
  selectedLocation: string;
  minFollowers: number;
  maxFollowers: number;
  minRating: number;
  verifiedOnly: boolean;
  availableOnly: boolean;
  popularLocations: string[];
  userId?: string;
}

interface SavedFilter {
  id?: string;
  name: string;
  location: string;
  minFollowers: number;
  maxFollowers: number;
  minRating: number;
  verifiedOnly: boolean;
  availableOnly: boolean;
  platforms?: {
    instagram?: boolean;
    youtube?: boolean;
    tiktok?: boolean;
  };
}

const popularLocations = [
  "São Paulo - SP",
  "Rio de Janeiro - RJ", 
  "Belo Horizonte - MG",
  "Brasília - DF",
  "Porto Alegre - RS",
  "Salvador - BA",
  "Recife - PE",
  "Fortaleza - CE",
  "Curitiba - PR",
  "Goiânia - GO"
];

export function SidebarMenu({
  onLocationFilter,
  onFollowersRangeChange,
  onRatingChange,
  onVerifiedOnlyChange,
  onAvailableOnlyChange,
  onResetFilters,
  selectedLocation,
  minFollowers,
  maxFollowers,
  minRating,
  verifiedOnly,
  availableOnly,
  popularLocations: propsPopularLocations,
  userId
}: SidebarMenuProps) {
  
  const [isOpen, setIsOpen] = useState(true);
  const [followersRange, setFollowersRange] = useState([minFollowers, maxFollowers]);
  const [ratingValue, setRatingValue] = useState(minRating);
  const [filterName, setFilterName] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editingFilterId, setEditingFilterId] = useState<string | null>(null);
  
  const {
    savedFilters,
    loading: filtersLoading,
    error: filtersError,
    createSavedFilter,
    updateSavedFilter,
    deleteSavedFilter
  } = useSavedFiltersGraphQL(userId || 'system');

  
  // Atualizar os valores quando os props mudarem
  React.useEffect(() => {
    setFollowersRange([minFollowers, maxFollowers]);
    setRatingValue(minRating);
  }, [minFollowers, maxFollowers, minRating]);
  
  const handleFollowersChange = (value: number[]) => {
    setFollowersRange(value);
    onFollowersRangeChange(value[0], value[1]);
  };
  
  const handleRatingChange = (value: number[]) => {
    setRatingValue(value[0]);
    onRatingChange(value[0]);
  };
  
  const handleSaveFilter = async () => {
    if (!filterName.trim()) {
      return;
    }
    
    setIsSaving(true);
    
    const filterData = {
      name: filterName,
      location: selectedLocation,
      minFollowers: followersRange[0],
      maxFollowers: followersRange[1],
      minRating: ratingValue,
      verifiedOnly,
      availableOnly,
      platforms: {
        instagram: true, // Você pode ajustar com os valores reais quando implementar
        youtube: false,
        tiktok: false
      }
    };
    
    try {
      if (editingFilterId) {
        // Editando filtro existente
        const response = await updateSavedFilter(editingFilterId, filterData);
        
        if (response.data?.updateSavedFilter) {
          console.log('✅ [SidebarMenu] Filtro atualizado com sucesso');
        } else {
          throw new Error('Erro ao atualizar filtro');
        }
      } else {
        // Criando novo filtro
        const response = await createSavedFilter(filterData);
        
        if (response.data?.createSavedFilter) {
          console.log('✅ [SidebarMenu] Filtro criado com sucesso');
        } else {
          throw new Error('Erro ao criar filtro');
        }
      }
      
        setFilterName('');
        setShowSaveDialog(false);
        setEditingFilterId(null);
      
    } catch (error) {
      console.error('❌ [SidebarMenu] Erro ao salvar filtro:', error);
      alert('Erro ao salvar filtro. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleApplyFilter = (filter: SavedFilter) => {
    onLocationFilter(filter.location);
    onFollowersRangeChange(filter.minFollowers, filter.maxFollowers);
    onRatingChange(filter.minRating);
    onVerifiedOnlyChange(filter.verifiedOnly);
    onAvailableOnlyChange(filter.availableOnly);
  };

  const handleEditFilter = (filter: SavedFilter) => {
    // Aplicar o filtro primeiro para preencher os campos
    handleApplyFilter(filter);
    // Definir o nome do filtro para edição
    setFilterName(filter.name);
    // Definir o ID do filtro que está sendo editado
    setEditingFilterId(filter.id || null);
    // Mostrar o diálogo de salvar (que será usado para editar)
    setShowSaveDialog(true);
  };

  const handleDeleteFilter = async (filterId: string) => {
    if (!confirm('Tem certeza que deseja excluir este filtro?')) {
      return;
    }

    try {
      const response = await deleteSavedFilter(filterId);

      if (response.data?.deleteSavedFilter) {
        console.log('✅ [SidebarMenu] Filtro deletado com sucesso');
      } else {
        throw new Error('Erro ao excluir filtro');
      }
    } catch (error) {
      console.error('❌ [SidebarMenu] Erro ao excluir filtro:', error);
      alert('Erro ao excluir filtro. Tente novamente.');
    }
  };

  const handleClearAllFilters = () => {
    if (!confirm('Tem certeza que deseja limpar todos os filtros?')) {
      return;
    }
    onResetFilters();
    setFilterName('');
  };
  
  return (
    <div className={cn(
      "h-full flex flex-col bg-white dark:bg-[#080210] backdrop-blur-md border-r border-gray-200 dark:border-white/10  transition-all duration-300",
      isOpen ? "w-72" : "w-16"
    )}>
      <div className="sticky top-0 z-10 bg-white dark:bg-[#080210] backdrop-blur-lg border-b  p-4">
        <div className="flex items-center justify-between">
          {isOpen ? (
            <h2 className="font-semibold  text-sm text-black dark:bg-[#080210] dark:text-white">Filtros</h2>
          ) : (
            <span className="w-4"></span>
          )}
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => setIsOpen(!isOpen)}
            className="h-8 w-8 rounded-full hover:bg-white/10"
          >
            {isOpen ? <ChevronRight className="h-4 w-4" /> : <Filter className="h-4 w-4" />}
          </Button>
        </div>
         <Protect role="org:admin">
        {isOpen && savedFilters.length > 0 && (
          <div className="mt-3 border-t border-gray-200 dark:border-white/10 pt-3">
            <div className="flex items-center gap-2 mb-2">
              <BookmarkIcon className="h-4 w-4 text-gray-400" />
              <h3 className="text-sm font-medium text-black dark:text-white">Filtros Salvos</h3>
            </div>
            <div className="space-y-1 max-h-32 overflow-y-auto pr-1">
              {savedFilters.map((filter: SavedFilter) => (
                <div
                  key={filter.id}
                  className="w-full flex items-center gap-1 px-2 py-1 text-xs rounded hover:bg-gray-100 dark:hover:bg-white/10 group border border-gray-200 dark:border-white/5"
                >
                  <button
                    onClick={() => handleApplyFilter(filter)}
                    className="flex-1 text-left text-gray-700 dark:text-gray-300 truncate"
                  >
                    {filter.name}
                  </button>
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditFilter(filter);
                      }}
                      className="h-5 w-5 p-0 hover:bg-blue-500/20 hover:text-blue-400"
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteFilter(filter.id!);
                      }}
                      className="h-5 w-5 p-0 hover:bg-red-500/20 hover:text-red-400"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}</Protect>
      </div>
      
      {isOpen && (
        <div className="flex-1 p-4 space-y-6 overflow-y-auto">
          <div className="space-y-4">
            <div className="flex items-center">
              <Sliders className="w-4 h-4 mr-2 text-gray-400" />
              <h3 className="text-sm font-medium text-black dark:text-white text-sm">Opções de Filtro</h3>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-700 dark:text-gray-400">Apenas Exclusivos</span>
                <Switch 
                  checked={verifiedOnly}
                  onCheckedChange={onVerifiedOnlyChange}
                  className="data-[state=checked]:bg-white data-[state=checked]:text-black"
                />
              </div>
              
             
            </div>
          </div>
          
          <Accordion type="multiple" defaultValue={["location", "followers", "rating", "platforms"]} className="w-full">
            <AccordionItem value="location" className="border-gray-200 dark:border-white/10">
              <AccordionTrigger className="text-sm font-medium hover:no-underline py-2">
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                  <span className="text-black dark:text-white text-sm">Localização</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-2 pt-2">
                  <Input
                    placeholder="Buscar localização..."
                    className="bg-gray-100 dark:bg-black/30 border-gray-200 dark:border-white/10 text-black dark:text-white text-xs h-8"
                  />
                  
                  <div className="space-y-1 mt-2">
                    <button
                      onClick={() => onLocationFilter('')}
                      className={cn(
                        "w-full text-left px-2 py-1 text-xs rounded hover:bg-white/10",
                        selectedLocation === '' ? "bg-white/20 text-white" : "text-gray-400"
                      )}
                    >
                      Todas as localizações
                    </button>
                    
                    {propsPopularLocations.map((location) => (
                      <button
                        key={location}
                        onClick={() => onLocationFilter(location)}
                        className={cn(
                          "w-full text-left px-2 py-1 text-xs rounded hover:bg-white/10",
                          selectedLocation === location ? "bg-white/20 text-white" : "text-gray-400"
                        )}
                      >
                        {location}
                      </button>
                    ))}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="followers" className="border-white/10">
              <AccordionTrigger className="text-sm font-medium hover:no-underline py-2">
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-2 text-gray-400 text-sm" />
                  <span>Seguidores</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4 pt-2">
                  <Slider
                    defaultValue={[minFollowers, maxFollowers]}
                    value={followersRange}
                    max={1000000}
                    step={10000}
                    onValueChange={handleFollowersChange}
                    className="mt-6"
                  />
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400">
                      {followersRange[0].toLocaleString('pt-BR')} - {followersRange[1].toLocaleString('pt-BR')}
                    </span>
                    <Badge variant="outline" className="text-xs border-white/10 bg-black/20">
                      Seguidores
                    </Badge>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
            
            
            
            <AccordionItem value="platforms" className="border-white/10">
              <AccordionTrigger className="text-sm font-medium hover:no-underline py-2">
                <div className="flex items-center">
                  <Activity className="w-4 h-4 mr-2 text-gray-400" />
                  <span>Plataformas</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-2 pt-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Instagram className="w-3 h-3 mr-1 text-gray-400" />
                      <span className="text-xs text-gray-400">Instagram</span>
                    </div>
                    <Switch className="data-[state=checked]:bg-white data-[state=checked]:text-black" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Youtube className="w-3 h-3 mr-1 text-gray-400" />
                      <span className="text-xs text-gray-400">YouTube</span>
                    </div>
                    <Switch className="data-[state=checked]:bg-white data-[state=checked]:text-black" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Music className="w-3 h-3 mr-1 text-gray-400" />
                      <span className="text-xs text-gray-400">TikTok</span>
                    </div>
                    <Switch className="data-[state=checked]:bg-white data-[state=checked]:text-black" />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          
         <Protect role="org:admin">
          <div className="pt-4 space-y-2">
            <Button 
              variant="outline" 
              className="w-full border-white/10 hover:bg-white hover:text-black text-white text-xs h-8"
              onClick={handleClearAllFilters}
            >
              Limpar Todos os Filtros
            </Button>
          </div>
       
          <div className="pt-4 space-y-2">
            {showSaveDialog ? (
              <div className="space-y-2  p-2 rounded border ">
                <Input
                  placeholder="Nome do filtro"
                  value={filterName}
                  onChange={(e) => setFilterName(e.target.value)}
                  className=" text-black text-xs h-8"
                />
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    className="flex-1 border bg-white/10 text-black hover:bg-white hover:text-black text-black text-xs h-8"
                    onClick={handleSaveFilter}
                    disabled={isSaving || !filterName.trim()}
                  >
                    {isSaving ? (editingFilterId ? 'Atualizando...' : 'Salvando...') : (editingFilterId ? 'Atualizar' : 'Salvar')}
                  </Button>
                  <Button 
                    variant="outline" 
                    className="border bg-black-100 text-black hover:bg-white/20 text-black text-xs h-8"
                    onClick={() => {
                      setShowSaveDialog(false);
                      setEditingFilterId(null);
                      setFilterName('');
                    }}
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            ) : (
              <Button 
                variant="outline" 
                className="w-full border-white/10 bg-white/10 hover:bg-white hover:text-black text-white text-xs h-8"
                onClick={() => {
                  setShowSaveDialog(true);
                  setEditingFilterId(null);
                  setFilterName('');
                }}
              >
                Salvar Novo Filtro
              </Button>
            )}
            
            <Button 
              variant="outline" 
              className="w-full border-white/10 hover:bg-white hover:text-black text-white text-xs h-8"
              onClick={handleClearAllFilters}
            >
              Limpar Todos os Filtros
            </Button>
          </div>
            </Protect>
          {/* Seção de Filtros Salvos foi movida para o cabeçalho */}
        </div>
      )}

      {/* Footer com Language Toggle e Theme Toggle */}
      <div className="mt-auto p-4 border-t border-gray-200 dark:border-white/10 bg-white dark:bg-[#080210]">
        <div className={cn(
          "flex flex-col gap-2",
          isOpen ? "items-start" : "items-center"
        )}>
          <LanguageToggle isExpanded={isOpen} />
          <ThemeToggle isExpanded={isOpen} />
        </div>
      </div>
    </div>
  );
}


