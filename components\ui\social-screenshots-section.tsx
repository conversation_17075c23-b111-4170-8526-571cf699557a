import React, { useState, useEffect, useCallback } from 'react'
import { createPortal } from 'react-dom'
import { useQuery } from '@apollo/client'
import { useTranslations } from '@/hooks/use-translations'
import { Eye, Download, ExternalLink, RefreshCw } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { SocialIcon, getPlatformDisplayName } from '@/components/ui/social-icons'
import { LazyScreenshot } from '@/components/ui/lazy-screenshot'
import { ScreenshotGridSkeleton, ScreenshotSkeletonStyles } from '@/components/ui/screenshot-skeleton'
import { GET_INFLUENCER_SCREENSHOTS_QUERY } from '@/lib/graphql/mutations'

interface Screenshot {
  id: string
  influencerId: string
  platform: string
  url: string
  filename: string
  size: number
  contentType: string
  uploadedAt: string
  uploadedBy: string
}

interface PlatformScreenshots {
  platform: string
  screenshots: Screenshot[]
  followers?: number
  username?: string
  lastUploadDate?: Date
}

interface SocialScreenshotsSectionProps {
  influencer: any
  className?: string
}

export function SocialScreenshotsSection({ influencer, className = "" }: SocialScreenshotsSectionProps) {
  const { t } = useTranslations()
  const [viewingImage, setViewingImage] = useState<string | null>(null)

  // Usar Apollo Client GraphQL query com política de cache otimizada
  const { data, loading, error, refetch } = useQuery(GET_INFLUENCER_SCREENSHOTS_QUERY, {
    variables: {
      influencerId: influencer?.id
    },
    skip: !influencer?.id,
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'cache-first', // 🔥 CORREÇÃO: Mudança para cache-first para evitar refetches desnecessários
    onCompleted: (data) => {
      console.log('📸 [SocialScreenshotsSection] Dados do GraphQL recebidos:', data)
      console.log('📸 [SocialScreenshotsSection] Screenshots recebidos:', data?.influencerScreenshots?.length || 0)
      if (data?.influencerScreenshots) {
        console.log('📸 [SocialScreenshotsSection] Screenshots detalhados:', data.influencerScreenshots.map((s: Screenshot) => ({
          id: s.id,
          platform: s.platform,
          filename: s.filename,
          url: s.url.substring(0, 100) + '...'
        })))
      }
    },
    onError: (error) => {
      console.error('📸 [SocialScreenshotsSection] Erro GraphQL:', error)
    }
  })

  // 🔥 CORREÇÃO: useCallback para estabilizar a função refetch e useRef para rastrear o ID anterior
  const stableRefetch = useCallback(() => {
    if (influencer?.id) {
      console.log('📸 [SocialScreenshotsSection] Refetch manual iniciado para influencer:', influencer.id)
      refetch()
    }
  }, [influencer?.id, refetch])

  // 🔥 CORREÇÃO: Remover useEffect problemático - deixar que o Apollo gerencie as mudanças via variables
  // O Apollo Client já refaz a query automaticamente quando as variables mudam
  
  // Processar dados do GraphQL
  const processScreenshotsData = (): PlatformScreenshots[] => {
    const screenshots = data?.influencerScreenshots || []
    
    // 🔥 CORREÇÃO: Reduzir logs para evitar spam
    if (screenshots.length > 0) {
      console.log('📸 [SocialScreenshotsSection] Processando screenshots:', screenshots.length)
    }
    
    if (screenshots.length === 0) return []

    // Agrupar screenshots por plataforma
    const platformsMap = new Map<string, Screenshot[]>()
    
    screenshots.forEach((screenshot: Screenshot) => {
      const platform = screenshot.platform.toLowerCase()
      if (!platformsMap.has(platform)) {
        platformsMap.set(platform, [])
      }
      platformsMap.get(platform)!.push(screenshot)
    })
    
    // Converter para array e adicionar informações da plataforma
    const platformsArray: PlatformScreenshots[] = Array.from(platformsMap.entries()).map(([platform, screenshots]) => {
      // Tentar obter informações da plataforma do influencer.platforms se disponível
      const platformInfo = influencer?.platforms?.[platform] || influencer?.[platform] || {}
      
      // Calcular a data do último upload (mais recente)
      const lastUploadDate = screenshots.reduce((latest, screenshot) => {
        const uploadDate = new Date(screenshot.uploadedAt)
        return uploadDate > latest ? uploadDate : latest
      }, new Date(0)) // Começar com data mínima
      
      return {
        platform,
        screenshots,
        followers: platformInfo.followers || platformInfo.seguidores,
        username: platformInfo.username || platformInfo.usuario,
        lastUploadDate: lastUploadDate > new Date(0) ? lastUploadDate : undefined
      }
    })
    
    // Ordenar por número de screenshots (plataformas com mais screenshots primeiro)
    platformsArray.sort((a, b) => b.screenshots.length - a.screenshots.length)
    
    return platformsArray
  }

  const platformsData = processScreenshotsData()

  // 🔥 CORREÇÃO: Usar stableRefetch para evitar re-criação desnecessária
  const handleRefresh = stableRefetch

  // Download de imagem
  const handleDownload = async (imageUrl: string, platform: string, screenshotName: string) => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `${influencer?.name || 'influencer'}-${platform}-${screenshotName}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Erro ao baixar imagem:', error)
    }
  }

  // Modal de visualização
  const ImageModal = () => {
    if (!viewingImage) return null

    return createPortal(
      <div 
        className="fixed inset-0 bg-black/95 z-[9999] flex items-center justify-center p-4"
        onClick={() => setViewingImage(null)}
        style={{ zIndex: 999999 }}
      >
        <div className="relative max-w-[95vw] max-h-[95vh] w-full h-full flex items-center justify-center">
          <img
            src={viewingImage}
            alt="Screenshot"
            className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          />
          <Button
            size="sm"
            variant="secondary"
            className="absolute top-6 right-6 h-12 w-12 p-0 bg-white/90 hover:bg-white shadow-xl text-black text-xl"
            onClick={() => setViewingImage(null)}
          >
            ✕
          </Button>
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
            <p className="text-white text-sm bg-black/70 px-4 py-2 rounded-full backdrop-blur-sm">
              Clique fora da imagem para fechar
            </p>
          </div>
        </div>
      </div>,
      document.body
    )
  }

  if (loading) {
    return (
      <>
        <ScreenshotSkeletonStyles />
        <div className={`space-y-4 ${className}`}>
          <div className="flex justify-between items-center">
            <div className="h-4 bg-muted animate-pulse rounded w-32" />
            <div className="h-4 bg-muted animate-pulse rounded w-16" />
          </div>

          {/* Skeleton para plataformas */}
          {['instagram', 'tiktok', 'youtube'].map((platform) => (
            <Card key={platform}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <SocialIcon platform={platform} size={16} className="opacity-50" />
                    <div className="h-4 bg-muted animate-pulse rounded w-20" />
                  </div>
                  <div className="h-5 bg-muted animate-pulse rounded w-16" />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <ScreenshotGridSkeleton columns={2} rows={1} />
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    )
  }

  if (error) {
    console.error('Erro GraphQL ao carregar screenshots:', error)
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex justify-between items-center">
         
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            className="h-6 px-2 text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Tentar novamente
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center p-4 border border-dashed rounded-md">
          <SocialIcon platform="instagram" size={24} className="text-red-400 opacity-50 mb-2" />
          <p className="text-xs text-red-400 text-center">
            Erro ao carregar screenshots
          </p>
        </div>
      </div>
    )
  }

  if (platformsData.length === 0) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex justify-between items-center">
         
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            className="h-6 px-2 text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Atualizar
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center p-4 border border-dashed rounded-md">
          <SocialIcon platform="instagram" size={24} className="text-muted-foreground opacity-50 mb-2" />
          <p className="text-xs text-muted-foreground text-center">
            Nenhum screenshot disponível
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      <ScreenshotSkeletonStyles />
      <div className={`space-y-2 ${className}`}>
        <div className="flex justify-between items-center">
          
          <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
              {platformsData.length === 1 
                ? t('influencers.networks_count_singular')
                : t('influencers.networks_count_plural', { count: platformsData.length })
              }
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              className="h-6 px-2 text-xs"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </div>
        </div>

        <div className="space-y-3">
          {platformsData.map((platformData) => (
            <Card key={platformData.platform} className="overflow-hidden">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <SocialIcon 
                    platform={platformData.platform} 
                    size={16} 
                    className="text-foreground" 
                  />
                  {getPlatformDisplayName(platformData.platform)}
                  
                  <div className="flex items-center gap-2 ml-auto">
                    {platformData.followers && (
                      <Badge variant="outline" className="text-xs">
                        {platformData.followers.toLocaleString()} seguidores
                      </Badge>
                    )}
                    {platformData.lastUploadDate && (
                      <span className="text-xs text-muted-foreground">
                        {platformData.lastUploadDate.toLocaleDateString('pt-BR', {
                          day: '2-digit',
                          month: '2-digit',
                          year: 'numeric'
                        })}
                      </span>
                    )}
                  </div>
                </CardTitle>
                {platformData.username && (
                  <p className="text-xs text-muted-foreground">@{platformData.username}</p>
                )}
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="grid grid-cols-4 gap-2 max-w-full overflow-hidden">
                  {platformData.screenshots
                    .map((screenshot, index) => (
                    <div key={screenshot.id} className="space-y-1 min-w-0 max-w-full" style={{ maxWidth: '64px' }}>
                      <LazyScreenshot
                        src={screenshot.url}
                        alt={`${platformData.platform} screenshot ${index + 1}`}
                        filename={screenshot.filename}
                        platform={platformData.platform}
                        aspectRatio="square"
                        onView={setViewingImage}
                        onDownload={handleDownload}
                        showActions={true}
                        useThumbnail={true}
                      />

                      {/* Nome do arquivo */}
                      <p className="text-xs text-muted-foreground truncate" title={screenshot.filename}>
                        {screenshot.filename}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Modal renderizado via Portal */}
      <ImageModal />
    </>
  )
} 

