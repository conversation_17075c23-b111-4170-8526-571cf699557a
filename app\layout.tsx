import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { ClerkProvider } from '@clerk/nextjs';
import { ApolloProviderWrapper } from '@/components/apollo-provider';
import { FirebaseAuthProvider } from '@/contexts/firebase-auth-context';

import { Toaster } from '@/components/ui/toaster';
import { TooltipProvider } from '@/components/ui/tooltip';
import { SidebarProvider } from '@/contexts/sidebar-context';
import { ConditionalLayout } from '@/components/conditional-layout';
import { GlobalLoaderProvider } from '@/components/ui/loader';
import { LanguageProviderWrapper } from '@/contexts/language-context';
import { ProposalsProvider } from '@/contexts/proposals-context';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import "@/styles/globals.css";

const inter = Inter({ subsets: ['latin'] });

// Localização customizada em português
const ptBR = {
  userProfile: {
    navbar: {
      title: 'Con<PERSON>',
      description: '<PERSON><PERSON><PERSON><PERSON> as informações da sua conta.',
      account: 'Con<PERSON>',
      security: 'Segurança',
    },
    start: {
      headerTitle__account: 'Perfil',
      headerTitle__security: 'Segurança',
      // Seções do perfil
      profileSection: {
        title: 'Perfil',
        primaryButton: 'Atualizar perfil',
      },
      emailAddressesSection: {
        title: 'Endereços de email',
        primaryButton: 'Adicionar endereço de email',
        detailsAction__unverified: 'Verificar',
        destructiveAction: 'Remover email',
      },
      phoneNumbersSection: {
        title: 'Números de telefone',
        primaryButton: 'Adicionar número de telefone',
      },
      passwordSection: {
        title: 'Senha',
        primaryButton: 'Alterar senha',
      },
      mfaSection: {
        title: 'Verificação em duas etapas',
        phoneCode: {
          destructiveActionLabel: 'Remover',
        },
        backupCodes: {
          actionLabel__regenerate: 'Regenerar',
        },
      },
      activeDevicesSection: {
        title: 'Dispositivos ativos',
        primaryButton: 'Gerenciar dispositivos',
      },
      connectedAccountsSection: {
        title: 'Contas conectadas',
        primaryButton: 'Conectar conta',
      },
      web3WalletsSection: {
        title: 'Carteiras Web3',
        primaryButton: 'Conectar carteira',
      },
      dangerSection: {
        title: 'Excluir conta',
        deleteAccountButton: 'Excluir conta',
      },
    },
    profilePage: {
      title: 'Perfil',
      imageFormTitle: 'Foto do perfil',
      imageFormSubtitle: 'Carregar',
      imageFormDestructiveActionSubtitle: 'Remover',
      fileDropAreaHint: 'Tamanho recomendado 1:1, até 10MB.',
    },
    emailAddressPage: {
      title: 'Endereços de email',
      verifyTitle: 'Verificar endereço de email',
    },
    phoneNumberPage: {
      title: 'Números de telefone',
      verifyTitle: 'Verificar número de telefone',
      verifySubtitle: 'Digite o código de verificação enviado para {{identifier}}',
      infoText: 'Uma mensagem de texto com um código de verificação será enviada para este número. Taxas de mensagem e dados podem ser aplicadas.',
    },
    passwordPage: {
      title: 'Senha',
      successMessage__set: 'Sua senha foi definida.',
      successMessage__update: 'Sua senha foi alterada.',
    },
    connectedAccountPage: {
      title: 'Contas conectadas',
      socialButtonsBlockButton: '{{provider|titleize}}',
    },
    mfaPage: {
      title: 'Verificação em duas etapas',
      formHint: 'Selecione um método para adicionar',
    },
    mfaPhoneCodePage: {
      title: 'Verificação em duas etapas por SMS',
      primaryButton__addPhoneNumber: 'Adicionar número de telefone',
      subtitle__availablePhoneNumbers: 'Selecione um número de telefone existente para registrar na verificação em duas etapas por SMS ou adicione um novo.',
      subtitle__unavailablePhoneNumbers: 'Não há números de telefone disponíveis para registrar na verificação em duas etapas por SMS, adicione um novo.',
      backButton: 'Usar número existente',
      successTitle: 'Verificação por código SMS habilitada',
      successMessage1: 'Ao fazer login, você precisará inserir um código de verificação enviado para este número de telefone como uma etapa adicional.',
      successMessage2: 'Salve estes códigos de backup e armazene-os em um local seguro. Se você perder o acesso ao seu dispositivo de autenticação, pode usar códigos de backup para fazer login.',
    },
    mfaTOTPPage: {
      title: 'Adicionar aplicativo autenticador',
      verifyTitle: 'Código de verificação',
      verifySubtitle: 'Digite o código gerado pelo seu aplicativo autenticador',
      successMessage: 'A verificação em duas etapas agora está habilitada. Ao fazer login, você precisará inserir um código de verificação deste aplicativo autenticador como uma etapa adicional.',
      authenticatorApp: {
        infoText__ableToScan: 'Configure um novo método de login no seu aplicativo autenticador e escaneie o seguinte código QR para vinculá-lo à sua conta.',
        infoText__unableToScan: 'Configure um novo método de login no seu autenticador e digite a chave fornecida abaixo.',
        inputLabel__unableToScan: 'Chave secreta',
        buttonAbleToScan__nonPrimary: 'Escanear código QR',
        buttonUnableToScan__nonPrimary: 'Não consigo escanear o código QR',
      },
    },
    mfaBackupCodePage: {
      title: 'Adicionar verificação de código de backup',
      subtitle: 'Os códigos de backup são uma forma secundária de autenticar sua conta no caso de você perder o acesso ao seu dispositivo autenticador.',
      successTitle: 'Códigos de backup habilitados',
      successMessage1: 'Agora você pode usar um desses códigos de backup para acessar sua conta se perder o acesso ao seu dispositivo autenticador.',
      successMessage2: 'Salve estes códigos e guarde-os em um local seguro.',
    },
    deletePage: {
      title: 'Excluir conta',
      messageLine1: 'Tem certeza de que deseja excluir sua conta?',
      messageLine2: 'Esta ação é permanente e irreversível.',
      actionDescription: 'Digite "Excluir conta" abaixo para continuar.',
      confirm: 'Excluir conta',
    },
  },
  organizationSwitcher: {
    action__createOrganization: 'Criar organização',
    action__manageOrganization: 'Gerenciar',
  },
  organizationProfile: {
    navbar: {
      title: 'Organização',
      description: 'Gerencie sua organização.',
      general: 'Geral',
      members: 'Membros',
    },
    start: {
      headerTitle__general: 'Geral',
      headerTitle__members: 'Membros',
      profileSection: {
        primaryButton: 'Editar perfil',
        title: 'Perfil da Organização',
        uploadAction__title: 'Logo',
      },
    },
    profilePage: {
      title: 'Atualizar Perfil',
      dangerSection: {
        leaveOrganization: {
          actionDescription: 'Digite "{{organizationName}}" abaixo para continuar.',
        },
        deleteOrganization: {
          actionDescription: 'Digite "{{organizationName}}" abaixo para continuar.',
        },
      },
      domainSection: {
        menuAction__manage: 'Gerenciar',
        menuAction__remove: 'Excluir',
        menuAction__verify: 'Verificar',
      },
    },
    membersPage: {
      detailsTitle__emptyRow: 'Nenhum membro para exibir',
      action__invite: 'Convidar',
    },
    invitePage: {
      title: 'Convidar novos membros',
      subtitle: 'Digite ou cole um ou mais endereços de email, separados por espaços ou vírgulas.',
      formButtonPrimary__continue: 'Enviar convites',
      selectDropdown__role: 'Selecionar função',
    },
    verifiedDomainPage: {
      title: 'Atualizar {{domain}}',
    },
  },
  createOrganization: {
    title: 'Criar organização',
  },
  organizationList: {
    title: 'Escolher uma conta',
    titleWithoutPersonal: 'Escolher uma organização',
  },
  signIn: {
    start: {
      title: 'Entrar em {{applicationName}}',
      subtitle: 'Bem-vindo de volta! Por favor, faça login para continuar',
      actionText: 'Não tem uma conta?',
      actionLink: 'Inscrever-se',
    },
    password: {
      subtitle: 'Digite a senha associada à sua conta',
    },
    forgotPasswordAlternativeMethods: {
      label__alternativeMethods: 'Ou, entre com outro método',
    },
    resetPassword: {
      title: 'Definir nova senha',
    },
    phoneCodeMfa: {
      subtitle: 'Para continuar, digite o código de verificação enviado para seu telefone',
    },
    totpMfa: {
      subtitle: 'Para continuar, digite o código de verificação gerado pelo seu aplicativo autenticador',
    },
    backupCodeMfa: {
      subtitle: 'Seu código de backup é aquele que você obteve ao configurar a autenticação de duas etapas.',
    },
    accountSwitcher: {
      action__addAccount: 'Adicionar conta',
      action__signOutAll: 'Sair de todas as contas',
      subtitle: 'Selecione a conta com a qual deseja continuar.',
      title: 'Escolher uma conta',
    },
    alternativeMethods: {
      actionText: 'Não tem nenhum destes?',
      subtitle: 'Enfrentando problemas? Você pode usar qualquer um destes métodos para entrar.',
    },
    forgotPassword: {
      subtitle_email: 'Primeiro, digite o código enviado para seu email',
      subtitle_phone: 'Primeiro, digite o código enviado para seu telefone',
      title: 'Redefinir senha',
    },
  },
  signUp: {
    start: {
      title: 'Criar sua conta',
      subtitle: 'para continuar em {{applicationName}}',
      actionText: 'Já tem uma conta?',
      actionLink: 'Entrar',
    },
    emailLink: {
      title: 'Verificar seu email',
      subtitle: 'para continuar em {{applicationName}}',
      formTitle: 'Link de verificação',
      formSubtitle: 'Use o link de verificação enviado para seu endereço de email',
      resendButton: 'Não recebeu um link? Reenviar',
      verified: {
        title: 'Inscrito com sucesso',
      },
      loading: {
        title: 'Inscrevendo...',
      },
      verifiedSwitchTab: {
        title: 'Email verificado com sucesso',
        subtitle: 'Retorne à aba recém-aberta para continuar',
        subtitleNewTab: 'Retorne à aba anterior para continuar',
      },
    },
    emailCode: {
      title: 'Verificar seu email',
      subtitle: 'para continuar em {{applicationName}}',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de verificação enviado para seu endereço de email',
      resendButton: 'Não recebeu um código? Reenviar',
    },
    phoneCode: {
      title: 'Verificar seu telefone',
      subtitle: 'para continuar em {{applicationName}}',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de verificação enviado para seu número de telefone',
      resendButton: 'Não recebeu um código? Reenviar',
    },
    continue: {
      title: 'Preencher campos obrigatórios',
      subtitle: 'para continuar em {{applicationName}}',
      actionText: 'Já tem uma conta?',
      actionLink: 'Entrar',
    },
  },
  // Campos e labels gerais
  formFieldLabel__organizationName: 'Nome da organização',
  formFieldInputPlaceholder__organizationName: 'Digite o nome da organização',
  formFieldError__matchingPasswords: 'As senhas coincidem.',
  formFieldError__notMatchingPasswords: 'As senhas não coincidem.',
  formFieldAction__forgotPassword: 'Esqueceu a senha?',
  formButtonPrimary: 'Continuar',
  formButtonPrimary__save: 'Salvar',
  formButtonPrimary__update: 'Atualizar',
  formButtonPrimary__add: 'Adicionar',
  formButtonPrimary__remove: 'Remover',
  formButtonPrimary__verify: 'Verificar',
  dividerText: 'ou',
  footerActionLink__useAnotherMethod: 'Usar outro método',
  badge__primary: 'Principal',
  badge__thisDevice: 'Este dispositivo',
  badge__unverified: 'Não verificado',
  badge__you: 'Você',
  // Botões específicos para UserProfile
  formButtonPrimary__updateProfile: 'Atualizar perfil',
  formButtonPrimary__updatePassword: 'Atualizar senha',
  formButtonPrimary__addEmailAddress: 'Adicionar endereço de email',
  formButtonPrimary__deleteAccount: 'Excluir conta',
  // Labels específicos
  formFieldLabel__currentPassword: 'Senha atual',
  formFieldLabel__newPassword: 'Nova senha',
  formFieldLabel__confirmPassword: 'Confirmar senha',
  formFieldLabel__emailAddress: 'Endereço de email',
  formFieldLabel__phoneNumber: 'Número de telefone',
  formFieldLabel__password: 'Senha',
  formFieldLabel__firstName: 'Nome',
  formFieldLabel__lastName: 'Sobrenome',
  // Placeholders específicos
  formFieldInputPlaceholder__emailAddress: 'Digite seu email',
  formFieldInputPlaceholder__phoneNumber: 'Digite seu telefone',
  formFieldInputPlaceholder__password: 'Digite sua senha',
  formFieldInputPlaceholder__firstName: 'Digite seu nome',
  formFieldInputPlaceholder__lastName: 'Digite seu sobrenome',
  // Erros
  unstable__errors: {
    identification_deletion_failed: 'Não é possível excluir sua última identificação.',
    phone_number_exists: 'Este número de telefone já está sendo usado por outra conta.',
    captcha_invalid: 'Verificação de segurança inválida. Tente novamente.',
    form_param_format_invalid__phone_number: 'O número de telefone deve estar em um formato internacional válido',
  },

  // Traduções específicas para SignUp - Modal de acesso restrito
  signUp: {
    restrictedAccess: {
      title: 'Acesso restrito',
      subtitle: 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
      blockButton__emailSupport: 'Suporte por email',
      footerText: 'Já tem uma conta?',
      footerLink: 'Entrar',
      footerActionText: 'Já tem uma conta?',
      footerActionLink: 'Entrar',
      actionText: 'Já tem uma conta?',
      actionLink: 'Entrar',
    },
    start: {
      title: 'Criar sua conta',
      subtitle: 'Junte-se à nossa plataforma e comece sua jornada',
      actionText: 'Já tem uma conta?',
      actionLink: 'Entrar',
    },
  },

  // Traduções específicas para SignIn
  signIn: {
    start: {
      title: 'Entrar na sua conta',
      subtitle: 'Bem-vindo de volta! Entre para continuar',
      actionText: 'Quer acesso antecipado?',
      actionLink: 'Entrar na lista de espera',
      actionText__join_waitlist: 'Quer acesso antecipado?',
      actionLink__join_waitlist: 'Entrar na lista de espera',
      footerActionText: 'Não tem uma conta?',
      footerActionLink: 'Criar conta',
    },
    factorOne: {
      title: 'Verificar sua identidade',
      subtitle: 'Digite o código enviado para continuar',
      resendButton: 'Não recebeu o código? Reenviar',
    },
    factorTwo: {
      title: 'Verificação em duas etapas',
      subtitle: 'Digite o código do seu aplicativo autenticador',
      resendButton: 'Não recebeu o código? Reenviar',
    },
    emailCode: {
      title: 'Verificar seu email',
      subtitle: 'Digite o código enviado para {{identifier}}',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de 6 dígitos enviado para seu email',
      resendButton: 'Não recebeu o código? Reenviar',
    },
    phoneCode: {
      title: 'Verificar seu telefone',
      subtitle: 'Digite o código enviado para {{identifier}}',
      formTitle: 'Código de verificação',
      formSubtitle: 'Digite o código de 6 dígitos enviado para seu telefone',
      resendButton: 'Não recebeu o código? Reenviar',
    },
  },

  // Traduções diretas para chaves específicas do Clerk
  'Sign ups are currently disabled. If you believe you should have access, please contact support.': 'Cadastros estão temporariamente desabilitados. Se você acredita que deveria ter acesso, entre em contato com o suporte.',
  'Email support': 'Suporte por email',
  'Already have an account?': 'Já tem uma conta?',
  'Sign in': 'Entrar',
  'Access restricted': 'Acesso restrito',
  'Secured by': 'Protegido por',
  'Development mode': 'Modo de desenvolvimento',

  // Traduções específicas para Sign In
  "Didn't receive a code? Resend": 'Não recebeu o código? Reenviar',
  'Resend': 'Reenviar',
  'Verify': 'Verificar',
  'Continue': 'Continuar',
  'Back': 'Voltar',
  'Try again': 'Tentar novamente',
  'Want early access?': 'Quer acesso antecipado?',
  "Don't have an account?": 'Não tem uma conta?',
  'Sign up': 'Criar conta',
  'Join waitlist': 'Entrar na lista de espera',
};

export const metadata: Metadata = {
  title: 'Deu Match - Plataforma de Marketing Influencer',
  description: 'Conecte marcas e influenciadores de forma eficiente',
  keywords: ['marketing', 'influencer', 'marcas', 'campanhas', 'publicidade'],
  authors: [{ name: 'Deu Match' }],
  icons: {
    icon: '/favicon.svg',
    apple: '/favicon.svg',
    shortcut: '/favicon.svg',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider
      localization={ptBR}
      appearance={{
        variables: {
          colorPrimary: '#ff0074',
        },
      }}
    >
      <html lang="pt" suppressHydrationWarning>
        <body className={inter.className}>
          <ProposalsProvider>
          <ThemeProvider
           attribute="class"
           defaultTheme="dark"
           enableSystem
           storageKey="deu-match-theme"
           disableTransitionOnChange
          >
            <TooltipProvider>
              <ApolloProviderWrapper>
                <FirebaseAuthProvider>
                  <GlobalLoaderProvider>
                    <LanguageProviderWrapper>
                      <SidebarProvider>
                        <NuqsAdapter>
                          <ConditionalLayout>
                            {children}
                            <Toaster />
                          </ConditionalLayout>
                        </NuqsAdapter>
                      </SidebarProvider>
                    </LanguageProviderWrapper>
                  </GlobalLoaderProvider>
                </FirebaseAuthProvider>
              </ApolloProviderWrapper>
            </TooltipProvider>
          </ThemeProvider>
          </ProposalsProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}


