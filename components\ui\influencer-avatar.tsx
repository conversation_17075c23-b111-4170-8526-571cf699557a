'use client';

import React, { memo } from 'react';
import { cn } from '@/lib/utils';

interface InfluencerAvatarProps {
  influencerId: string;
  influencerName: string;
  avatarPath?: string;
  avatarUrl?: string;
  className?: string;
  fallbackClassName?: string;
}

/**
 * Componente Avatar específico para influenciadores
 * ✅ ULTRA-OTIMIZADO: Usa React.memo e CSS puro para eliminar piscar
 * 🔥 SEM JAVASCRIPT LOADING: Apenas CSS para máxima performance
 */
const InfluencerAvatarCore = memo(({
  influencerId,
  influencerName,
  avatarPath,
  avatarUrl: providedAvatarUrl,
  className,
  fallbackClassName
}: InfluencerAvatarProps) => {
  
  // ✅ SEM PLACEHOLDER SVG - apenas fallbacks CSS instantâneos
  const finalAvatarUrl = providedAvatarUrl || avatarPath;

  // Gerar iniciais do nome para fallback
  const getInitials = (name: string | undefined | null) => {
    if (!name) return "??";
    
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(influencerName);

  return (
    <div 
      className={cn(
        "relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",
        className
      )}
      style={{
        // 🔥 CSS BACKGROUND IMAGE: Sem JavaScript, sem piscar
        backgroundImage: `url("${finalAvatarUrl}")`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* 🔥 FALLBACK: Só aparece quando a imagem de fundo falha */}
      <div 
        className={cn(
          'flex h-full w-full items-center justify-center rounded-full text-white font-semibold text-sm',
          'opacity-0', // Por padrão invisível
          fallbackClassName
        )}
        style={{
          // Só fica visível se a imagem de fundo não carregar
          background: 'linear-gradient(135deg, rgb(59 130 246) 0%, rgb(147 51 234) 100%)'
        }}
        onError={() => {
          // Se houver erro, tornar o fallback visível
          const element = document.getElementById(`avatar-fallback-${influencerId}`);
          if (element) {
            element.style.opacity = '1';
          }
        }}
        id={`avatar-fallback-${influencerId}`}
      >
        {initials}
      </div>
      
      {/* 🔥 IMAGEM INVISÍVEL: Para detectar erros de carregamento */}
      <img
        src={finalAvatarUrl}
        alt=""
        style={{ display: 'none' }}
        onError={() => {
          // Se a imagem falhar, mostrar o fallback
          const fallback = document.getElementById(`avatar-fallback-${influencerId}`);
          if (fallback) {
            fallback.style.opacity = '1';
          }
        }}
      />
    </div>
  );
}, (prevProps, nextProps) => {
  // 🔥 COMPARAÇÃO OTIMIZADA: Só re-renderiza se mudou algo importante
  return (
    prevProps.influencerId === nextProps.influencerId &&
    prevProps.influencerName === nextProps.influencerName &&
    prevProps.avatarPath === nextProps.avatarPath &&
    prevProps.avatarUrl === nextProps.avatarUrl &&
    prevProps.className === nextProps.className
  );
});

InfluencerAvatarCore.displayName = 'InfluencerAvatarCore';

export function InfluencerAvatar(props: InfluencerAvatarProps) {
  return <InfluencerAvatarCore {...props} />;
}

/**
 * Versão simplificada do componente - agora é idêntica à principal
 */
export function InfluencerAvatarSimple(props: InfluencerAvatarProps) {
  return <InfluencerAvatarCore {...props} />;
}

