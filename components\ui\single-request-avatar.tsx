'use client';

import React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface SingleRequestAvatarProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackClassName?: string;
  fallbackText?: string;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
}

const sizeMap = {
  sm: { size: 32, className: 'h-8 w-8' },
  md: { size: 40, className: 'h-10 w-10' },
  lg: { size: 48, className: 'h-12 w-12' },
  xl: { size: 80, className: 'h-20 w-20' }
};

/**
 * Avatar com APENAS UMA REQUISIÇÃO HTTP
 * 🎯 ESTRATÉGIA: Eliminar srcset responsivo para imagens críticas
 * ✅ UMA única versão da imagem - sem múltiplas requisições
 * 🚀 Preload automático para elementos críticos
 * 💡 Sizes fixo para evitar múltiplas versões
 * 
 * Este componente força o Next.js a usar apenas UMA versão da imagem,
 * eliminando as 16+ requisições HTTP que estavam causando LCP lento.
 */
export function SingleRequestAvatar({
  src,
  alt,
  size = 'md',
  className,
  fallbackClassName,
  fallbackText,
  priority = false,
  loading = 'lazy'
}: SingleRequestAvatarProps) {
  const sizeConfig = sizeMap[size];
  
  // Gerar iniciais para fallback
  const getInitials = (text: string) => {
    if (!text) return '??';
    return text
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(fallbackText || alt);

  // Gerar cor baseada no nome para consistência
  const getColorFromName = (name: string) => {
    const colors = [
      'from-blue-500 to-purple-600',
      'from-green-500 to-blue-600', 
      'from-purple-500 to-pink-600',
      'from-orange-500 to-red-600',
      'from-teal-500 to-cyan-600',
      'from-indigo-500 to-purple-600',
      'from-pink-500 to-rose-600',
      'from-emerald-500 to-teal-600'
    ];
    
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const gradientColors = getColorFromName(fallbackText || alt);

  // Se não há src, renderizar apenas fallback (ZERO requisições)
  if (!src) {
    return (
      <div 
        className={cn(
          'relative flex shrink-0 overflow-hidden rounded-full bg-gradient-to-br',
          gradientColors,
          sizeConfig.className,
          className
        )}
        role="img"
        aria-label={alt}
      >
        <div 
          className={cn(
            'flex items-center justify-center text-white font-semibold w-full h-full select-none',
            size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : size === 'lg' ? 'text-base' : 'text-lg',
            fallbackClassName
          )}
        >
          {initials}
        </div>
      </div>
    );
  }

  return (
    <div 
      className={cn(
        'relative flex shrink-0 overflow-hidden rounded-full bg-gradient-to-br',
        gradientColors,
        sizeConfig.className,
        className
      )}
      role="img"
      aria-label={alt}
    >
      {/* 🎯 IMAGEM COM UMA ÚNICA REQUISIÇÃO */}
      <Image
        src={src}
        alt={alt}
        width={sizeConfig.size}
        height={sizeConfig.size}
        loading={loading}
        priority={priority}
        
        // 🚀 CRÍTICO: sizes fixo elimina srcset responsivo
        sizes={`${sizeConfig.size}px`}
        
        // 🎯 CRÍTICO: Forçar dimensões exatas
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          objectPosition: 'center'
        }}
        
        // Fallback em caso de erro
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
        }}
      />
      
      {/* 🎯 FALLBACK sempre presente para casos de erro */}
      <div 
        className={cn(
          'absolute inset-0 flex items-center justify-center text-white font-semibold',
          size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : size === 'lg' ? 'text-base' : 'text-lg',
          fallbackClassName
        )}
        style={{ display: 'none' }}
      >
        {initials}
      </div>
    </div>
  );
}

/**
 * Avatar crítico com preload automático
 * ✅ UMA única requisição HTTP
 * 🎯 Preload automático para LCP otimizado
 */
export function CriticalSingleRequestAvatar(props: SingleRequestAvatarProps) {
  return (
    <SingleRequestAvatar
      {...props}
      priority={true}
      loading="eager"
    />
  );
}
