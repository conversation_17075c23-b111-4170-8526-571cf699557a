'use client'

import { useMemo, useCallback } from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { ZeroLCPAvatar, ZeroLCPInfluencerAvatar } from "@/components/ui/zero-lcp-avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { Grid, List, Users, MapPin, Star, ExternalLink } from "lucide-react"
import { useInfluencersUrlState } from "@/hooks/use-influencers-url-state"
import { useInfluencersGraphQL } from "@/hooks/use-influencers-graphql"
import { useAuth } from "@/hooks/use-auth-v2"

interface InfluencerGridClientProps {
  userId: string
}

/**
 * 🔥 COMPONENTE CLIENT OTIMIZADO - Grid de influenciadores
 * Usa GraphQL com cache otimizado e estado via URL
 */
export default function InfluencerGridClient({ userId }: InfluencerGridClientProps) {
  const { currentUser } = useAuth()
  const { state, actions } = useInfluencersUrlState()

  // 🔥 FILTROS PARA GRAPHQL COM MEMOIZAÇÃO
  const graphqlFilters = useMemo(() => ({
    searchTerm: state.searchTerm || undefined,
    location: state.selectedLocation || undefined,
    minFollowers: state.minFollowers > 0 ? state.minFollowers : undefined,
    maxFollowers: state.maxFollowers < 10000000 ? state.maxFollowers : undefined,
    minRating: state.minRating > 0 ? state.minRating : undefined,
    verifiedOnly: state.verifiedOnly || undefined,
    availableOnly: state.availableOnly || undefined,
    brands: state.selectedBrands.length > 0 ? state.selectedBrands : undefined
  }), [state])

  // 🔥 HOOK GRAPHQL OTIMIZADO
  const {
    influencers,
    loading,
    error,
    totalCount,
    hasNextPage,
    loadMore
  } = useInfluencersGraphQL({
    userId: currentUser?.id || '',
    autoFetch: true,
    filters: graphqlFilters,
    pagination: {
      limit: state.limit,
      offset: (state.page - 1) * state.limit
    }
  })

  // 🔥 HANDLERS OTIMIZADOS COM useCallback
  const handleSelectInfluencer = useCallback((influencerId: string) => {
    actions.setSelectedInfluencer(influencerId)
  }, [actions])

  const handleViewModeToggle = useCallback(() => {
    actions.setViewMode(state.viewMode === 'grid' ? 'table' : 'grid')
  }, [actions, state.viewMode])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !loading) {
      actions.setPage(state.page + 1)
    }
  }, [hasNextPage, loading, actions, state.page])

  // 🔥 FORMATAÇÃO DE NÚMEROS
  const formatFollowers = useCallback((count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`
    if (count >= 1000) return `${(count / 1000).toFixed(0)}K`
    return count.toString()
  }, [])

  // 🔥 LOADING STATE
  if (loading && influencers.length === 0) {
    return <InfluencerGridSkeleton />
  }

  // 🔥 ERROR STATE
  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <p className="text-destructive mb-4">Erro ao carregar influenciadores</p>
            <Button onClick={() => window.location.reload()}>
              Tentar novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-auto min-h-0">
      {/* 🔥 HEADER COM CONTROLES */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur border-b border-border">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-4">
            <h3 className="font-medium">
              {totalCount} influenciador{totalCount !== 1 ? 'es' : ''}
            </h3>
            {state.isFiltered && (
              <Badge variant="outline" className="text-xs">
                Filtrado
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleViewModeToggle}
              className="h-8"
            >
              {state.viewMode === 'grid' ? (
                <List className="h-4 w-4" />
              ) : (
                <Grid className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* 🔥 CONTEÚDO PRINCIPAL */}
      <div className="p-4">
        {influencers.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              Nenhum influenciador encontrado com os filtros aplicados
            </p>
            <Button
              variant="outline"
              onClick={actions.resetFilters}
              className="mt-4"
            >
              Limpar filtros
            </Button>
          </div>
        ) : (
          <>
            {/* 🔥 GRID VIEW */}
            {state.viewMode === 'grid' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {influencers.map((influencer) => (
                  <InfluencerCard
                    key={influencer.id}
                    influencer={influencer}
                    isSelected={state.selectedInfluencer === influencer.id}
                    onSelect={() => handleSelectInfluencer(influencer.id)}
                    formatFollowers={formatFollowers}
                  />
                ))}
              </div>
            )}

            {/* 🔥 TABLE VIEW */}
            {state.viewMode === 'table' && (
              <div className="space-y-2">
                {influencers.map((influencer) => (
                  <InfluencerRow
                    key={influencer.id}
                    influencer={influencer}
                    isSelected={state.selectedInfluencer === influencer.id}
                    onSelect={() => handleSelectInfluencer(influencer.id)}
                    formatFollowers={formatFollowers}
                  />
                ))}
              </div>
            )}

            {/* 🔥 LOAD MORE */}
            {hasNextPage && (
              <div className="text-center mt-8">
                <Button
                  onClick={handleLoadMore}
                  disabled={loading}
                  variant="outline"
                >
                  {loading ? 'Carregando...' : 'Carregar mais'}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

// 🔥 COMPONENTE DE CARD OTIMIZADO
function InfluencerCard({ influencer, isSelected, onSelect, formatFollowers }: any) {
  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-[#ff0074] border-[#ff0074]' : ''
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-4">
        <div className="flex items-center space-x-3 mb-3">
          <ZeroLCPInfluencerAvatar
            influencerName={influencer.name || 'Influenciador'}
            avatarUrl={influencer.avatar}
            size="lg"
          />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium truncate">{influencer.name}</h4>
            <p className="text-sm text-muted-foreground truncate">
              @{influencer.username}
            </p>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center text-sm text-muted-foreground">
            <Users className="h-3 w-3 mr-1" />
            {formatFollowers(influencer.followersCount)} seguidores
          </div>
          
          {influencer.location && (
            <div className="flex items-center text-sm text-muted-foreground">
              <MapPin className="h-3 w-3 mr-1" />
              {influencer.location}
            </div>
          )}
          
          {influencer.rating && (
            <div className="flex items-center text-sm text-muted-foreground">
              <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
              {influencer.rating.toFixed(1)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// 🔥 COMPONENTE DE ROW OTIMIZADO
function InfluencerRow({ influencer, isSelected, onSelect, formatFollowers }: any) {
  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-sm ${
        isSelected ? 'ring-2 ring-[#ff0074] border-[#ff0074]' : ''
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ZeroLCPInfluencerAvatar
              influencerName={influencer.name || 'Influenciador'}
              avatarUrl={influencer.avatar}
              size="md"
              className="h-10 w-10"
            />
            <div>
              <h4 className="font-medium">{influencer.name}</h4>
              <p className="text-sm text-muted-foreground">@{influencer.username}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <span>{formatFollowers(influencer.followersCount)} seguidores</span>
            {influencer.location && <span>{influencer.location}</span>}
            {influencer.rating && (
              <div className="flex items-center">
                <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                {influencer.rating.toFixed(1)}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// 🔥 SKELETON COMPONENT
function InfluencerGridSkeleton() {
  return (
    <div className="flex-1 bg-muted/30 dark:bg-[#080210] overflow-auto min-h-0">
      <div className="p-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
