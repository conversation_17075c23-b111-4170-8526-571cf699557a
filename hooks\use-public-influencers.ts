// Hook para buscar influenciadores públicos (sem autenticação)
// Usado na página pública /public/[userId]/influencers

import { useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';
import { gql } from '@apollo/client';
import { toast } from 'sonner';

// 🔓 Query GraphQL pública para influenciadores
export const GET_PUBLIC_INFLUENCERS = gql`
  query GetPublicInfluencers($userId: ID!, $filters: InfluencerFilters, $pagination: PaginationInput) {
    publicInfluencers(userId: $userId, filters: $filters, pagination: $pagination) {
      totalCount
      hasNextPage
      hasPreviousPage
      nodes {
        id
        name
        avatar
        category
        country
        state
        city
        location
        bio
        totalFollowers
        engagementRate
        rating
        isVerified
        isAvailable
        
        # Redes sociais públicas
        instagramUsername
        instagramFollowers
        instagramEngagementRate
        
        tiktokUsername
        tiktokFollowers
        tiktokEngagementRate
        
        youtubeUsername
        youtubeFollowers
        youtubeSubscribers
        youtubeEngagementRate
        
        facebookUsername
        facebookFollowers
        
        twitterUsername
        twitterFollowers
        
        categories
        createdAt
        updatedAt
      }
    }
  }
`;

// Tipos para o hook
interface PublicInfluencer {
  id: string;
  name: string;
  avatar?: string;
  category?: string;
  country?: string;
  state?: string;
  city?: string;
  location?: string;
  bio?: string;
  totalFollowers: number;
  engagementRate: number;
  rating: number;
  isVerified: boolean;
  isAvailable: boolean;
  instagramUsername?: string;
  instagramFollowers?: number;
  instagramEngagementRate?: number;
  tiktokUsername?: string;
  tiktokFollowers?: number;
  tiktokEngagementRate?: number;
  youtubeUsername?: string;
  youtubeFollowers?: number;
  youtubeSubscribers?: number;
  youtubeEngagementRate?: number;
  facebookUsername?: string;
  facebookFollowers?: number;
  twitterUsername?: string;
  twitterFollowers?: number;
  categories: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface PublicInfluencerFilters {
  category?: string;
  isVerified?: boolean;
  search?: string;
  minFollowers?: number;
  maxFollowers?: number;
  minRating?: number;
  location?: string;
}

interface PaginationOptions {
  limit?: number;
  offset?: number;
}

interface UsePublicInfluencersOptions {
  userId: string;
  filters?: PublicInfluencerFilters;
  pagination?: PaginationOptions;
  enabled?: boolean;
}

interface UsePublicInfluencersResult {
  influencers: PublicInfluencer[];
  loading: boolean;
  error: any;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  refetch: () => void;
  fetchMore: (variables: any) => Promise<any>;
}

/**
 * Hook para buscar influenciadores públicos sem autenticação
 * Usado na página pública /public/[userId]/influencers
 */
export function usePublicInfluencers({
  userId,
  filters = {},
  pagination = {},
  enabled = true
}: UsePublicInfluencersOptions): UsePublicInfluencersResult {
  
  // 🔥 OTIMIZAÇÃO: Debounce para filtros de busca
  const [debouncedFilters, setDebouncedFilters] = useState(filters);
  
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 300); // 300ms de debounce para busca
    
    return () => clearTimeout(timeoutId);
  }, [filters]);

  // Query GraphQL com configurações otimizadas para acesso público
  const { 
    data, 
    loading, 
    error,
    refetch,
    fetchMore
  } = useQuery(GET_PUBLIC_INFLUENCERS, {
    variables: { 
      userId, 
      filters: debouncedFilters,
      pagination: pagination || { limit: 20, offset: 0 }
    },
    skip: !enabled || !userId || userId === 'undefined',
    fetchPolicy: 'cache-first', // 🔥 OTIMIZAÇÃO: Priorizar cache para dados públicos
    errorPolicy: 'all',
    notifyOnNetworkStatusChange: false, // 🔥 OTIMIZAÇÃO: Reduzir re-renders
    // 🚀 CACHE: Cache mais agressivo para dados públicos (5 minutos)
    pollInterval: 0, // Não fazer polling automático
    onError: (error) => {
      console.error('❌ [PUBLIC] Erro ao carregar influenciadores públicos:', error);
      toast.error('Erro ao carregar influenciadores públicos');
    }
  });

  // Processar dados da resposta
  const publicInfluencers = data?.publicInfluencers || {};
  const influencers = publicInfluencers.nodes || [];
  const totalCount = publicInfluencers.totalCount || 0;
  const hasNextPage = publicInfluencers.hasNextPage || false;
  const hasPreviousPage = publicInfluencers.hasPreviousPage || false;

  return {
    influencers,
    loading,
    error,
    totalCount,
    hasNextPage,
    hasPreviousPage,
    refetch,
    fetchMore
  };
}

/**
 * Hook simplificado para buscar um influenciador público por ID
 */
export function usePublicInfluencer(influencerId: string, userId: string) {
  const { influencers, loading, error } = usePublicInfluencers({
    userId,
    enabled: !!influencerId && !!userId
  });

  const influencer = influencers.find(inf => inf.id === influencerId) || null;

  return {
    influencer,
    loading,
    error
  };
}

/**
 * Função utilitária para verificar se um usuário tem influenciadores públicos
 */
export async function checkUserHasPublicInfluencers(userId: string): Promise<boolean> {
  try {
    // Esta função pode ser usada para validação antes de mostrar a página
    // Por enquanto, sempre retorna true - pode ser implementada com uma query específica
    return true;
  } catch (error) {
    console.error('Erro ao verificar influenciadores públicos:', error);
    return false;
  }
}
