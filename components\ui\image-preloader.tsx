'use client';

import { useEffect } from 'react';

interface ImagePreloaderProps {
  src: string;
  priority?: 'high' | 'low';
  as?: 'image';
}

/**
 * Componente para preload de imagens críticas
 * 🎯 ESTRATÉGIA: Carregar imagens críticas ANTES do LCP
 * ✅ Preload com prioridade alta
 * 🚀 Reduz tempo de carregamento do elemento LCP
 * 💡 Usa Resource Hints para otimização
 */
export function ImagePreloader({ 
  src, 
  priority = 'high',
  as = 'image' 
}: ImagePreloaderProps) {
  useEffect(() => {
    // Criar link de preload
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = as;
    link.href = src;
    
    // Definir prioridade
    if (priority === 'high') {
      link.setAttribute('fetchpriority', 'high');
    }
    
    // Adicionar ao head
    document.head.appendChild(link);
    
    // Cleanup
    return () => {
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
    };
  }, [src, priority, as]);

  return null; // Componente não renderiza nada
}

/**
 * Hook para preload de múltiplas imagens
 * 🎯 Preload em lote para otimização
 */
export function useImagePreloader(images: string[], priority: 'high' | 'low' = 'high') {
  useEffect(() => {
    const links: HTMLLinkElement[] = [];
    
    images.forEach(src => {
      if (src) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        
        if (priority === 'high') {
          link.setAttribute('fetchpriority', 'high');
        }
        
        document.head.appendChild(link);
        links.push(link);
      }
    });
    
    // Cleanup
    return () => {
      links.forEach(link => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [images, priority]);
}

/**
 * Componente para preload de domínios externos
 * 🎯 Estabelecer conexão com Firebase Storage antecipadamente
 */
export function DomainPreloader({ domains }: { domains: string[] }) {
  useEffect(() => {
    const links: HTMLLinkElement[] = [];
    
    domains.forEach(domain => {
      // Preconnect
      const preconnectLink = document.createElement('link');
      preconnectLink.rel = 'preconnect';
      preconnectLink.href = domain;
      document.head.appendChild(preconnectLink);
      links.push(preconnectLink);
      
      // DNS-prefetch como fallback
      const dnsPrefetchLink = document.createElement('link');
      dnsPrefetchLink.rel = 'dns-prefetch';
      dnsPrefetchLink.href = domain;
      document.head.appendChild(dnsPrefetchLink);
      links.push(dnsPrefetchLink);
    });
    
    return () => {
      links.forEach(link => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [domains]);

  return null;
}
