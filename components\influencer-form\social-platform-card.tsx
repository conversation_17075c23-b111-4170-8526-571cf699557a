import React, { useState } from 'react'
import { Control, useFormContext, useFieldArray } from 'react-hook-form'
import { useTranslations } from '@/hooks/use-translations'
import { X, AlertCircle, Camera, Plus, Trash2, Users, MapPin, Globe } from 'lucide-react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormattedInput } from '@/components/ui/formatted-input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ScreenshotUpload } from '@/components/ui/screenshot-upload'
import { ScreenshotAnalyzer } from '@/components/ui/screenshot-analyzer'
import { PLATFORM_CONFIG } from '@/types/influencer-form'
import type { InfluencerFormData, SocialPlatform } from '@/types/influencer-form'
import { useScreenshots } from '@/hooks/use-screenshots'
import { getPopularCountries, getAllCountries, type Country } from '@/lib/countries'
import { CountryFlag } from '@/components/ui/country-flag'
import type { ExtractedDemographics } from '@/lib/mistral-ai-service'
import { Protect } from '@clerk/nextjs'
  
interface PendingFile {
  file: File
  preview: string
  id: string
  platform: string
}

interface SocialPlatformCardProps {
  platform: string
  control: Control<InfluencerFormData>
  onRemove?: () => void
  canRemove?: boolean
  showAsTab?: boolean
  pendingFiles?: PendingFile[]
  onPendingFilesChange?: (files: PendingFile[]) => void
  onAddFilesWithPreview?: (files: File[]) => PendingFile[]
  onClearPlatformFiles?: () => void
}

// Dados auxiliares para seleção
const GENDER_OPTIONS = [
  { value: 'male', label: 'Masculino' },
  { value: 'female', label: 'Feminino' },
  { value: 'other', label: 'Outro' }
]



const COMMON_CITIES_BRAZIL = [
  'São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Salvador', 'Brasília', 
  'Fortaleza', 'Curitiba', 'Recife', 'Porto Alegre', 'Manaus', 'Belém', 
  'Goiânia', 'Guarulhos', 'Campinas', 'São Luís', 'Nova Iguaçu'
]

const AGE_RANGES = [
  '13-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+'
]

// Função para validar número de seguidores
const validateFollowers = (value: any): string | null => {
  const num = parseInt(value)
  if (isNaN(num) || num < 0) {
    return 'Número de seguidores deve ser positivo'
  }
  if (num > 1000000000) {
    return 'Número de seguidores muito alto'
  }
  return null
}

// Função para validar taxa de engajamento
const validateEngagement = (value: any): string | null => {
  // Se o campo estiver vazio, é válido (campo opcional)
  if (!value || value === '' || value === null || value === undefined) {
    return null
  }

  const num = parseFloat(value)
  if (isNaN(num) || num < 0) {
    return 'Taxa de engajamento deve ser positiva'
  }
  if (num > 100) {
    return 'Taxa de engajamento não pode ser maior que 100%'
  }
  return null
}

// Função para validar preço
const validatePrice = (value: any): string | null => {
  const num = parseFloat(value)
  if (value && (isNaN(num) || num < 0)) {
    return 'Preço deve ser positivo'
  }
  if (num > 1000000) {
    return 'Preço muito alto'
  }
  return null
}

// Função para validar percentual
const validatePercentage = (value: any): string | null => {
  const num = parseFloat(value)
  if (isNaN(num) || num < 0) {
    return 'Percentual deve ser positivo'
  }
  if (num > 100) {
    return 'Percentual não pode ser maior que 100%'
  }
  return null
}

export function SocialPlatformCard({
  platform,
  control,
  onRemove,
  canRemove = true,
  showAsTab,
  pendingFiles = [],
  onPendingFilesChange,
  onAddFilesWithPreview,
  onClearPlatformFiles
}: SocialPlatformCardProps) {
  const { register, watch, setValue, formState: { errors } } = useFormContext<InfluencerFormData>()
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({})
  const { t } = useTranslations()
  
  const config = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG]
  const platformData = watch(`platforms.${platform}` as any) as SocialPlatform | undefined
  const isMainPlatform = watch('mainPlatform') === platform
  
  // Buscar ID do influenciador para carregar screenshots existentes
  const influencerId = watch('id')
  
  // Log removido para limpar console
  
  const { screenshots: existingScreenshots, loading: loadingScreenshots, refetch: refetchScreenshots } = useScreenshots(influencerId, platform)

  // 🔥 MELHORIA: Adicionar hook para invalidar cache do Apollo
  // Função para preencher automaticamente os campos com dados extraídos do Mistral AI
  const handleDataExtracted = (extractedData: ExtractedDemographics) => {
    // Logs removidos para limpar console
    
    // Verificar se a plataforma corresponde
    if (extractedData.platform.toLowerCase() !== platform.toLowerCase()) {
      // Aviso removido para limpar console
    }
    
    // Preencher dados básicos da plataforma
    if (extractedData.username) {
      setValue(`platforms.${platform}.username` as any, extractedData.username)
    }
    
    if (extractedData.followers > 0) {
      setValue(`platforms.${platform}.followers` as any, extractedData.followers)
    }
    
    if (extractedData.engagementRate > 0) {
      setValue(`platforms.${platform}.engagementRate` as any, extractedData.engagementRate)
    }
    
    // Preencher following e posts também
    if (extractedData.following > 0) {
      setValue(`platforms.${platform}.following` as any, extractedData.following)
    }
    
    if (extractedData.posts > 0) {
      setValue(`platforms.${platform}.posts` as any, extractedData.posts)
    }
    
    // Preencher dados demográficos de gênero
    if (extractedData.audienceGender && (extractedData.audienceGender.male > 0 || extractedData.audienceGender.female > 0 || extractedData.audienceGender.other > 0)) {
      setValue(`platforms.${platform}.audienceGender` as any, extractedData.audienceGender)
    }
    
    // Preencher faixas etárias usando field array
    if (extractedData.audienceAgeRange.length > 0) {
      
      // Limpar campos existentes (remover do final para o início)
      for (let i = ageRangeFields.length - 1; i >= 0; i--) {
        removeAgeRange(i)
      }
      
      // Adicionar novos itens
      extractedData.audienceAgeRange.forEach((ageRange) => {
        if (ageRange.range && ageRange.percentage > 0) {
          appendAgeRange({ range: ageRange.range, percentage: ageRange.percentage })
        }
      })
    }
    
    // Preencher localizações por país usando field array
    if (extractedData.audienceLocations.length > 0) {
      
      // Limpar campos existentes (remover do final para o início)
      for (let i = locationFields.length - 1; i >= 0; i--) {
        removeLocation(i)
      }
      
      // Adicionar novos itens
      extractedData.audienceLocations.forEach((location) => {
        if (location.country && location.percentage > 0) {
          appendLocation({ country: location.country, percentage: location.percentage })
        }
      })
    }
    
    // Preencher cidades usando field array
    if (extractedData.audienceCities.length > 0) {
      
      // Limpar campos existentes (remover do final para o início)
      for (let i = cityFields.length - 1; i >= 0; i--) {
        removeCity(i)
      }
      
      // Adicionar novos itens
      extractedData.audienceCities.forEach((city) => {
        if (city.city && city.percentage > 0) {
          appendCity({ city: city.city, percentage: city.percentage })
        }
      })
    }
    
    // Preencher métricas específicas da plataforma
    if (extractedData.additionalMetrics) {
      const metrics = extractedData.additionalMetrics
      
      switch (platform) {
        case 'instagram':
          if (metrics.storiesViews) {
            setValue(`platforms.${platform}.metrics.storiesViews` as any, metrics.storiesViews)
          }
          if (metrics.reelsViews) {
            setValue(`platforms.${platform}.metrics.reelsViews` as any, metrics.reelsViews)
          }
          break
          
        case 'youtube':
          if (metrics.shortsViews) {
            setValue(`platforms.${platform}.metrics.shortsViews` as any, metrics.shortsViews)
          }
          if (metrics.longFormViews) {
            setValue(`platforms.${platform}.metrics.longFormViews` as any, metrics.longFormViews)
          }
          break
          
        case 'tiktok':
          if (metrics.videoViews) {
            setValue(`platforms.${platform}.metrics.videoViews` as any, metrics.videoViews)
          }
          break
      }
    }
    
    // Log removido para limpar console
    
    // Exibir toast de sucesso (assumindo que toast está disponível no contexto)
  }

  // Field arrays para dados demográficos
  const { fields: locationFields, append: appendLocation, remove: removeLocation } = useFieldArray({
    control,
    name: `platforms.${platform}.audienceLocations` as any
  })

  const { fields: cityFields, append: appendCity, remove: removeCity } = useFieldArray({
    control,
    name: `platforms.${platform}.audienceCities` as any
  })

  const { fields: ageRangeFields, append: appendAgeRange, remove: removeAgeRange } = useFieldArray({
    control,
    name: `platforms.${platform}.audienceAgeRange` as any
  })

  if (!config) {
    console.error(`Configuração não encontrada para plataforma: ${platform}`)
    return null
  }

  // Handlers com validação
  const handleFollowersChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const error = validateFollowers(value)
    
    if (error) {
      setLocalErrors(prev => ({ ...prev, followers: error }))
    } else {
      setLocalErrors(prev => {
        const { followers, ...rest } = prev
        return rest
      })
    }
  }

  const handleEngagementChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const error = validateEngagement(value)
    
    if (error) {
      setLocalErrors(prev => ({ ...prev, engagement: error }))
    } else {
      setLocalErrors(prev => {
        const { engagement, ...rest } = prev
        return rest
      })
    }
  }

  const handlePriceChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const error = validatePrice(value)
    
    if (error) {
      setLocalErrors(prev => ({ ...prev, [`price_${field}`]: error }))
    } else {
      setLocalErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[`price_${field}`]
        return newErrors
      })
    }
  }

  const handlePercentageChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const error = validatePercentage(value)
    
    if (error) {
      setLocalErrors(prev => ({ ...prev, [`percentage_${field}`]: error }))
    } else {
      setLocalErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[`percentage_${field}`]
        return newErrors
      })
    }
  }

  // Função para validar número de visualizações
  const validateViews = (value: any): string | null => {
    const num = parseInt(value)
    if (value && (isNaN(num) || num < 0)) {
      return 'Número de visualizações deve ser positivo'
    }
    if (num > 1000000000) {
      return 'Número de visualizações muito alto'
    }
    return null
  }

  const handleViewsChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const error = validateViews(value)
    
    if (error) {
      setLocalErrors(prev => ({ ...prev, [`views_${field}`]: error }))
    } else {
      setLocalErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[`views_${field}`]
        return newErrors
      })
    }
  }

  // Função para formatar número de seguidores
  const formatFollowers = (num: number | undefined): string => {
    if (!num) return '0'
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const platformErrors = errors.platforms?.[platform as keyof typeof errors.platforms]

  return (
    <Card className={`border transition-all ${isMainPlatform ? 'border-primary shadow-sm' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div 
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: config.color }}
            />
            <h3 className="font-medium">{config.name}</h3>
            {isMainPlatform && (
              <Badge variant="default" className="text-xs">Principal</Badge>
            )}
            {platformData?.followers && (
              <Badge variant="secondary" className="text-xs">
                {formatFollowers(platformData.followers)}
              </Badge>
            )}
          </div>
          
          {canRemove && !showAsTab && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onRemove} 
              className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Informações básicas */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">Informações Básicas</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="space-y-2">
              <Label htmlFor={`${platform}-username`} className="text-sm">
                Usuário
              </Label>
              <Input
                id={`${platform}-username`}
                {...register(`platforms.${platform}.username` as any)}
                placeholder="@usuario"
                className="text-sm"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor={`${platform}-followers`} className="text-sm">
                Seguidores <span className="text-red-500">*</span>
              </Label>
              <FormattedInput
                id={`${platform}-followers`}
                formatType="number"
                value={platformData?.followers || ''}
                onChange={(formattedValue, numericValue) => {
                  setValue(`platforms.${platform}.followers` as any, numericValue, { shouldValidate: true })
                  handleFollowersChange({ target: { value: numericValue.toString() } } as any)
                }}
                placeholder="0"
                className={`text-sm ${localErrors.followers ? 'border-red-500' : ''}`}
              />
              {localErrors.followers && (
                <p className="text-xs text-red-500">{localErrors.followers}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor={`${platform}-engagement`} className="text-sm">
                Engajamento (%)
              </Label>
              <Input
                id={`${platform}-engagement`}
                type="number"
                step="0.1"
                {...register(`platforms.${platform}.engagementRate` as any, {
                  setValueAs: (value) => {
                    // Se o campo estiver vazio, retornar undefined para ser tratado como opcional
                    if (!value || value === '' || value === null) {
                      return undefined
                    }
                    const num = parseFloat(value)
                    return isNaN(num) ? undefined : num
                  },
                  onChange: handleEngagementChange
                })}
                placeholder="0.0"
                className={`text-sm ${localErrors.engagement ? 'border-red-500' : ''}`}
              />
              {localErrors.engagement && (
                <p className="text-xs text-red-500">{localErrors.engagement}</p>
              )}
            </div>
          </div>
        </div>

        {/* 🔒 Preços por tipo de conteúdo - Apenas admins podem visualizar e editar */}
        <Protect role="org:admin">
        {config.fields.length > 0 && (
          <div className="space-y-3">
            <Separator />
            <h4 className="text-sm font-medium text-muted-foreground">Valores por Conteúdo</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {config.fields.map((field) => (
                <div key={field} className="space-y-1">
                  <Label className="text-xs text-muted-foreground">
                    {field === 'story' ? 'Stories' :
                     field === 'reel' ? 'Reels' :
                     field === 'shorts' ? 'Shorts' :
                     field === 'dedicated' ? 'Vídeo Dedicado' :
                     field === 'insertion' ? 'Inserção' :
                     field === 'video' ? 'Vídeo' :
                     field === 'stream' ? 'Stream' : field}
                  </Label>
                  <div className="relative">
                    <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-500 z-10">
                      R$
                    </span>
                    <FormattedInput
                      formatType="currency"
                      value={platformData?.pricing?.[field] || ''}
                      onChange={(formattedValue, numericValue) => {
                        setValue(`platforms.${platform}.pricing.${field}` as any, numericValue, { shouldValidate: true })
                        handlePriceChange(field)({ target: { value: numericValue.toString() } } as any)
                      }}
                      placeholder="0,00"
                      className={`text-sm pl-7 ${localErrors[`price_${field}`] ? 'border-red-500' : ''}`}
                    />
                  </div>
                  {localErrors[`price_${field}`] && (
                    <p className="text-xs text-red-500">{localErrors[`price_${field}`]}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        </Protect>

        {/* Visualizações por Tipo de Conteúdo */}
        {config.viewsFields && config.viewsFields.length > 0 && (
          <div className="space-y-3">
            <Separator />
            <h4 className="text-sm font-medium text-muted-foreground">Visualizações por Conteúdo</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {config.viewsFields.map((viewField) => (
                <div key={viewField.key} className="space-y-2">
                  <Label htmlFor={`${platform}-${viewField.key}`} className="text-sm">
                    {viewField.label}
                  </Label>
                  <FormattedInput
                    id={`${platform}-${viewField.key}`}
                    formatType="number"
                    value={(platformData as any)?.views?.[viewField.key] || (platformData as any)?.[viewField.key] || ''}
                    onChange={(formattedValue, numericValue) => {
                      // Para avgViews, salvar diretamente no platformData
                      if (viewField.key === 'avgViews') {
                        setValue(`platforms.${platform}.${viewField.key}` as any, numericValue, { shouldValidate: true })
                      } else {
                        // Para outros campos, salvar dentro de views
                        setValue(`platforms.${platform}.views.${viewField.key}` as any, numericValue, { shouldValidate: true })
                      }
                      handleViewsChange(viewField.key)({ target: { value: numericValue.toString() } } as any)
                    }}
                    placeholder="0"
                    className={`text-sm ${localErrors[`views_${viewField.key}`] ? 'border-red-500' : ''}`}
                  />
                  {localErrors[`views_${viewField.key}`] && (
                    <p className="text-xs text-red-500">{localErrors[`views_${viewField.key}`]}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Demografia da Audiência */}
        <div className="space-y-4">
          <Separator />
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <h4 className="text-xs font-medium text-muted-foreground">Demografia da Audiência</h4>
          </div>

          {/* Gênero da Audiência */}
          <div className="space-y-3">
            <Label className="text-xs font-medium">Distribuição por Gênero (%)</Label>
            <div className="grid grid-cols-3 gap-2">
              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Masculino</Label>
                <FormattedInput
                  formatType="percentage"
                  maxValue={100}
                  value={platformData?.audienceGender?.male || ''}
                  onChange={(formattedValue, numericValue) => {
                    setValue(`platforms.${platform}.audienceGender.male` as any, numericValue, { shouldValidate: true })
                    handlePercentageChange('male')({ target: { value: numericValue.toString() } } as any)
                  }}
                  placeholder="0"
                  className={`text-sm ${localErrors.percentage_male ? 'border-red-500' : ''}`}
                />
                {localErrors.percentage_male && (
                  <p className="text-xs text-red-500">{localErrors.percentage_male}</p>
                )}
              </div>

              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Feminino</Label>
                <FormattedInput
                  formatType="percentage"
                  maxValue={100}
                  value={platformData?.audienceGender?.female || ''}
                  onChange={(formattedValue, numericValue) => {
                    setValue(`platforms.${platform}.audienceGender.female` as any, numericValue, { shouldValidate: true })
                    handlePercentageChange('female')({ target: { value: numericValue.toString() } } as any)
                  }}
                  placeholder="0"
                  className={`text-sm ${localErrors.percentage_female ? 'border-red-500' : ''}`}
                />
                {localErrors.percentage_female && (
                  <p className="text-xs text-red-500">{localErrors.percentage_female}</p>
                )}
              </div>

              <div className="space-y-1">
                <Label className="text-xs text-muted-foreground">Outro</Label>
                <FormattedInput
                  formatType="percentage"
                  maxValue={100}
                  value={platformData?.audienceGender?.other || ''}
                  onChange={(formattedValue, numericValue) => {
                    setValue(`platforms.${platform}.audienceGender.other` as any, numericValue, { shouldValidate: true })
                    handlePercentageChange('other')({ target: { value: numericValue.toString() } } as any)
                  }}
                  placeholder="0"
                  className={`text-sm ${localErrors.percentage_other ? 'border-red-500' : ''}`}
                />
                {localErrors.percentage_other && (
                  <p className="text-xs text-red-500">{localErrors.percentage_other}</p>
                )}
              </div>
            </div>
          </div>

          {/* Localização por Países */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Globe className="h-3 w-3 text-muted-foreground" />
                <Label className="text-sm font-medium">{t('audience.countries')}</Label>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendLocation({ country: '', percentage: 0 })}
                className="h-7 gap-1"
              >
                <Plus className="h-3 w-3" />
                Adicionar
              </Button>
            </div>
            {locationFields.map((field, index) => (
              <div key={field.id} className="flex gap-2">
                <Input
                  {...register(`platforms.${platform}.audienceLocations.${index}.country` as any)}
                  placeholder="Digite o país"
                  className="flex-1 text-sm"
                />
                <div className="relative w-20">
                  <Input
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    {...register(`platforms.${platform}.audienceLocations.${index}.percentage` as any, { valueAsNumber: true })}
                    placeholder="0"
                    className="text-sm pr-6"
                  />
                  <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">%</span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeLocation(index)}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>

          {/* Localização por Cidades */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MapPin className="h-3 w-3 text-muted-foreground" />
                <Label className="text-sm font-medium">{t('audience.cities')}</Label>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendCity({ city: '', percentage: 0 })}
                className="h-7 gap-1"
              >
                <Plus className="h-3 w-3" />
                Adicionar
              </Button>
            </div>
            {cityFields.map((field, index) => (
              <div key={field.id} className="flex gap-2">
                <Input
                  {...register(`platforms.${platform}.audienceCities.${index}.city` as any)}
                  placeholder="Digite a cidade"
                  className="flex-1 text-sm"
                />
                <div className="relative w-20">
                  <Input
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    {...register(`platforms.${platform}.audienceCities.${index}.percentage` as any, { valueAsNumber: true })}
                    placeholder="0"
                    className="text-sm pr-6"
                  />
                  <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">%</span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeCity(index)}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>

          {/* Faixas Etárias */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm font-medium">Faixas Etárias (%)</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => appendAgeRange({ range: '', percentage: 0 })}
                className="h-7 gap-1"
              >
                <Plus className="h-3 w-3" />
                Adicionar
              </Button>
            </div>
            {ageRangeFields.map((field, index) => (
              <div key={field.id} className="flex gap-2">
                <Select 
                  value={watch(`platforms.${platform}.audienceAgeRange.${index}.range` as any) || ''}
                  onValueChange={(value) => 
                    setValue(`platforms.${platform}.audienceAgeRange.${index}.range` as any, value)
                  }
                >
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Selecione a faixa etária" />
                  </SelectTrigger>
                  <SelectContent>
                    {AGE_RANGES.map((range) => (
                      <SelectItem key={range} value={range}>{range} anos</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="relative w-20">
                  <Input
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    {...register(`platforms.${platform}.audienceAgeRange.${index}.percentage` as any, { valueAsNumber: true })}
                    placeholder="0"
                    className="text-sm pr-6"
                  />
                  <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground">%</span>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAgeRange(index)}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-red-500"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>

          {/* Seção de Screenshots */}
          <div className="space-y-3">
            <Separator />
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Screenshots da Plataforma</Label>
                {loadingScreenshots && (
                  <Badge variant="secondary" className="text-xs">Carregando...</Badge>
                )}
              </div>
              
              {/* Análise Inteligente com Mistral AI */}
              <ScreenshotAnalyzer
                onDataExtracted={handleDataExtracted}
              />
              
              <ScreenshotUpload
                platform={platform}
                screenshots={existingScreenshots}
                onScreenshotsUpdate={(screenshots) => {
                  // Atualizar lista local e recarregar do servidor
                  const urls = screenshots.map(s => s.url)
                  setValue(`platforms.${platform}.screenshots` as any, urls)
                  
                  // 🔥 INVALIDAR CACHE DO APOLLO para forçar refresh
                  // Recarregar screenshots do servidor após mudanças
                  setTimeout(() => {
                    refetchScreenshots()
                  }, 1000)
                }}
                pendingFiles={pendingFiles}
                onPendingFilesChange={(files) => {
                  // Usar a função callback passada como prop
                  if (onPendingFilesChange) {
                    const updatedFiles = files.map(f => ({ ...f, platform }))
                    onPendingFilesChange(updatedFiles)
                  }
                }}
                onAddFilesWithPreview={onAddFilesWithPreview}
                onClearPlatformFiles={onClearPlatformFiles}
              />
            </div>
          </div>
        </div>

        {/* Erro geral da plataforma */}
        {platformErrors && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {typeof platformErrors === 'string' 
                ? platformErrors 
                : 'Erro ao validar dados desta plataforma'}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
} 

