'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedAvatarProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackClassName?: string;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  fallbackText?: string;
}

const sizeMap = {
  sm: { size: 32, className: 'h-8 w-8' },
  md: { size: 40, className: 'h-10 w-10' },
  lg: { size: 48, className: 'h-12 w-12' },
  xl: { size: 80, className: 'h-20 w-20' }
};

/**
 * Avatar otimizado usando IMG NATIVO para eliminar srcset
 * ✅ UMA única requisição HTTP por imagem
 * 🚀 ZERO srcset responsivo - máxima performance LCP
 * 🎯 Elimina completamente múltiplas versões (16w, 32w, 48w, etc.)
 */
export function OptimizedAvatar({
  src,
  alt,
  size = 'md',
  className,
  fallbackClassName,
  loading = 'lazy',
  priority = false,
  placeholder = 'empty',
  blurDataURL,
  fallbackText
}: OptimizedAvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  
  const sizeConfig = sizeMap[size];
  const imageSize = sizeConfig.size;
  
  // Gerar iniciais para fallback
  const getInitials = (text: string) => {
    if (!text) return '??';
    return text
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(fallbackText || alt);

  // Gerar cor baseada no nome para consistência
  const getColorFromName = (name: string) => {
    const colors = [
      'from-blue-500 to-purple-600',
      'from-green-500 to-blue-600',
      'from-purple-500 to-pink-600',
      'from-orange-500 to-red-600',
      'from-teal-500 to-cyan-600',
      'from-indigo-500 to-purple-600',
      'from-pink-500 to-rose-600',
      'from-emerald-500 to-teal-600'
    ];

    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const gradientColors = getColorFromName(fallbackText || alt);

  // Otimizar URL da imagem para Firebase Storage
  const optimizeImageUrl = (url: string, targetSize: number) => {
    if (url.includes('firebasestorage.googleapis.com') || url.includes('storage.googleapis.com')) {
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}alt=media&w=${targetSize}&h=${targetSize}&fit=crop&format=webp&quality=85`;
    }
    return url;
  };

  const optimizedSrc = src ? optimizeImageUrl(src, imageSize) : undefined;

  // Container com tamanho fixo para evitar layout shift
  return (
    <div
      className={cn(
        'relative flex shrink-0 overflow-hidden rounded-full bg-gradient-to-br',
        gradientColors,
        sizeConfig.className,
        className
      )}
      role="img"
      aria-label={alt}
    >
      {optimizedSrc && !imageError ? (
        <>
          {/* 🎯 IMG NATIVO - SEM SRCSET DO NEXT.JS */}
          <img
            src={optimizedSrc}
            alt={alt}
            width={imageSize}
            height={imageSize}
            loading={loading}
            decoding={priority ? 'sync' : 'async'}
            fetchPriority={priority ? 'high' : 'auto'}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageError(true)}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center',
              display: 'block'
            }}
          />
          
          {/* Fallback enquanto carrega */}
          {!imageLoaded && (
            <div 
              className={cn(
                'absolute inset-0 flex items-center justify-center text-white font-semibold',
                size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : size === 'lg' ? 'text-base' : 'text-lg',
                fallbackClassName
              )}
            >
              {initials}
            </div>
          )}
        </>
      ) : (
        // Fallback quando não há imagem ou erro
        <div 
          className={cn(
            'flex h-full w-full items-center justify-center text-white font-semibold',
            size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : size === 'lg' ? 'text-base' : 'text-lg',
            fallbackClassName
          )}
        >
          {initials}
        </div>
      )}
    </div>
  );
}

/**
 * Avatar otimizado especificamente para influenciadores
 * ✅ SEM FALLBACK DO DB - Usa apenas placeholders locais
 * 🚀 IMG NATIVO - ZERO srcset responsivo
 * 🎯 UMA única requisição HTTP por imagem
 */
export function OptimizedInfluencerAvatar({
  influencerName,
  avatarPath,
  avatarUrl,
  className,
  fallbackClassName,
  size = 'xl',
  loading = 'lazy',
  priority = false
}: {
  influencerName: string;
  avatarPath?: string;
  avatarUrl?: string;
  className?: string;
  fallbackClassName?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: 'lazy' | 'eager';
  priority?: boolean;
}) {
  // ✅ SEM PLACEHOLDER SVG - apenas fallbacks CSS instantâneos
  const finalAvatarUrl = avatarUrl || avatarPath;

  return (
    <OptimizedAvatar
      src={finalAvatarUrl}
      alt={influencerName || 'Influenciador'}
      size={size}
      className={className}
      fallbackClassName={fallbackClassName}
      loading={loading}
      priority={priority}
      fallbackText={influencerName}
      placeholder="empty" // Simplificado para evitar problemas de build
    />
  );
}
