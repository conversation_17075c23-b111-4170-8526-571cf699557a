'use client';

import { useEffect } from 'react';

interface CriticalImagePreloaderProps {
  images: Array<{
    src: string;
    size: number;
  }>;
}

/**
 * Preloader específico para imagens críticas do LCP
 * 🎯 ESTRATÉGIA: Preload apenas das imagens above-the-fold
 * ✅ Prioridade alta para elementos críticos
 * 🚀 Reduz tempo de carregamento do LCP
 * 💡 Otimiza URLs do Firebase Storage
 */
export function CriticalImagePreloader({ images }: CriticalImagePreloaderProps) {
  useEffect(() => {
    const links: HTMLLinkElement[] = [];
    
    images.forEach(({ src, size }) => {
      if (src) {
        // Otimizar URL para Firebase Storage
        const optimizedSrc = optimizeFirebaseUrl(src, size);
        
        // Criar link de preload
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = optimizedSrc;
        link.setAttribute('fetchpriority', 'high');
        
        // Adicionar ao head
        document.head.appendChild(link);
        links.push(link);
        
        console.log(`🚀 [PRELOAD] Imagem crítica: ${optimizedSrc}`);
      }
    });
    
    // Cleanup
    return () => {
      links.forEach(link => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [images]);

  return null;
}

/**
 * Otimizar URL do Firebase Storage para tamanho específico
 */
function optimizeFirebaseUrl(url: string, targetSize: number): string {
  if (url.includes('firebasestorage.googleapis.com') || url.includes('storage.googleapis.com')) {
    // Adicionar parâmetros de otimização do Firebase
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}alt=media&w=${targetSize}&h=${targetSize}&fit=crop&format=webp&quality=85`;
  }
  
  return url;
}

/**
 * Hook para preload automático de avatares críticos
 */
export function useCriticalAvatarPreload(
  userAvatar?: string,
  influencerAvatar?: string,
  userAvatarSize: number = 32,
  influencerAvatarSize: number = 48
) {
  useEffect(() => {
    const imagesToPreload = [];
    
    if (userAvatar) {
      imagesToPreload.push({
        src: userAvatar,
        size: userAvatarSize
      });
    }
    
    if (influencerAvatar) {
      imagesToPreload.push({
        src: influencerAvatar,
        size: influencerAvatarSize
      });
    }
    
    if (imagesToPreload.length > 0) {
      const links: HTMLLinkElement[] = [];
      
      imagesToPreload.forEach(({ src, size }) => {
        const optimizedSrc = optimizeFirebaseUrl(src, size);
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = optimizedSrc;
        link.setAttribute('fetchpriority', 'high');
        
        document.head.appendChild(link);
        links.push(link);
      });
      
      return () => {
        links.forEach(link => {
          if (document.head.contains(link)) {
            document.head.removeChild(link);
          }
        });
      };
    }
  }, [userAvatar, influencerAvatar, userAvatarSize, influencerAvatarSize]);
}
