import { NextRequest, NextResponse } from 'next/server';
import { ListServiceV2 } from '@/services/list-service-v2';
import { withUserIsolation } from '@/lib/middleware/user-isolation';
import { db } from '@/lib/firebase-admin';
import { v4 as uuidv4 } from 'uuid';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

/**
 * POST /api/lists/[id]/share - Criar token de compartilhamento para lista
 */
export async function POST(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      const resolvedParams = await params;
      const { id: listId } = resolvedParams;

      try {
        console.log('[API_LIST_SHARE] Iniciando compartilhamento:', {
          listId,
          userId
        });

        // 1. Verificar se a lista existe e o usuário tem acesso
        const lista = await ListServiceV2.buscarListaPorId(listId, userId);
        if (!lista) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        // 2. Buscar itens da lista para extrair apenas os IDs
        const { itens } = await ListServiceV2.buscarItensLista(listId, userId, 1000, 0);
        
        if (itens.length === 0) {
          return NextResponse.json(
            { error: 'Lista vazia não pode ser compartilhada' },
            { status: 400 }
          );
        }

        // 3. Extrair apenas os IDs dos influenciadores
        const influencerIds = itens
          .filter(item => item.tipoItem === 'influenciador')
          .map(item => item.itemId);

        if (influencerIds.length === 0) {
          return NextResponse.json(
            { error: 'Lista não contém influenciadores para compartilhar' },
            { status: 400 }
          );
        }

        console.log('[API_LIST_SHARE] IDs extraídos:', {
          totalItens: itens.length,
          influencerIds: influencerIds.length,
          ids: influencerIds
        });

        // 4. Gerar token seguro
        const shareToken = uuidv4().replace(/-/g, '');
        
        // 5. Calcular data de expiração (7 dias)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 7);

        // 6. Salvar token no banco com estrutura simplificada
        const shareData = {
          token: shareToken,
          listId: listId,
          listName: lista.nome,
          listType: lista.tipoObjeto,
          listData: influencerIds, // Array JSON com apenas os IDs
          createdBy: userId,
          createdAt: new Date(),
          expiresAt: expiresAt,
          isActive: true,
          accessCount: 0,
          lastAccessed: null,
          // Metadados para auditoria
          originalItemCount: itens.length,
          sharedItemCount: influencerIds.length
        };

        await db.collection('share_tokens').doc(shareToken).set(shareData);

        console.log('[API_LIST_SHARE] Token criado:', {
          token: shareToken,
          expiresAt,
          itemCount: influencerIds.length
        });

        // 7. Atualizar estatísticas da lista
        try {
          await db.collection('lists').doc(listId).update({
            'estatisticas.totalCompartilhamentos': (lista.estatisticas?.totalCompartilhamentos || 0) + 1,
            ultimaAtualizacao: new Date()
          });
        } catch (updateError) {
          console.warn('[API_LIST_SHARE] Erro ao atualizar estatísticas:', updateError);
          // Não falhar a operação por causa disso
        }

        // 8. Gerar URL de compartilhamento limpa para /shared/list/[token]
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL ||
                       (req.headers.get('host') ? `https://${req.headers.get('host')}` : 'http://localhost:3000');

        // 🔧 FIX: URL limpa para página de compartilhamento sem parâmetros extras
        const shareUrl = `${baseUrl}/shared/list/${shareToken}`;

        return NextResponse.json({
          success: true,
          shareUrl,
          token: shareToken,
          expiresAt: expiresAt.toISOString(),
          itemCount: influencerIds.length,
          listName: lista.nome
        });

      } catch (error: any) {
        console.error('[API_LIST_SHARE] Erro:', {
          listId,
          userId,
          error: error.message,
          stack: error.stack
        });

        if (error?.message?.includes('não encontrada')) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        if (error?.message?.includes('permissão')) {
          return NextResponse.json(
            { error: 'Sem permissão para compartilhar esta lista' },
            { status: 403 }
          );
        }

        return NextResponse.json(
          { error: 'Erro interno ao criar compartilhamento' },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * GET /api/lists/[id]/share - Listar compartilhamentos ativos da lista
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      const resolvedParams = await params;
      const { id: listId } = resolvedParams;

      try {
        // Verificar acesso à lista
        const lista = await ListServiceV2.buscarListaPorId(listId, userId);
        if (!lista) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        // Buscar tokens ativos para esta lista
        const tokensSnapshot = await db.collection('share_tokens')
          .where('listId', '==', listId)
          .where('createdBy', '==', userId)
          .where('isActive', '==', true)
          .where('expiresAt', '>', new Date())
          .orderBy('expiresAt', 'desc')
          .orderBy('createdAt', 'desc')
          .get();

        const activeShares = tokensSnapshot.docs.map(doc => {
          const data = doc.data();
          return {
            token: data.token,
            createdAt: data.createdAt.toDate(),
            expiresAt: data.expiresAt.toDate(),
            accessCount: data.accessCount || 0,
            lastAccessed: data.lastAccessed?.toDate() || null,
            itemCount: data.sharedItemCount || 0
          };
        });

        return NextResponse.json({
          success: true,
          shares: activeShares,
          total: activeShares.length
        });

      } catch (error: any) {
        console.error('[API_LIST_SHARE_GET] Erro:', {
          listId,
          userId,
          error: error.message
        });

        return NextResponse.json(
          { error: 'Erro interno ao buscar compartilhamentos' },
          { status: 500 }
        );
      }
    }
  )(req);
}

/**
 * DELETE /api/lists/[id]/share - Revogar todos os compartilhamentos da lista
 */
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  return withUserIsolation(
    async (req: NextRequest, userId: string, context: any) => {
      const resolvedParams = await params;
      const { id: listId } = resolvedParams;

      try {
        // Verificar acesso à lista
        const lista = await ListServiceV2.buscarListaPorId(listId, userId);
        if (!lista) {
          return NextResponse.json(
            { error: 'Lista não encontrada' },
            { status: 404 }
          );
        }

        // Desativar todos os tokens da lista
        const tokensSnapshot = await db.collection('share_tokens')
          .where('listId', '==', listId)
          .where('createdBy', '==', userId)
          .where('isActive', '==', true)
          .get();

        const batch = db.batch();
        tokensSnapshot.docs.forEach(doc => {
          batch.update(doc.ref, {
            isActive: false,
            revokedAt: new Date(),
            revokedBy: userId
          });
        });

        await batch.commit();

        return NextResponse.json({
          success: true,
          message: `${tokensSnapshot.docs.length} compartilhamentos revogados`,
          revokedCount: tokensSnapshot.docs.length
        });

      } catch (error: any) {
        console.error('[API_LIST_SHARE_DELETE] Erro:', {
          listId,
          userId,
          error: error.message
        });

        return NextResponse.json(
          { error: 'Erro interno ao revogar compartilhamentos' },
          { status: 500 }
        );
      }
    }
  )(req);
}
