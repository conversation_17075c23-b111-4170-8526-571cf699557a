# Teste da Página Pública de Influenciadores

## URLs de Teste

Para testar a página pública, use as seguintes URLs:

### Desenvolvimento Local
```
http://localhost:3000/public/[userId]/influencers
```

### Exemplos com userId real
```
http://localhost:3000/public/user123/influencers
http://localhost:3000/public/test-user/influencers
```

### Com filtros
```
http://localhost:3000/public/user123/influencers?category=lifestyle&verified=true
http://localhost:3000/public/user123/influencers?influencer=inf123
```

## Funcionalidades a Testar

### ✅ Funcionalidades Básicas
- [ ] Página carrega sem autenticação
- [ ] Mostra lista de influenciadores públicos
- [ ] Header público com informações corretas
- [ ] Filtros funcionam (categoria, seguidores, rating, verificados)
- [ ] Busca por nome funciona
- [ ] Seleção de influenciador atualiza URL
- [ ] Botão de compartilhar funciona

### ✅ Segurança
- [ ] Não expõe dados sensíveis (email, telefone, whatsapp)
- [ ] Não mostra funcionalidades de edição
- [ ] Não mostra dados financeiros
- [ ] Não mostra informações de propostas

### ✅ Performance
- [ ] Página carrega rapidamente
- [ ] Cache funciona (5 minutos)
- [ ] Lazy loading dos componentes
- [ ] Debounce na busca funciona

### ✅ UI/UX
- [ ] Design responsivo
- [ ] Modo claro/escuro funciona
- [ ] Filtros são intuitivos
- [ ] Cards de influenciadores são informativos
- [ ] Loading states apropriados

## Dados de Teste

Para testar adequadamente, certifique-se de que há:

1. **Usuário com influenciadores públicos**
   - userId válido no sistema
   - Influenciadores com `isAvailable: true`
   - Dados básicos preenchidos (nome, avatar, categoria)

2. **Variedade de dados**
   - Influenciadores de diferentes categorias
   - Diferentes números de seguidores
   - Alguns verificados, outros não
   - Diferentes ratings

3. **Redes sociais**
   - Instagram, TikTok, YouTube usernames
   - Números de seguidores variados

## Possíveis Problemas

### GraphQL
- Verificar se a query `publicInfluencers` está funcionando
- Confirmar que o resolver retorna apenas dados públicos
- Testar com userId inexistente

### Middleware
- Confirmar que `/public/*` não requer autenticação
- Verificar headers de cache
- Testar com diferentes locales (/pt/, /en/)

### Componentes
- Verificar se `PublicInfluencerCard` renderiza corretamente
- Testar filtros com dados reais
- Confirmar que não há vazamentos de dados sensíveis

## Comandos de Teste

```bash
# Iniciar servidor de desenvolvimento
npm run dev

# Verificar logs do GraphQL
# Abrir DevTools > Network > GraphQL requests

# Testar diferentes cenários
curl -X POST http://localhost:3000/api/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "query { publicInfluencers(userId: \"test-user\") { totalCount nodes { id name } } }"}'
```

## Checklist de Validação

- [ ] Middleware permite acesso sem autenticação
- [ ] Query GraphQL pública funciona
- [ ] Hook `usePublicInfluencers` carrega dados
- [ ] Página renderiza sem erros
- [ ] Filtros aplicam corretamente
- [ ] Não há dados sensíveis expostos
- [ ] Performance é adequada
- [ ] Design está consistente
