// HOOK CUSTOMIZADO PARA INFLUENCIADORES VIA GRAPHQL
// Gerenciamento completo de dados de influenciadores usando Apollo Client

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { gql } from '@apollo/client';
import { toast } from 'sonner';
import { useApolloClient } from '@apollo/client';

// ===== QUERIES GRAPHQL =====

const GET_INFLUENCERS = gql`
  query GetInfluencers($userId: ID!, $filters: InfluencerFilters, $pagination: PaginationInput) {
    influencers(userId: $userId, filters: $filters, pagination: $pagination) {
      totalCount
      hasNextPage
      hasPreviousPage
      nodes {
        id
        userId
        name
        email
        phone
        whatsapp
        country
        state
        city
        location
        age
        gender
        bio
        avatar
        backgroundImage
        gradient
        category
        categories
        totalFollowers
        totalViews
        engagementRate
        rating
        isVerified
        isAvailable
        status
        # Redes sociais (campos diretos)
        instagramUsername
        instagramFollowers
        instagramEngagementRate
        instagramAvgViews
        instagramStoriesViews
        instagramReelsViews
        
        tiktokUsername
        tiktokFollowers
        tiktokEngagementRate
        tiktokAvgViews
        tiktokVideoViews
        
        youtubeUsername
        youtubeFollowers
        youtubeSubscribers
        youtubeEngagementRate
        youtubeAvgViews
        youtubeShortsViews
        youtubeLongFormViews
        
        facebookUsername
        facebookFollowers
        facebookEngagementRate
        facebookAvgViews
        
        twitchUsername
        twitchFollowers
        twitchEngagementRate
        twitchAvgViews
        
        kwaiUsername
        kwaiFollowers
        kwaiEngagementRate
        kwaiAvgViews
        
        # Configurações profissionais
        promotesTraders
        responsibleName
        agencyName
        
        # Rede social principal
        mainNetwork
        mainPlatform
        
        # Pricing denormalizado básico
        pricing {
          hasFinancialData
          priceRange
          avgPrice
          lastPriceUpdate
          isNegotiable
        }
        
        # Pricing separado (nova estrutura) - dados básicos
        currentPricing {
          id
          services {
            instagram {
              story { price currency }
              reel { price currency }
            }
            tiktok {
              video { price currency }
            }
            youtube {
              insertion { price currency }
              dedicated { price currency }
              shorts { price currency }
            }
          }
          isActive
          validFrom
          validUntil
        }
        
        # Demographics separado (nova estrutura) - dados básicos
        currentDemographics {
          id
          platform
          audienceGender {
            male
            female
            other
          }
          audienceLocations {
            country
            percentage
          }
          audienceCities {
            city
            percentage
          }
          audienceAgeRange {
            range
            percentage
          }
          isActive
          source
        }

        # 🔥 CORREÇÃO: Adicionar orçamentos organizados por plataforma
        budgets {
          instagram {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          tiktok {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          youtube {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          facebook {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          twitch {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          kwai {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
          personalizado {
            id
            amount
            currency
            status
            serviceType
            description
            createdAt
            updatedAt
          }
        }
        
        createdAt
        updatedAt
      }
    }
  }
`;

const GET_INFLUENCER_BY_ID = gql`
  query GetInfluencer($id: ID!, $userId: ID!) {
    influencer(id: $id, userId: $userId) {
      id
      userId
      name
      email
      phone
      whatsapp
      country
      state
      city
      location
      age
      gender
      bio
      avatar
      backgroundImage
      gradient
      category
      categories
      totalFollowers
      totalViews
      engagementRate
      rating
      isVerified
      isAvailable
      status
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchAvgViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiAvgViews
      
      promotesTraders
      responsibleName
      agencyName
      
      # Rede social principal
      mainNetwork
      
      # Pricing separado (nova estrutura)
      currentPricing {
        id
        services {
          instagram {
            story {
              price
              currency
            }
            reel {
              price
              currency
            }
          }
          tiktok {
            video {
              price
              currency
            }
          }
          youtube {
            insertion {
              price
              currency
            }
            dedicated {
              price
              currency
            }
            shorts {
              price
              currency
            }
          }
          facebook {
            post {
              price
              currency
            }
          }
          twitch {
            stream {
              price
              currency
            }
          }
          kwai {
            video {
              price
              currency
            }
          }
        }
        isActive
        validFrom
        validUntil
        notes
        createdAt
        updatedAt
      }
      
      # Demographics separado (nova estrutura)
      currentDemographics {
        id
        platform
        audienceGender {
          male
          female
          other
        }
        audienceLocations {
          country
          percentage
        }
        audienceCities {
          city
          percentage
        }
        audienceAgeRange {
          range
          percentage
        }
        captureDate
        isActive
        source
        createdAt
        updatedAt
      }
      
      # Fallback para estrutura antiga
      pricing {
        hasFinancialData
        priceRange
        avgPrice
        lastPriceUpdate
        isNegotiable
      }
      createdAt
      updatedAt
    }
  }
`;

const GET_CACHE_STATS = gql`
  query GetCacheStats {
    cacheStats {
      hits
      misses
      evictions
      size
      hitRate
    }
  }
`;

// ===== MUTATIONS GRAPHQL =====

const CREATE_INFLUENCER = gql`
  mutation CreateInfluencer($input: CreateInfluencerInput!) {
    createInfluencer(input: $input) {
      id
      name
      email
      country
      state
      city
      gender
      category
      categories
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      
      youtubeUsername
      youtubeFollowers
      youtubeEngagementRate
      youtubeAvgViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchAvgViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiAvgViews
      
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_INFLUENCER = gql`
  mutation UpdateInfluencer($id: ID!, $input: UpdateInfluencerInput!) {
    updateInfluencer(id: $id, input: $input) {
      id
      name
      email
      phone
      whatsapp
      country
      state
      city
      location
      age
      gender
      bio
      avatar
      category
      categories
      totalFollowers
      engagementRate
      isVerified
      isAvailable
      status
      
      # Redes sociais (campos diretos)
      instagramUsername
      instagramFollowers
      instagramEngagementRate
      instagramAvgViews
      instagramStoriesViews
      instagramReelsViews
      
      tiktokUsername
      tiktokFollowers
      tiktokEngagementRate
      tiktokAvgViews
      tiktokVideoViews
      
      youtubeUsername
      youtubeFollowers
      youtubeSubscribers
      youtubeEngagementRate
      youtubeAvgViews
      youtubeShortsViews
      youtubeLongFormViews
      
      facebookUsername
      facebookFollowers
      facebookEngagementRate
      facebookAvgViews
      
      twitchUsername
      twitchFollowers
      twitchEngagementRate
      twitchAvgViews
      
      kwaiUsername
      kwaiFollowers
      kwaiEngagementRate
      kwaiAvgViews
      
      promotesTraders
      responsibleName
      agencyName
      mainNetwork
      mainPlatform
      updatedAt
    }
  }
`;

const DELETE_INFLUENCER = gql`
  mutation DeleteInfluencer($id: ID!) {
    deleteInfluencer(id: $id)
  }
`;

// ===== INTERFACES =====

interface InfluencerFilters {
  search?: string;
  category?: string;
  isAvailable?: boolean;
  followersMin?: number;
  followersMax?: number;
  priceRange?: string;
}

interface PaginationInput {
  limit?: number;
  offset?: number;
}

interface UseInfluencersOptions {
  userId: string;
  autoFetch?: boolean;
  filters?: InfluencerFilters;
  pagination?: PaginationInput;
  infiniteScroll?: boolean;
}

// ===== HOOK PRINCIPAL =====

export function useInfluencersGraphQL(options: UseInfluencersOptions) {
  const { userId, autoFetch = true, filters, pagination, infiniteScroll = true } = options;
  
  // Estados locais
  const [localInfluencers, setLocalInfluencers] = useState<any[]>([]);
  const [selectedInfluencer, setSelectedInfluencer] = useState<any | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  
  // Query principal para buscar influenciadores
  const { 
    data: influencersData, 
    loading: influencersLoading, 
    error: influencersError,
    refetch: refetchInfluencers,
    fetchMore
  } = useQuery(GET_INFLUENCERS, {
    variables: { 
      userId, 
      filters: filters || {}, 
      pagination: pagination || { limit: 20, offset: 0 }
    },
    skip: !autoFetch || !userId,
    fetchPolicy: 'cache-and-network',
    errorPolicy: 'all',
    onCompleted: (data) => {
      console.log('🚀 [GraphQL] Influenciadores carregados:', data.influencers.nodes.length);
      console.log('🔍 [GraphQL] Dados recebidos:', data.influencers.nodes);
      console.log('🔍 [GraphQL] Filtros aplicados:', filters);
      console.log('🔍 [GraphQL] UserId:', userId);
      
      const currentOffset = pagination?.offset || 0;
      const newInfluencers = data.influencers.nodes || [];
      
      if (currentOffset === 0 || !infiniteScroll) {
        setLocalInfluencers(newInfluencers);
      } else {
        setLocalInfluencers(prevInfluencers => {
          const existingIds = new Set(prevInfluencers.map((inf: any) => inf.id));
          const newUniqueInfluencers = newInfluencers.filter((inf: any) => !existingIds.has(inf.id));
          return [...prevInfluencers, ...newUniqueInfluencers];
        });
      }
      
      setHasMoreData(data.influencers.hasNextPage);
    },
    onError: (error) => {
      console.error('❌ [GraphQL] Erro ao carregar influenciadores:', error);
      toast.error('Erro ao carregar influenciadores via GraphQL');
    }
  });

  // 🔥 NOVA FUNÇÃO: Carregar mais dados (scroll infinito)
  const loadMore = useCallback(async () => {
    if (!hasMoreData || isLoadingMore || influencersLoading) {
      console.log('🚫 [GraphQL] Load more cancelado:', { hasMoreData, isLoadingMore, loading: influencersLoading });
      return;
    }

    console.log('📄 [GraphQL] Carregando mais influenciadores...', {
      currentCount: localInfluencers.length,
      hasMoreData
    });

    setIsLoadingMore(true);

    try {
      const result = await fetchMore({
        variables: {
          userId,
          filters: filters || {},
          pagination: {
            limit: pagination?.limit || 20,
            offset: localInfluencers.length
          }
        },
        updateQuery: (previousResult, { fetchMoreResult }) => {
          if (!fetchMoreResult) return previousResult;

          const newNodes = fetchMoreResult.influencers.nodes;
          console.log('📄 [GraphQL] Novos influenciadores carregados:', newNodes.length);

          const existingIds = new Set(previousResult.influencers.nodes.map((inf: any) => inf.id));
          const uniqueNewNodes = newNodes.filter((inf: any) => !existingIds.has(inf.id));

          return {
            influencers: {
              ...fetchMoreResult.influencers,
              nodes: [...previousResult.influencers.nodes, ...uniqueNewNodes],
              totalCount: fetchMoreResult.influencers.totalCount,
              hasNextPage: fetchMoreResult.influencers.hasNextPage
            }
          };
        }
      });

      console.log('✅ [GraphQL] Load more concluído com sucesso');
    } catch (error) {
      console.error('❌ [GraphQL] Erro ao carregar mais:', error);
      toast.error('Erro ao carregar mais influenciadores');
    } finally {
      setIsLoadingMore(false);
    }
  }, [
    hasMoreData, 
    isLoadingMore, 
    influencersLoading, 
    fetchMore, 
    localInfluencers.length, 
    userId, 
    filters, 
    pagination
  ]);

  // Query para buscar estatísticas do cache
  const { data: cacheStatsData } = useQuery(GET_CACHE_STATS, {
    pollInterval: 30000,
    errorPolicy: 'ignore'
  });

  // ===== MUTATIONS =====

  const [createInfluencerMutation] = useMutation(CREATE_INFLUENCER, {
    onCompleted: (data) => {
      console.log('✅ [GraphQL] Influenciador criado:', data.createInfluencer.name);
      toast.success('Influenciador criado com sucesso via GraphQL!');
      
      setLocalInfluencers(prev => {
        const exists = prev.some((inf: any) => inf.id === data.createInfluencer.id);
        if (!exists) {
          return [data.createInfluencer, ...prev];
        }
        return prev;
      });
    },
    onError: (error) => {
      console.error('❌ [GraphQL] Erro ao criar influenciador:', error);
      toast.error('Erro ao criar influenciador via GraphQL');
    }
  });

  const [updateInfluencerMutation] = useMutation(UPDATE_INFLUENCER, {
    onCompleted: (data) => {
      console.log('✅ [GraphQL] Influenciador atualizado:', data.updateInfluencer.name);
      toast.success('Influenciador atualizado com sucesso via GraphQL!');
      
      setLocalInfluencers(prev => prev.map((inf: any) => 
        inf.id === data.updateInfluencer.id ? { ...inf, ...data.updateInfluencer } : inf
      ));
    },
    onError: (error) => {
      console.error('❌ [GraphQL] Erro ao atualizar influenciador:', error);
      toast.error('Erro ao atualizar influenciador via GraphQL');
    }
  });

  const [deleteInfluencerMutation] = useMutation(DELETE_INFLUENCER, {
    onCompleted: (data, variables) => {
      console.log('✅ [GraphQL] Influenciador deletado:', variables?.variables?.id);
      toast.success('Influenciador deletado com sucesso via GraphQL!');
      
      setLocalInfluencers(prev => prev.filter((inf: any) => inf.id !== variables?.variables?.id));
    },
    onError: (error) => {
      console.error('❌ [GraphQL] Erro ao deletar influenciador:', error);
      toast.error('Erro ao deletar influenciador via GraphQL');
    }
  });

  // ===== FUNÇÕES AUXILIARES =====

  const apolloClient = useApolloClient();

  const fetchInfluencerById = useCallback(async (id: string) => {
    try {
      if (!userId) {
        throw new Error('userId é obrigatório para buscar influenciador');
      }

      console.log(`🔍 [GraphQL] Buscando influenciador ${id} via query GraphQL`);

      const result = await apolloClient.query({
        query: GET_INFLUENCER_BY_ID,
        variables: { id, userId },
        fetchPolicy: 'network-only'
      });
      
      const influencerData = result.data?.influencer;
      
      if (influencerData) {
        console.log(`✅ [GraphQL] Influenciador encontrado: ${influencerData.name}`);
        console.log(`💰 [GraphQL] currentPricing encontrado:`, influencerData.currentPricing);
        console.log(`📊 [GraphQL] currentDemographics encontrado:`, influencerData.currentDemographics);
        setSelectedInfluencer(influencerData);
        return influencerData;
      }
      
      console.log(`⚠️ [GraphQL] Influenciador ${id} não encontrado`);
      return null;
    } catch (error) {
      console.error(`❌ [GraphQL] Erro ao buscar influenciador ${id}:`, error);
      toast.error('Erro ao buscar detalhes do influenciador');
      throw error;
    }
  }, []);

  const createInfluencer = useCallback(async (input: any) => {
    try {
      console.log('🚀 [GraphQL] Criando influenciador:', input.name);
      
      const result = await createInfluencerMutation({
        variables: { input }
      });

      return result.data?.createInfluencer;
    } catch (error) {
      console.error('❌ [GraphQL] Erro ao criar influenciador:', error);
      throw error;
    }
  }, [createInfluencerMutation]);

  const updateInfluencer = useCallback(async (id: string, input: any) => {
    try {
      console.log(`🚀 [FUNÇÃO 2 - use-influencers-graphql.ts] === INICIO updateInfluencer ===`);
      console.log(`🚀 [FUNÇÃO 2] ID recebido: ${id}`);
      console.log(`🚀 [FUNÇÃO 2] Input COMPLETO recebido:`, JSON.stringify(input, null, 2));
      
      const cleanInput = { ...input };
      
      const technicalFields = ['id', '__typename', 'createdAt', 'updatedAt', 'currentPricing', 'currentDemographics'];
      technicalFields.forEach(field => {
        if (cleanInput.hasOwnProperty(field)) {
          console.log(`🧹 [InfluencersGraphQL] Removendo campo técnico: ${field}`);
          delete cleanInput[field];
        }
      });
      
      console.log(`🔍 [FUNÇÃO 2] Verificando audienceLocations:`, cleanInput.audienceLocations);
      if (cleanInput.audienceLocations && Array.isArray(cleanInput.audienceLocations)) {
        console.log(`🚨 [FUNÇÃO 2] ENCONTROU audienceLocations com ${cleanInput.audienceLocations.length} itens`);
        cleanInput.audienceLocations = cleanInput.audienceLocations.map((item: any) => {
          const cleanItem = { ...item };
          console.log(`🗑️ [FUNÇÃO 2] ANTES da limpeza:`, item);
          delete cleanItem.__typename;
          console.log(`✅ [FUNÇÃO 2] DEPOIS da limpeza:`, cleanItem);
          return cleanItem;
        });
      }
      
      if (cleanInput.audienceCities && Array.isArray(cleanInput.audienceCities)) {
        cleanInput.audienceCities = cleanInput.audienceCities.map((item: any) => {
          const cleanItem = { ...item };
          delete cleanItem.__typename;
          console.log(`🧹 [InfluencersGraphQL] Removendo __typename de audienceCities:`, cleanItem);
          return cleanItem;
        });
      }
      
      console.log('🔧 [GraphQL Hook] Input LIMPO final:', JSON.stringify(cleanInput, null, 2));
      console.log('🔧 [GraphQL Hook] Mutation variables:', { id, input: cleanInput });
      
      const result = await updateInfluencerMutation({
        variables: { id, input: cleanInput }
      });

      console.log('✅ [GraphQL Hook] Mutation executada com sucesso');
      console.log('✅ [GraphQL Hook] Resultado:', result.data?.updateInfluencer);
      
      return result.data?.updateInfluencer;
    } catch (error) {
      console.error('❌ [GraphQL Hook] Erro na mutation:', error);
      throw error;
    }
  }, [updateInfluencerMutation]);

  const deleteInfluencer = useCallback(async (id: string) => {
    try {
      console.log(`🗑️ [GraphQL] Deletando influenciador ${id}`);
      
      const result = await deleteInfluencerMutation({
        variables: { id }
      });

      return result.data?.deleteInfluencer;
    } catch (error) {
      console.error('❌ [GraphQL] Erro ao deletar influenciador:', error);
      throw error;
    }
  }, [deleteInfluencerMutation]);

  const refreshData = useCallback(async () => {
    console.log('🔄 [GraphQL] Atualizando dados...');
    try {
      setLocalInfluencers([]);
      setHasMoreData(true);
      await refetchInfluencers();
    } catch (error) {
      console.error('❌ [GraphQL] Erro ao atualizar dados:', error);
      toast.error('Erro ao atualizar dados');
    }
  }, [refetchInfluencers]);

  // 🔥 CORREÇÃO: Memoizar os influenciadores para evitar re-renders desnecessários
  const memoizedInfluencers = useMemo(() => {
    const uniqueInfluencers = localInfluencers.reduce((acc, current) => {
      const existingInfluencer = acc.find((inf: any) => inf.id === current.id);
      if (!existingInfluencer) {
        acc.push(current);
      }
      return acc;
    }, [] as any[]);
    
    if (localInfluencers.length !== uniqueInfluencers.length) {
      console.warn(`🚨 [GraphQL Hook] ${localInfluencers.length - uniqueInfluencers.length} influenciadores duplicados removidos no hook!`);
    }
    
    return uniqueInfluencers;
  }, [localInfluencers]);

  // ===== RETORNO DO HOOK =====

  return {
    influencers: memoizedInfluencers,
    totalCount: influencersData?.influencers?.totalCount || 0,
    hasNextPage: hasMoreData,
    hasPreviousPage: influencersData?.influencers?.hasPreviousPage || false,
    selectedInfluencer,
    cacheStats: cacheStatsData?.cacheStats,
    
    loading: influencersLoading,
    loadingMore: isLoadingMore,
    error: influencersError,
    
    fetchInfluencerById,
    createInfluencer,
    updateInfluencer,
    deleteInfluencer,
    refreshData,
    setSelectedInfluencer,
    loadMore,
    
    refetch: refetchInfluencers
  };
}

export default useInfluencersGraphQL; 

