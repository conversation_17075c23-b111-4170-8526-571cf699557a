/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Configuração de source maps para desenvolvimento
  productionBrowserSourceMaps: false,
  generateEtags: false,
  // Configuração de i18n removida - usando sistema customizado
  // Adicionar transpilePackages para o Firebase e suas dependências
  transpilePackages: [
    'firebase',
    '@firebase/app',
    '@firebase/auth',
    '@firebase/storage',
    '@firebase/firestore',
    '@firebase/functions',
    '@firebase/database',
    '@firebase/remote-config',
    '@firebase/performance',
    '@firebase/util'
  ],
  // Configuração para lidar com páginas que usam recursos dinâmicos
  // Remover output: 'export' para permitir recursos dinâmicos
  staticPageGenerationTimeout: 1000,
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    // 🎯 SOLUÇÃO RADICAL: Desabilitar completamente otimizações do Next.js
    // para eliminar srcset responsivo que causa múltiplas requisições
    unoptimized: true,

    // Domínios permitidos para imagens externas
    domains: [
      'firebasestorage.googleapis.com',
      'storage.googleapis.com',
      'lh3.googleusercontent.com', // Clerk avatars
      'img.clerk.com' // Clerk avatars
    ],

    // Configuração mínima para evitar srcset
    deviceSizes: [640], // Apenas um tamanho
    imageSizes: [64],   // Apenas um tamanho pequeno
  },
  serverExternalPackages: ['firebase-admin'],
  // Configurar headers para reduzir requisições desnecessárias
  async headers() {
    return [
      {
        source: '/.well-known/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
    ]
  },
  webpack: (config, { dev, isServer }) => {
    // Configuração para o lado servidor
    if (isServer) {
      config.externals.push('firebase-admin')
    }
    
    // Melhorar source maps em desenvolvimento
    if (dev) {
      config.devtool = 'eval-source-map'
    }
    
    // Configuração simplificada para resolver problemas com Firebase Storage
    config.resolve.fallback = {
      ...config.resolve.fallback,
      "fs": false,
      "net": false,
      "tls": false,
    }
    
    // Ignorar módulos problemáticos especificamente para Firebase Storage
    config.module.rules.push({
      test: /node_modules\/firebase\/storage\/dist\/index\.mjs$/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false
      }
    })
    
    // Configurar parser geral para módulos ES do Firebase
    config.module.rules.push({
      test: /\.m?js$/,
      include: /node_modules\/firebase/,
      type: 'javascript/auto',
      resolve: {
        fullySpecified: false,
      },
    })
    
    // Configuração para arquivos SVG
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack']
    })
    
    return config
  },
}

export default nextConfig