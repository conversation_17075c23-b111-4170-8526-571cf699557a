import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { ServicePackage, PackageService } from '@/types/service-package';
import { ServiceType } from '@/types/budget';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [POST] Recebendo dados do pacote de serviços');

    let body: {
      name: string;
      description?: string;
      totalPrice: number;
      isTemplate?: boolean;
      category?: string;
      tags?: string[];
      minFollowers?: number;
      platforms: string[];
      services: PackageService[];
      influencerId: string;
      influencerName: string;
      proposalId: string;
      userId: string;
      brandId: string;
    };

    try {
      body = await request.json();
      console.log('📦 [POST] Body parseado com sucesso');
    } catch (parseError) {
      console.error('❌ [POST] Erro ao parsear JSON:', parseError);
      return NextResponse.json(
        { error: 'Dados JSON inválidos' },
        { status: 400 }
      );
    }
    
    console.log('📦 [POST] Dados recebidos:', {
      name: body.name,
      influencerId: body.influencerId,
      proposalId: body.proposalId,
      servicesCount: body.services?.length || 0,
      userId: body.userId,
      brandId: body.brandId,
      totalPrice: body.totalPrice,
      platforms: body.platforms
    });

    // Log detalhado dos serviços
    if (body.services && body.services.length > 0) {
      console.log('🔧 [POST] Serviços detalhados:');
      body.services.forEach((service, index) => {
        console.log(`  ${index + 1}. ${service.serviceName} (${service.platform}): ${service.quantity}x R$${service.unitPrice} = R$${service.totalPrice}`);
      });
    }

    console.log('🔍 [POST] Iniciando validações...');

    // Validação de campos obrigatórios
    if (!body.name?.trim()) {
      console.log('❌ [POST] Nome do pacote não fornecido');
      return NextResponse.json(
        { error: 'Nome do pacote é obrigatório' },
        { status: 400 }
      );
    }

    if (!body.influencerId || !body.proposalId || !body.userId || !body.brandId) {
      return NextResponse.json(
        { error: 'Campos obrigatórios: influencerId, proposalId, userId, brandId' },
        { status: 400 }
      );
    }

    if (!body.services || body.services.length === 0) {
      return NextResponse.json(
        { error: 'Pelo menos um serviço deve ser selecionado' },
        { status: 400 }
      );
    }

    // Validar estrutura dos serviços
    console.log('🔍 [POST] Validando estrutura dos serviços...');
    for (let i = 0; i < body.services.length; i++) {
      const service = body.services[i];
      console.log(`🔧 [POST] Validando serviço ${i + 1}:`, {
        serviceType: service.serviceType,
        platform: service.platform,
        serviceName: service.serviceName,
        quantity: service.quantity,
        unitPrice: service.unitPrice,
        totalPrice: service.totalPrice
      });

      if (!service.serviceType || !service.platform) {
        console.log(`❌ [POST] Serviço ${i + 1} inválido - faltam campos obrigatórios`);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: serviceType e platform são obrigatórios` },
          { status: 400 }
        );
      }

      if (typeof service.quantity !== 'number' || service.quantity <= 0) {
        console.log(`❌ [POST] Serviço ${i + 1} - quantidade inválida:`, service.quantity);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: Quantidade deve ser um número maior que 0` },
          { status: 400 }
        );
      }

      if (typeof service.unitPrice !== 'number' || service.unitPrice < 0) {
        console.log(`❌ [POST] Serviço ${i + 1} - preço unitário inválido:`, service.unitPrice);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: Preço unitário deve ser um número não negativo` },
          { status: 400 }
        );
      }

      // Validar se totalPrice está correto
      const expectedTotal = service.quantity * service.unitPrice;
      if (typeof service.totalPrice !== 'number' || Math.abs(service.totalPrice - expectedTotal) > 0.01) {
        console.log(`❌ [POST] Serviço ${i + 1} - preço total incorreto. Esperado: ${expectedTotal}, Recebido: ${service.totalPrice}`);
        return NextResponse.json(
          { error: `Serviço ${i + 1}: Preço total incorreto. Esperado: R$${expectedTotal.toFixed(2)}` },
          { status: 400 }
        );
      }
    }
    console.log('✅ [POST] Todos os serviços validados com sucesso');

    // Calcular preço total do pacote
    const calculatedTotalPrice = body.services.reduce((total, service) => total + service.totalPrice, 0);
    
    if (Math.abs(body.totalPrice - calculatedTotalPrice) > 0.01) {
      return NextResponse.json(
        { error: 'Preço total do pacote não confere com a soma dos serviços' },
        { status: 400 }
      );
    }

    console.log('🔍 [POST] Verificando proposta:', body.proposalId);

    // Verificar se a proposta existe e o usuário tem acesso
    let proposalDoc;
    try {
      proposalDoc = await db.collection('proposals').doc(body.proposalId).get();
      console.log('📄 [POST] Proposta encontrada:', proposalDoc.exists);
    } catch (dbError) {
      console.error('❌ [POST] Erro ao acessar Firebase:', dbError);
      return NextResponse.json(
        { error: 'Erro ao acessar banco de dados' },
        { status: 500 }
      );
    }

    if (!proposalDoc.exists) {
      console.log('❌ [POST] Proposta não encontrada:', body.proposalId);
      return NextResponse.json(
        { error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    const proposalData = proposalDoc.data();
    console.log('📄 [POST] Dados da proposta carregados, owner:', proposalData?.criadoPor);
    
    // Verificar permissões (owner ou colaborador)
    const isOwner = proposalData?.criadoPor === body.userId;
    let isCollaborator = false;

    console.log('🔐 [POST] Verificando permissões:', {
      userId: body.userId,
      proposalOwner: proposalData?.criadoPor,
      isOwner
    });

    if (!isOwner && proposalData?.collaborators) {
      isCollaborator = proposalData.collaborators.some((collab: any) =>
        collab.email === body.userId || collab.userId === body.userId
      );
      console.log('👥 [POST] Verificação de colaborador:', { isCollaborator, collaboratorsCount: proposalData.collaborators.length });
    }

    if (!isOwner && !isCollaborator) {
      console.log('❌ [POST] Acesso negado - usuário sem permissão');
      return NextResponse.json(
        { error: 'Acesso negado à proposta' },
        { status: 403 }
      );
    }

    console.log('✅ [POST] Permissões verificadas com sucesso');

    // Verificar se o influenciador existe na proposta
    console.log('👤 [POST] Verificando se influenciador existe na proposta...');
    try {
      const influencerDoc = await db
        .collection('proposals')
        .doc(body.proposalId)
        .collection('influencers')
        .doc(body.influencerId)
        .get();

      if (!influencerDoc.exists) {
        console.log('❌ [POST] Influenciador não encontrado na proposta');
        return NextResponse.json(
          { error: 'Influenciador não encontrado na proposta' },
          { status: 404 }
        );
      }
      console.log('✅ [POST] Influenciador encontrado na proposta');
    } catch (influencerError) {
      console.error('❌ [POST] Erro ao verificar influenciador:', influencerError);
      return NextResponse.json(
        { error: 'Erro ao verificar influenciador na proposta' },
        { status: 500 }
      );
    }

    console.log('🔧 [POST] Preparando dados do pacote...');

    // Preparar dados do pacote para salvar na subcoleção budgets
    const packageData: any = {
      // Identificação
      type: 'package', // Diferencia de budgets individuais
      packageName: body.name.trim(),
      description: body.description || '',

      // Relacionamentos
      influencerId: body.influencerId,
      influencerName: body.influencerName || '',
      userId: body.userId,
      brandId: body.brandId,
      proposalId: body.proposalId,

      // Dados financeiros
      amount: body.totalPrice,
      currency: 'BRL',

      // Serviços do pacote - limpar campos undefined
      services: body.services.map(service => ({
        serviceType: service.serviceType,
        quantity: service.quantity,
        unitPrice: service.unitPrice,
        totalPrice: service.totalPrice,
        serviceName: service.serviceName || '',
        platform: service.platform,
        originalPrice: service.originalPrice || service.unitPrice
      })),
      platforms: body.platforms || [...new Set(body.services.map(s => s.platform))],

      // Configurações do pacote - garantir que não há undefined
      isTemplate: body.isTemplate === true,
      category: body.category || '',
      tags: body.tags || [],

      // Status e workflow
      status: 'draft',
      serviceType: 'package',

      // Contrapropostas (seguindo padrão dos budgets)
      counterProposals: [],
      hasCounterProposal: false,

      // Metadados
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: body.userId
    };

    // Adicionar campos opcionais apenas se tiverem valor
    if (body.minFollowers && body.minFollowers > 0) {
      packageData.minFollowers = body.minFollowers;
    }

    console.log('📋 [POST] Dados preparados:', {
      type: packageData.type,
      packageName: packageData.packageName,
      servicesCount: packageData.services.length,
      amount: packageData.amount,
      hasUndefinedFields: Object.values(packageData).some(v => v === undefined)
    });

    console.log('💾 [POST] Salvando pacote na estrutura hierárquica:', {
      proposalId: body.proposalId,
      influencerId: body.influencerId,
      packageName: body.name
    });

    // Salvar na mesma estrutura dos budgets: proposals/{proposalId}/influencers/{influencerId}/budgets/{packageId}
    let packageRef;
    try {
      console.log('💾 [POST] Iniciando salvamento no Firebase...');

      // Validar se todos os campos obrigatórios estão presentes
      const requiredFields = ['type', 'packageName', 'influencerId', 'userId', 'brandId', 'amount', 'services'];
      const missingFields = requiredFields.filter(field => !packageData[field]);

      if (missingFields.length > 0) {
        console.error('❌ [POST] Campos obrigatórios faltando:', missingFields);
        return NextResponse.json(
          { error: `Campos obrigatórios faltando: ${missingFields.join(', ')}` },
          { status: 400 }
        );
      }

      packageRef = await db
        .collection('proposals')
        .doc(body.proposalId)
        .collection('influencers')
        .doc(body.influencerId)
        .collection('budgets')
        .add(packageData);

      console.log('✅ [POST] Pacote salvo com ID:', packageRef.id);
    } catch (saveError: any) {
      console.error('❌ [POST] Erro detalhado ao salvar no Firebase:', {
        error: saveError.message,
        code: saveError.code,
        stack: saveError.stack,
        packageDataKeys: Object.keys(packageData),
        proposalId: body.proposalId,
        influencerId: body.influencerId
      });

      return NextResponse.json(
        {
          error: 'Erro ao salvar pacote no banco de dados',
          details: saveError.message
        },
        { status: 500 }
      );
    }

    // Retornar dados do pacote criado
    const createdPackage = {
      id: packageRef.id,
      ...packageData,
      createdAt: new Date(packageData.createdAt),
      updatedAt: new Date(packageData.updatedAt)
    };

    return NextResponse.json({
      success: true,
      message: 'Pacote de serviços criado com sucesso',
      package: createdPackage
    });

  } catch (error: any) {
    console.error('❌ [POST] Erro geral ao salvar pacote de serviços:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verificar se o Firebase está inicializado
    console.log('🔥 [GET] Verificando inicialização do Firebase...');
    if (!db) {
      console.error('❌ [GET] Firebase não inicializado');
      return NextResponse.json(
        { error: 'Erro de configuração do banco de dados' },
        { status: 500 }
      );
    }
    console.log('✅ [GET] Firebase inicializado com sucesso');

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const proposalId = searchParams.get('proposalId');
    const influencerId = searchParams.get('influencerId');

    console.log('📊 [GET] Buscando pacotes de serviços:', { userId, proposalId, influencerId });

    if (!userId) {
      return NextResponse.json(
        { error: 'userId é obrigatório' },
        { status: 400 }
      );
    }

    if (!proposalId || !influencerId) {
      return NextResponse.json(
        { error: 'proposalId e influencerId são obrigatórios' },
        { status: 400 }
      );
    }

    // Verificar acesso à proposta
    console.log('🔍 [GET] Verificando proposta...');
    let proposalDoc;
    try {
      proposalDoc = await db.collection('proposals').doc(proposalId).get();
      console.log('✅ [GET] Proposta verificada, existe:', proposalDoc.exists);
    } catch (proposalError: any) {
      console.error('❌ [GET] Erro ao verificar proposta:', proposalError);
      return NextResponse.json(
        { error: 'Erro ao verificar proposta', details: proposalError.message },
        { status: 500 }
      );
    }

    if (!proposalDoc.exists) {
      return NextResponse.json(
        { error: 'Proposta não encontrada' },
        { status: 404 }
      );
    }

    const proposalData = proposalDoc.data();
    const isOwner = proposalData?.criadoPor === userId;
    let isCollaborator = false;
    
    if (!isOwner && proposalData?.collaborators) {
      isCollaborator = proposalData.collaborators.some((collab: any) => 
        collab.email === userId || collab.userId === userId
      );
    }
    
    if (!isOwner && !isCollaborator) {
      return NextResponse.json({
        success: true,
        packages: [],
        total: 0,
        error: 'Acesso negado à proposta'
      });
    }

    // Buscar pacotes na subcoleção budgets (filtrar por type: 'package')
    console.log('🔍 [GET] Iniciando query no Firestore...');
    console.log('📍 [GET] Path:', `proposals/${proposalId}/influencers/${influencerId}/budgets`);

    let packagesSnapshot;
    try {
      // Primeiro, tentar query simples sem orderBy para evitar erros de índice
      packagesSnapshot = await db
        .collection('proposals')
        .doc(proposalId)
        .collection('influencers')
        .doc(influencerId)
        .collection('budgets')
        .where('type', '==', 'package')
        .get();

      console.log('✅ [GET] Query executada, documentos encontrados:', packagesSnapshot.size);
    } catch (queryError: any) {
      console.error('❌ [GET] Erro na query do Firestore:', queryError);

      // Se a query falhar, tentar buscar todos os budgets e filtrar manualmente
      try {
        console.log('🔄 [GET] Tentando query alternativa...');
        const allBudgetsSnapshot = await db
          .collection('proposals')
          .doc(proposalId)
          .collection('influencers')
          .doc(influencerId)
          .collection('budgets')
          .get();

        console.log('📊 [GET] Total de budgets encontrados:', allBudgetsSnapshot.size);

        // Filtrar manualmente por type: 'package'
        const packageDocs = allBudgetsSnapshot.docs.filter(doc => {
          const data = doc.data();
          return data.type === 'package';
        });

        console.log('📦 [GET] Pacotes filtrados manualmente:', packageDocs.length);

        // Criar um snapshot mock com os documentos filtrados
        packagesSnapshot = {
          docs: packageDocs,
          size: packageDocs.length,
          empty: packageDocs.length === 0
        };

      } catch (fallbackError: any) {
        console.error('❌ [GET] Erro na query alternativa:', fallbackError);
        // Retornar lista vazia ao invés de erro
        return NextResponse.json({
          success: true,
          packages: [],
          total: 0,
          message: 'Nenhum pacote encontrado ou erro na consulta'
        });
      }
    }

    const packages = packagesSnapshot.docs.map(doc => {
      const data = doc.data();

      // Processar dados de forma mais robusta
      const services = data.services || [];

      // Calcular valor total do pacote
      let totalPrice = data.amount || data.totalPrice || 0;

      // Se não tiver valor definido, calcular pela soma dos serviços
      if (totalPrice === 0 && services.length > 0) {
        totalPrice = services.reduce((sum: number, service: any) => {
          const serviceTotal = (service.quantity || 1) * (service.unitPrice || service.totalPrice || 0);
          return sum + serviceTotal;
        }, 0);
      }

      const processedData = {
        id: doc.id,
        name: data.packageName || data.name || 'Pacote sem nome',
        description: data.description || '',
        type: data.type || 'package',
        amount: data.amount || totalPrice,
        totalPrice: totalPrice,
        services: services,
        platforms: data.platforms || [],
        influencerId: data.influencerId || '',
        userId: data.userId || '',
        brandId: data.brandId || '',
        createdAt: data.createdAt ? new Date(data.createdAt) : new Date(),
        updatedAt: data.updatedAt ? new Date(data.updatedAt) : new Date(),
        // Incluir todos os outros campos que possam existir
        ...data
      };

      console.log('📦 [GET] Pacote processado:', {
        id: processedData.id,
        name: processedData.name,
        servicesCount: processedData.services.length,
        amount: processedData.amount,
        totalPrice: processedData.totalPrice
      });

      return processedData;
    });

    console.log('✅ [GET] Pacotes encontrados:', packages.length);

    return NextResponse.json({
      success: true,
      packages,
      total: packages.length
    });

  } catch (error: any) {
    console.error('❌ [GET] Erro detalhado ao buscar pacotes:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code
    });
    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        details: error.message,
        code: error.code
      },
      { status: 500 }
    );
  }
}

// 🆕 PUT - Atualizar status do pacote (aceitar/recusar)
export async function PUT(request: NextRequest) {
  try {
    console.log('🔄 [PUT] Iniciando atualização de pacote...');

    const body = await request.json();
    const { influencerId, packageId, proposalId, action, userId } = body;

    console.log('📋 [PUT] Dados recebidos:', {
      influencerId,
      packageId,
      proposalId,
      action,
      userId
    });

    // Validar dados obrigatórios
    if (!influencerId || !packageId || !action || !userId) {
      console.error('❌ [PUT] Dados obrigatórios ausentes');
      return NextResponse.json(
        { error: 'Dados obrigatórios ausentes' },
        { status: 400 }
      );
    }

    // Validar ação
    if (!['accept_package', 'reject_package'].includes(action)) {
      console.error('❌ [PUT] Ação inválida:', action);
      return NextResponse.json(
        { error: 'Ação inválida' },
        { status: 400 }
      );
    }

    // Verificar se o Firebase está inicializado
    if (!db) {
      console.error('❌ [PUT] Firebase não inicializado');
      return NextResponse.json(
        { error: 'Erro de configuração do banco de dados' },
        { status: 500 }
      );
    }

    // Determinar novo status baseado na ação
    const newStatus = action === 'accept_package' ? 'accepted' : 'rejected';

    console.log('🔄 [PUT] Atualizando status do pacote para:', newStatus);

    // Atualizar o documento do pacote
    const packageRef = db
      .collection('proposals')
      .doc(proposalId || 'default')
      .collection('influencers')
      .doc(influencerId)
      .collection('budgets')
      .doc(packageId);

    await packageRef.update({
      status: newStatus,
      updatedAt: new Date().toISOString(),
      updatedBy: userId,
      [`${action}_at`]: new Date().toISOString(),
      [`${action}_by`]: userId
    });

    console.log('✅ [PUT] Pacote atualizado com sucesso');

    return NextResponse.json({
      success: true,
      message: `Pacote ${newStatus === 'accepted' ? 'aceito' : 'recusado'} com sucesso`,
      packageId,
      status: newStatus,
      updatedAt: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('❌ [PUT] Erro ao atualizar pacote:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code
    });

    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        details: error.message,
        code: error.code
      },
      { status: 500 }
    );
  }
}
