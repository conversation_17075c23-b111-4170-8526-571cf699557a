import { NextRequest, NextResponse } from 'next/server';
import { CategoryService } from '@/services/category-service-unified';

/**
 * 🔓 ROTA GET PÚBLICA - Buscar categorias públicas sem autenticação
 * Para uso em páginas de compartilhamento e outras páginas públicas
 */
export async function GET(req: NextRequest) {
  try {
    console.log('🔓 [PUBLIC] Buscando categorias públicas');
    
    // Buscar apenas categorias públicas básicas
    const publicCategories = [
      { 
        id: 'all', 
        name: 'Todos', 
        slug: 'all', 
        description: 'Todos os influenciadores', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      },
      { 
        id: 'lifestyle', 
        name: 'Lifestyle', 
        slug: 'lifestyle', 
        description: 'Conteúdo de lifestyle', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      },
      { 
        id: 'fashion', 
        name: '<PERSON><PERSON>', 
        slug: 'fashion', 
        description: 'Conteúdo de moda', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      },
      { 
        id: 'beauty', 
        name: 'Beleza', 
        slug: 'beauty', 
        description: 'Conteúdo de beleza', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      },
      { 
        id: 'fitness', 
        name: 'Fitness', 
        slug: 'fitness', 
        description: 'Conteúdo de fitness e saúde', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      },
      { 
        id: 'food', 
        name: 'Gastronomia', 
        slug: 'food', 
        description: 'Conteúdo gastronômico', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      },
      { 
        id: 'travel', 
        name: 'Viagem', 
        slug: 'travel', 
        description: 'Conteúdo de viagem', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      },
      { 
        id: 'tech', 
        name: 'Tecnologia', 
        slug: 'tech', 
        description: 'Conteúdo de tecnologia', 
        count: null, 
        userId: null, 
        isActive: true, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      }
    ];

    console.log(`✅ [PUBLIC] ${publicCategories.length} categorias públicas retornadas`);
    
    return NextResponse.json(publicCategories);
    
  } catch (error: any) {
    console.error('❌ [PUBLIC] Erro ao buscar categorias públicas:', error);
    
    return NextResponse.json(
      { error: 'Erro ao buscar categorias públicas' },
      { status: 500 }
    );
  }
}
