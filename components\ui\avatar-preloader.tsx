'use client';

import { useEffect } from 'react';

interface AvatarPreloaderProps {
  avatarUrls: string[];
  priority?: boolean;
}

/**
 * Componente para preload de avatares críticos
 * Melhora LCP precarregando imagens que aparecerão above-the-fold
 */
export function AvatarPreloader({ avatarUrls, priority = true }: AvatarPreloaderProps) {
  useEffect(() => {
    // Preload apenas avatares válidos
    const validUrls = avatarUrls.filter(url =>
      url &&
      url.trim() !== '' &&
      !url.startsWith('data:')
    );

    if (validUrls.length === 0) return;

    // Criar links de preload
    const preloadLinks: HTMLLinkElement[] = [];

    validUrls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      
      if (priority) {
        link.setAttribute('fetchpriority', 'high');
      }

      // Adicionar ao head
      document.head.appendChild(link);
      preloadLinks.push(link);
    });

    // Cleanup: remover links quando componente desmonta
    return () => {
      preloadLinks.forEach(link => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });
    };
  }, [avatarUrls, priority]);

  // Componente não renderiza nada visualmente
  return null;
}

/**
 * Hook para preload de avatares
 * Uso: const preloadAvatars = useAvatarPreload();
 *      preloadAvatars([url1, url2, url3]);
 */
export function useAvatarPreload() {
  return (urls: string[], priority = true) => {
    const validUrls = urls.filter(url =>
      url &&
      url.trim() !== '' &&
      !url.startsWith('data:')
    );

    validUrls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      
      if (priority) {
        link.setAttribute('fetchpriority', 'high');
      }

      // Verificar se já existe antes de adicionar
      const existingLink = document.querySelector(`link[href="${url}"]`);
      if (!existingLink) {
        document.head.appendChild(link);
      }
    });
  };
}
