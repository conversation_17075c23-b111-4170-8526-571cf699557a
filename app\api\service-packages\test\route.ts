import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 [TEST] Iniciando teste de salvamento de pacote');
    
    const body = await request.json();
    console.log('📦 [TEST] Dados recebidos:', body);

    // Teste simples de conexão com Firebase
    try {
      const testDoc = await db.collection('test').add({
        message: 'Teste de conexão',
        timestamp: new Date().toISOString()
      });
      console.log('✅ [TEST] Conexão Firebase OK, doc ID:', testDoc.id);
      
      // Remover documento de teste
      await testDoc.delete();
      console.log('🗑️ [TEST] Documento de teste removido');
    } catch (firebaseError: any) {
      console.error('❌ [TEST] Erro de conexão Firebase:', firebaseError);
      return NextResponse.json({
        success: false,
        error: 'Erro de conexão Firebase',
        details: firebaseError.message
      });
    }

    // Teste de salvamento na estrutura real
    const testPackageData = {
      type: 'package',
      packageName: 'Teste Package',
      description: 'Pacote de teste',
      influencerId: body.influencerId || 'test-influencer',
      userId: body.userId || 'test-user',
      brandId: body.brandId || 'test-brand',
      proposalId: body.proposalId || 'test-proposal',
      amount: 100,
      currency: 'BRL',
      services: [{
        serviceType: 'instagram_story',
        quantity: 1,
        unitPrice: 100,
        totalPrice: 100,
        serviceName: 'Instagram Stories',
        platform: 'instagram'
      }],
      platforms: ['instagram'],
      status: 'draft',
      serviceType: 'package',
      counterProposals: [],
      hasCounterProposal: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: body.userId || 'test-user'
    };

    console.log('💾 [TEST] Tentando salvar na estrutura real...');
    
    try {
      const packageRef = await db
        .collection('proposals')
        .doc(body.proposalId || 'test-proposal')
        .collection('influencers')
        .doc(body.influencerId || 'test-influencer')
        .collection('budgets')
        .add(testPackageData);

      console.log('✅ [TEST] Pacote de teste salvo com ID:', packageRef.id);
      
      // Remover documento de teste
      await packageRef.delete();
      console.log('🗑️ [TEST] Pacote de teste removido');

      return NextResponse.json({
        success: true,
        message: 'Teste de salvamento bem-sucedido',
        testDocId: packageRef.id
      });

    } catch (saveError: any) {
      console.error('❌ [TEST] Erro ao salvar na estrutura real:', {
        message: saveError.message,
        code: saveError.code,
        stack: saveError.stack
      });

      return NextResponse.json({
        success: false,
        error: 'Erro ao salvar na estrutura real',
        details: saveError.message,
        code: saveError.code
      });
    }

  } catch (error: any) {
    console.error('❌ [TEST] Erro geral:', error);
    return NextResponse.json({
      success: false,
      error: 'Erro geral no teste',
      details: error.message
    });
  }
}
