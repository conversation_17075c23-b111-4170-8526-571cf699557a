import { Suspense } from 'react';
import { Loader2 } from 'lucide-react';
import SharedInfluencersRedirectClient from './shared-redirect-client';

/**
 * Página de redirecionamento para listas compartilhadas
 * Redireciona para /[userId]/influencers mantendo os parâmetros de compartilhamento
 */
export default function SharedInfluencersRedirect() {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <SharedInfluencersRedirectClient />
    </Suspense>
  );
}

function LoadingFallback() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-[#ff0074]" />
        </div>
        <div className="space-y-2">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Carregando Lista Compartilhada
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Preparando redirecionamento...
          </p>
        </div>
      </div>
    </div>
  );
}


