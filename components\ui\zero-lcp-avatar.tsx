'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ZeroLCPAvatarProps {
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackClassName?: string;
  fallbackText?: string;
}

const sizeMap = {
  sm: { size: 32, className: 'h-8 w-8', fontSize: 'text-xs' },
  md: { size: 40, className: 'h-10 w-10', fontSize: 'text-sm' },
  lg: { size: 48, className: 'h-12 w-12', fontSize: 'text-base' },
  xl: { size: 80, className: 'h-20 w-20', fontSize: 'text-lg' }
};

/**
 * Avatar com ZERO impacto no LCP
 * 🎯 ESTRATÉGIA: APENAS CSS e texto - SEM IMAGENS
 * ✅ Renderização instantânea - 0ms
 * 🚀 ZERO bytes de imagem carregados
 * 💡 Perfeito para elementos críticos above-the-fold
 * 
 * Este componente NUNCA carrega imagens, garantindo que não impacte o LCP.
 * Usa apenas gradientes CSS e iniciais do nome para criar um avatar atrativo.
 */
export function ZeroLCPAvatar({
  alt,
  size = 'md',
  className,
  fallbackClassName,
  fallbackText
}: ZeroLCPAvatarProps) {
  const sizeConfig = sizeMap[size];
  
  // Gerar iniciais para fallback
  const getInitials = (text: string) => {
    if (!text) return '??';
    return text
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(fallbackText || alt);

  // Gerar cor baseada no nome para consistência
  const getColorFromName = (name: string) => {
    const colors = [
      'from-blue-500 to-purple-600',
      'from-green-500 to-blue-600', 
      'from-purple-500 to-pink-600',
      'from-orange-500 to-red-600',
      'from-teal-500 to-cyan-600',
      'from-indigo-500 to-purple-600',
      'from-pink-500 to-rose-600',
      'from-emerald-500 to-teal-600'
    ];
    
    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const gradientColors = getColorFromName(fallbackText || alt);

  return (
    <div 
      className={cn(
        'relative flex shrink-0 overflow-hidden rounded-full bg-gradient-to-br',
        gradientColors,
        sizeConfig.className,
        className
      )}
      role="img"
      aria-label={alt}
    >
      {/* 🎯 APENAS TEXTO - Renderização instantânea */}
      <div 
        className={cn(
          'flex items-center justify-center text-white font-semibold w-full h-full select-none',
          sizeConfig.fontSize,
          fallbackClassName
        )}
      >
        {initials}
      </div>
    </div>
  );
}

/**
 * Avatar Zero LCP especificamente para influenciadores
 * ✅ MOSTRA AVATAR REAL quando disponível
 * 🎯 FALLBACK INSTANTÂNEO com iniciais e gradiente
 * 🚀 Otimizado para performance com lazy loading
 */
export function ZeroLCPInfluencerAvatar({
  influencerName,
  avatarUrl,
  className,
  fallbackClassName,
  size = 'xl'
}: {
  influencerName: string;
  avatarUrl?: string;
  className?: string;
  fallbackClassName?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  const sizeConfig = sizeMap[size];

  // Gerar iniciais para fallback
  const getInitials = (text: string) => {
    if (!text) return '??';
    return text
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const initials = getInitials(influencerName);

  // Gerar cor baseada no nome para consistência
  const getColorFromName = (name: string) => {
    const colors = [
      'from-blue-500 to-purple-600',
      'from-green-500 to-blue-600',
      'from-purple-500 to-pink-600',
      'from-orange-500 to-red-600',
      'from-teal-500 to-cyan-600',
      'from-indigo-500 to-purple-600',
      'from-pink-500 to-rose-600',
      'from-emerald-500 to-teal-600'
    ];

    const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const gradientColors = getColorFromName(influencerName);

  // Se não há avatar, usar apenas o fallback
  if (!avatarUrl) {
    return (
      <ZeroLCPAvatar
        alt={influencerName}
        size={size}
        className={className}
        fallbackClassName={fallbackClassName}
        fallbackText={influencerName}
      />
    );
  }

  // Se há avatar, mostrar a imagem com fallback
  return (
    <div
      className={cn(
        'relative flex shrink-0 overflow-hidden rounded-full',
        sizeConfig.className,
        className
      )}
      role="img"
      aria-label={influencerName}
    >
      {/* 🎯 IMAGEM REAL com fallback instantâneo */}
      <img
        src={avatarUrl}
        alt={influencerName}
        className="w-full h-full object-cover"
        loading="lazy"
        onError={(e) => {
          // Em caso de erro, mostrar fallback com gradiente
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) {
            fallback.style.display = 'flex';
          }
        }}
      />

      {/* 🎯 FALLBACK com gradiente (oculto por padrão) */}
      <div
        className={cn(
          'absolute inset-0 hidden items-center justify-center text-white font-semibold bg-gradient-to-br select-none',
          gradientColors,
          sizeConfig.fontSize,
          fallbackClassName
        )}
      >
        {initials}
      </div>
    </div>
  );
}
