'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useUser, useAuth, Protect } from '@clerk/nextjs';
import { useDataTable } from '@/hooks/use-data-table';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DataTable } from '@/components/ui/data-table';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Package, Plus, Search, RefreshCw, Building2, Shield, Settings, Check, 
  Calendar,
  Filter, 
  Users, 
  Phone, 
  Mail, 
  MapPin, 
  Instagram, 
  Youtube, 
  Video, 
  Eye, 
  Heart, 
  ArrowUp,
  ArrowDown,
  TrendingUp,
  DollarSign,
  FileText,
  MoreVertical,
  Star,
  Heart as HeartIcon,
  Target,
  Megaphone,
  Zap,
  TrendingUp as TrendingUpIcon,
  Gift,
  Sparkles,
  Crown,
  Flame,
  Trophy,
  Rocket,
  Diamond,
  Music,
  Camera,
  VideoIcon,
  Palette,
  Edit,
  ChevronDown
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { Loader, useGlobalLoader } from '@/components/ui/loader';
import { ProposalService } from '@/services/proposal-service';
import { Checkbox } from '@/components/ui/checkbox';

// Importações para criação de propostas
import { useBrandsList } from '@/hooks/use-brands';
import { useAuth as useAuthV2 } from '@/hooks/use-auth-v2';
import { useAuthWithOrganizations } from '@/hooks/use-auth-organizations';
import { ProposalService as ProposalServiceLib } from '@/services/proposal-service';
import { ProposalPriority } from '@/types/proposal';

// Ícones disponíveis para propostas
const AVAILABLE_ICONS = [
  { name: 'FileText', icon: FileText, label: 'Documento' },
  { name: 'Star', icon: Star, label: 'Estrela' },
  { name: 'Heart', icon: HeartIcon, label: 'Coração' },
  { name: 'Target', icon: Target, label: 'Alvo' },
  { name: 'Megaphone', icon: Megaphone, label: 'Megafone' },
  { name: 'Zap', icon: Zap, label: 'Raio' },
  { name: 'TrendingUp', icon: TrendingUpIcon, label: 'Tendência' },
  { name: 'Gift', icon: Gift, label: 'Presente' },
  { name: 'Sparkles', icon: Sparkles, label: 'Brilho' },
  { name: 'Crown', icon: Crown, label: 'Coroa' },
  { name: 'Flame', icon: Flame, label: 'Chama' },
  { name: 'Trophy', icon: Trophy, label: 'Troféu' },
  { name: 'Rocket', icon: Rocket, label: 'Foguete' },
  { name: 'Diamond', icon: Diamond, label: 'Diamante' },
  { name: 'Music', icon: Music, label: 'Música' },
  { name: 'Camera', icon: Camera, label: 'Câmera' },
  { name: 'Video', icon: VideoIcon, label: 'Vídeo' },
  { name: 'Palette', icon: Palette, label: 'Paleta' }
];

// Interfaces para o Backlog de Influenciadores
interface InfluencerWithBrandData {
  id: string;
  originalInfluencerId: string; // ID original do influenciador antes da concatenação com marca
  nome: string;
  verificado?: boolean;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  email?: string;
  avatar?: string;
  country?: string;
  age?: number;
  gender?: string;
  category?: string;
  isVerified?: boolean;
  location?: string;
  phone?: string;
  mainNetwork?: string;
  promotesTraders?: boolean;
  responsibleName?: string;
  agencyName?: string;
  engagementRate?: number;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  // Dados da associação com a marca
  brandInfo: {
    brandId: string;
    brandName: string;
    brandLogo?: string;
    associationDate: Date;
    tags?: string[];
  };
  // Dados financeiros
  dadosFinanceiros?: {
    precos?: {
      instagramStory?: { price: number; name?: string };
      instagramReel?: { price: number; name?: string };
      youtubeInsertion?: { price: number; name?: string };
      youtubeDedicated?: { price: number; name?: string };
      youtubeShorts?: { price: number; name?: string };
      tiktokVideo?: { price: number; name?: string };
    };
    responsavel?: string;
    agencia?: string;
    whatsappFinanceiro?: string;
    emailFinanceiro?: string;
  };
  // Status de propostas
  proposalStatus: 'Enviado' | 'Não enviado';
  lastProposalDate?: Date;
  lastProposalName?: string;
  totalProposals?: number;
  // Compatibilidade com DataTable
  status: 'ativo' | 'inativo' | 'pendente';
}

interface BrandSummary {
  id: string;
  name: string;
  logo?: string;
  count: number;
}

interface PageProps {
  params: Promise<{
    userId: string;
  }>;
}

export default function CreatorBacklogPage({ params }: PageProps) {
  const { user: currentUser, isLoaded: userLoaded } = useUser();
  const { getToken } = useAuth();
  const { isLoading: globalLoading, showLoader, hideLoader } = useGlobalLoader();
  const router = useRouter();
  const [userId, setUserId] = useState<string | null>(null);

  // Hooks para criação de propostas
  const { currentUser: authUser } = useAuthV2();
  const { brands } = useBrandsList();
  const { 
    currentOrganization, 
    organizations, 
    isLoading: orgLoading 
  } = useAuthWithOrganizations();

  useEffect(() => {
    const resolveParams = async () => {
      showLoader();
      try {
        const resolvedParams = await params;
        setUserId(resolvedParams.userId);
      } catch (error) {
        console.error('Erro ao resolver parâmetros:', error);
        hideLoader();
      }
    };
    resolveParams();
  }, [params, showLoader, hideLoader]);

  const isOwnProfile = currentUser?.id === userId;
  const canAccess = isOwnProfile;

  // Estados para o backlog de influenciadores
  const [backlogInfluencers, setBacklogInfluencers] = useState<InfluencerWithBrandData[]>([]);
  const [brandsSummary, setBrandsSummary] = useState<BrandSummary[]>([]);
  const [backlogLoading, setBacklogLoading] = useState(false);
  const [backlogError, setBacklogError] = useState<string | null>(null);
  const [backlogLoaded, setBacklogLoaded] = useState(false);

  const [backlogSearchTerm, setBacklogSearchTerm] = useState('');
  const [activeBrandTab, setActiveBrandTab] = useState<string>('all');
  const [selectedInfluencersForList, setSelectedInfluencersForList] = useState<string[]>([]);
  const [showAddToListDialog, setShowAddToListDialog] = useState(false);



  // Estados para criação de proposta (Sheet unificado)
  const [showPropostaSheet, setShowPropostaSheet] = useState(false);
  const [newPropostaName, setNewPropostaName] = useState('');
  const [newPropostaDescription, setNewPropostaDescription] = useState('');
  const [newPropostaDate, setNewPropostaDate] = useState<Date | undefined>(new Date());
  const [selectedIcon, setSelectedIcon] = useState<string>('FileText');
  const [newPropostaPriority, setNewPropostaPriority] = useState<ProposalPriority>('medium');

  // Estados para controle de colunas do backlog
  const [showBacklogColumnDialog, setShowBacklogColumnDialog] = useState(false);
  const [backlogVisibleColumns, setBacklogVisibleColumns] = useState<{ [key: string]: boolean }>({});
  const [backlogColumnOrder, setBacklogColumnOrder] = useState<string[]>([]);

  // Configurações padrão das colunas do backlog
  const defaultBacklogVisibleColumns = {
    nomeInfluencer: true,
    marca: true,
    proposalStatus: true,
    plataforma: true,
    seguidores: true,
    country: true,
    category: true,
    location: true,
    age: true,
    gender: true,
    verified: true,
    mainNetwork: true,
    engagementRate: true,
    promotesTraders: true,
    responsibleInfo: true,
    contato: true,
    instagram_followers: false,
    instagram_views: false,
    tiktok_followers: false,
    tiktok_views: false,
    youtube_followers: false,
    youtube_views: false,
    acoes: true,
  };

  // Nomes amigáveis das colunas do backlog
  const backlogColumnLabels: { [key: string]: string } = {
    nomeInfluencer: 'Influenciador',
    marca: 'Marca',
    plataforma: 'Plataforma',
    seguidores: 'Seguidores',
    proposalStatus: 'Status Proposta',
    country: 'País',
    location: 'Localização',
    age: 'Idade',
    gender: 'Gênero',
    category: 'Categoria',
    verified: 'Verificado',
    mainNetwork: 'Rede Principal',
    engagementRate: 'Engajamento',
    promotesTraders: 'Divulga Trader',
    responsibleInfo: 'Responsável',
    contato: 'Contato',
    instagram_followers: 'Instagram Seguidores',
    instagram_views: 'Instagram Views',
    tiktok_followers: 'TikTok Seguidores',
    tiktok_views: 'TikTok Views',
    youtube_followers: 'YouTube Seguidores',
    youtube_views: 'YouTube Views',
    acoes: 'Ações',
  };

  // Carregar configurações das colunas do backlog
  useEffect(() => {
    // Definir ordem padrão com marca na terceira posição
    const defaultOrder = [
      'select',
      'nomeInfluencer', 
      'marca',
      'proposalStatus', 
      'plataforma', 
      'seguidores', 
      'country', 
      'category', 
      'location', 
      'age', 
      'gender', 
      'verified', 
      'mainNetwork', 
      'engagementRate', 
      'promotesTraders', 
      'responsibleInfo', 
      'contato',
      'instagram_followers',
      'instagram_views',
      'tiktok_followers',
      'tiktok_views',
      'youtube_followers',
      'youtube_views',
      'acoes'
    ];

    // Sempre forçar a ordem padrão para garantir que a coluna marca esteja na posição correta
    console.log('🔧 [COLUMN_ORDER] Definindo ordem padrão das colunas:', defaultOrder);
    
    setBacklogVisibleColumns(defaultBacklogVisibleColumns);
    setBacklogColumnOrder(defaultOrder);
    
    // Limpar e recriar configurações para garantir ordem correta
    localStorage.removeItem('backlog-table-columns');
    localStorage.removeItem('backlog-table-order');
    saveBacklogColumnSettings(defaultBacklogVisibleColumns, defaultOrder);
  }, []);

  // Efeito adicional para garantir que a ordem seja aplicada após o primeiro render
  useEffect(() => {
    if (backlogColumnOrder.length > 0) {
      console.log('🔍 [COLUMN_ORDER] Verificando ordem atual das colunas:', backlogColumnOrder);
      
      // Verificar se a coluna 'marca' está na posição correta (índice 2)
      const marcaIndex = backlogColumnOrder.indexOf('marca');
      if (marcaIndex !== 2) {
        console.log('⚠️ [COLUMN_ORDER] Coluna marca não está na posição correta. Posição atual:', marcaIndex, 'Corrigindo...');
        
        // Reordenar para colocar marca na posição correta
        const correctedOrder = ['select', 'nomeInfluencer', 'marca', ...backlogColumnOrder.filter(col => !['select', 'nomeInfluencer', 'marca'].includes(col))];
        setBacklogColumnOrder(correctedOrder);
        saveBacklogColumnSettings(backlogVisibleColumns, correctedOrder);
      }
    }
  }, [backlogColumnOrder, backlogVisibleColumns]);

  // Funções para salvar/carregar configurações das colunas do backlog
  const saveBacklogColumnSettings = (columns: { [key: string]: boolean }, order: string[]) => {
    try {
      localStorage.setItem('backlog-table-columns', JSON.stringify(columns));
      localStorage.setItem('backlog-table-order', JSON.stringify(order));
    } catch (error) {
      console.warn('Erro ao salvar configurações das colunas do backlog:', error);
    }
  };

  // Função para toggle de visibilidade das colunas do backlog
  const toggleBacklogColumnVisibility = (columnKey: string) => {
    const newVisibleColumns = {
      ...backlogVisibleColumns,
      [columnKey]: !backlogVisibleColumns[columnKey]
    };
    setBacklogVisibleColumns(newVisibleColumns);
    saveBacklogColumnSettings(newVisibleColumns, backlogColumnOrder);
  };

  // Função para quando a ordem das colunas do backlog mudar
  const handleBacklogColumnOrderChange = (newOrder: string[]) => {
    const filteredOrder = newOrder.filter(col => col !== 'select');
    const finalOrder = ['select', ...filteredOrder];
    
    setBacklogColumnOrder(finalOrder);
    saveBacklogColumnSettings(backlogVisibleColumns, finalOrder);
  };

  // Controlar loader global baseado nos estados de loading
  useEffect(() => {
    if (!userLoaded || backlogLoading) {
      showLoader();
    } else if (currentUser && userId) {
      hideLoader();
    }
  }, [
    userLoaded, 
    backlogLoading,
    currentUser, 
    userId,
    showLoader,
    hideLoader
  ]);

  // ✅ CORREÇÃO: Carregar marcas das associações
  const [associatedBrands, setAssociatedBrands] = useState<any[]>([]);
  const [associationsLoading, setAssociationsLoading] = useState(false);

  // Função para carregar marcas das associações
  const loadAssociatedBrands = useCallback(async () => {
    if (!currentUser || !canAccess) {
      return;
    }

    try {
      setAssociationsLoading(true);
      
      const response = await fetch('/api/brand-influencers', {
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': currentUser.id
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Erro HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (data.associations && Array.isArray(data.associations)) {
        // Extrair marcas únicas das associações
        const brandMap = new Map();
        data.associations.forEach((assoc: any) => {
          if (!brandMap.has(assoc.brandId)) {
            brandMap.set(assoc.brandId, {
              id: assoc.brandId,
              name: assoc.brandName,
              logo: assoc.brandLogo,
              count: 1
            });
          } else {
            const existing = brandMap.get(assoc.brandId);
            existing.count += 1;
          }
        });
        
        const brandsArray = Array.from(brandMap.values());
        setAssociatedBrands(brandsArray);
      } else {
        setAssociatedBrands([]);
      }
    } catch (error) {
      console.error('Erro ao carregar marcas das associações:', error);
      setAssociatedBrands([]);
    } finally {
      setAssociationsLoading(false);
    }
  }, [currentUser, canAccess]);

  // Carregar marcas das associações
  useEffect(() => {
    loadAssociatedBrands();
  }, [loadAssociatedBrands]);

  // Usar apenas marcas das associações
  const allBrands = useMemo(() => {
    return associatedBrands.map((brand: any) => ({
      id: brand.id,
      name: brand.name,
      logo: brand.logo,
      industry: brand.industry || undefined
    }));
  }, [associatedBrands]);

  const finalBrandsLoading = associationsLoading;

  // Hook para DataTable do backlog
  const { dataTableProps: backlogDataTableProps, selectedItems: selectedBacklogItems, handleRowSelectionChange: handleBacklogRowSelectionChange, resetSelection: resetBacklogSelection } = useDataTable<InfluencerWithBrandData>({
    enableColumnOrdering: true,
    enableRowSelection: true,
    pageSize: 20
  });

  // ===== FUNÇÕES DO BACKLOG DE INFLUENCIADORES =====

  /**
   * Verificar status de propostas dos influenciadores para uma marca específica
   */
  const checkInfluencerProposalStatus = useCallback(async (influencerIds: string[], brandId?: string) => {
    if (!currentUser || influencerIds.length === 0) {
      return {};
    }

    try {
      const token = await getToken();
      
      console.log('🔍 [PROPOSAL_STATUS] Verificando status de propostas para', influencerIds.length, 'influenciadores', brandId ? `da marca ${brandId}` : 'de todas as marcas');

      const response = await fetch('/api/proposals/stats', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          influencerIds: influencerIds,
          brandId: brandId || 'all' // Passar o brandId ou 'all' para todas as marcas
        })
      });

      if (!response.ok) {
        console.warn('⚠️ [PROPOSAL_STATUS] Erro ao verificar status das propostas:', response.status);
        // Retornar status padrão se a API falhar
        return influencerIds.reduce((acc, id) => {
          acc[id] = {
            status: 'Não enviado' as const,
            totalProposals: 0
          };
          return acc;
        }, {} as Record<string, { status: 'Enviado' | 'Não enviado'; totalProposals: number; lastProposalDate?: Date; lastProposalName?: string }>);
      }

      const data = await response.json();
      
      console.log('✅ [PROPOSAL_STATUS] Status obtido:', data);

      return data.influencersStatus || {};

    } catch (error) {
      console.error('❌ [PROPOSAL_STATUS] Erro ao verificar status das propostas:', error);
      // Retornar status padrão em caso de erro
      return influencerIds.reduce((acc, id) => {
        acc[id] = {
          status: 'Não enviado' as const,
          totalProposals: 0
        };
        return acc;
      }, {} as Record<string, { status: 'Enviado' | 'Não enviado'; totalProposals: number; lastProposalDate?: Date; lastProposalName?: string }>);
    }
  }, [currentUser, getToken]);

  /**
   * ✅ Forçar reload dos dados do backlog (usado pelo botão "Atualizar")
   */
  const forceReloadBacklog = useCallback(() => {
    console.log('🔄 [BACKLOG] Forçando reload dos dados...');
    setBacklogLoaded(false); // Resetar estado para permitir novo carregamento
  }, []);

  /**
   * Carregar influenciadores das marcas do usuário via híbrido REST+GraphQL
   */
  const loadBacklogInfluencers = useCallback(async () => {
    if (!currentUser || !userId) {
      setBacklogInfluencers([]);
      setBrandsSummary([]);
      setBacklogLoading(false);
      return;
    }

    try {
      setBacklogLoading(true);
      setBacklogError(null);

      console.log('🔍 [BACKLOG HÍBRIDO] Iniciando busca de influenciadores...');

      const token = await getToken();
      
      // ===== ETAPA 1: Buscar associações brand_influencers via REST =====
      let associationsUrl = '/api/brand-influencers';

      console.log('🔍 [BACKLOG REST] Buscando associações:', associationsUrl);

      const associationsResponse = await fetch(associationsUrl, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!associationsResponse.ok) {
        const errorData = await associationsResponse.json().catch(() => ({}));
        throw new Error(errorData.message || `Erro HTTP ${associationsResponse.status}`);
      }

      const associationsData = await associationsResponse.json();
      
      if (!associationsData.associations || !Array.isArray(associationsData.associations)) {
        console.warn('⚠️ [BACKLOG REST] Nenhuma associação encontrada');
        setBacklogInfluencers([]);
        setBrandsSummary([]);
        return;
      }

      console.log('🔍 [BACKLOG REST] Associações encontradas:', associationsData.associations.length);

      // ===== ETAPA 2: Extrair IDs dos influenciadores únicos =====
      const influencerIds = [...new Set(associationsData.associations.map((assoc: any) => assoc.influencerId))];
      
      if (influencerIds.length === 0) {
        console.warn('⚠️ [BACKLOG] Nenhum influenciador associado encontrado');
        setBacklogInfluencers([]);
        setBrandsSummary([]);
        return;
      }

      // ===== ETAPA 3: Buscar dados dos influenciadores via GraphQL =====
      console.log('🔍 [BACKLOG GraphQL] Buscando dados de', influencerIds.length, 'influenciadores...');

      // Query GraphQL para buscar os influenciadores específicos
      const query = `
        query GetInfluencers($userId: ID!, $filters: InfluencerFilters) {
          influencers(userId: $userId, filters: $filters, pagination: { limit: 100 }) {
            nodes {
              id
              name
              avatar
              email
              whatsapp
              phone
              age
              gender
              country
              city
              state
              location
              category
              isVerified
              engagementRate
              promotesTraders
              responsibleName
              agencyName
              responsibleCapturer
              
              # Redes sociais diretas
              instagramUsername
              instagramFollowers
              instagramEngagementRate
              instagramAvgViews
              instagramStoriesViews
              instagramReelsViews
              
              youtubeUsername
              youtubeFollowers
              youtubeEngagementRate
              youtubeAvgViews
              youtubeShortsViews
              youtubeLongFormViews
              
              tiktokUsername
              tiktokFollowers
              tiktokEngagementRate
              tiktokAvgViews
              tiktokVideoViews
              
              facebookUsername
              facebookFollowers
              facebookEngagementRate
              facebookAvgViews
              
              twitchUsername
              twitchFollowers
              twitchEngagementRate
              twitchViews
              
              kwaiUsername
              kwaiFollowers
              kwaiEngagementRate
              kwaiViews
              
              # Dados demográficos atuais
              currentDemographics {
                platform
                audienceGender {
                  female
                  male
                  other
                }
                audienceAgeRange {
                  range
                  percentage
                }
                audienceLocations {
                  country
                  percentage
                }
                audienceCities {
                  city
                  percentage
                }
              }
              
              # Pricing atual
              currentPricing {
                services {
                  instagram {
                    story {
                      price
                    }
                    reel {
                      price
                    }
                    post {
                      price
                    }
                  }
                  youtube {
                    shorts {
                      price
                    }
                    insertion {
                      price
                    }
                    dedicated {
                      price
                    }
                  }
                  tiktok {
                    video {
                      price
                    }
                  }
                }
                notes
                validUntil
                createdAt
              }
              
              createdAt
              updatedAt
            }
            totalCount
          }
        }
      `;

      const graphqlResponse = await fetch('/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          variables: { userId }
        }),
      });

      if (!graphqlResponse.ok) {
        throw new Error(`Erro HTTP GraphQL ${graphqlResponse.status}: ${graphqlResponse.statusText}`);
      }

      const { data, errors } = await graphqlResponse.json();
      
      if (errors) {
        console.error('🔍 [BACKLOG GraphQL] Erros na query:', errors);
        throw new Error(errors[0]?.message || 'Erro na query GraphQL');
      }

      if (!data?.influencers?.nodes) {
        console.warn('⚠️ [BACKLOG GraphQL] Dados não encontrados na resposta');
        setBacklogInfluencers([]);
        setBrandsSummary([]);
        return;
      }

      // ===== ETAPA 4: Filtrar apenas os influenciadores associados =====
      const allInfluencers = data.influencers.nodes;
      const associatedInfluencers = allInfluencers.filter((inf: any) => 
        influencerIds.includes(inf.id)
      );

      // ===== ETAPA 5: Criar uma entrada para cada associação marca-influenciador =====
      const transformedInfluencers = [];
      
      for (const association of associationsData.associations) {
        // Encontrar o influenciador correspondente
        const influencer = associatedInfluencers.find((inf: any) => inf.id === association.influencerId);
        
        if (influencer) {
          // Criar uma entrada única para cada associação
          const transformedInfluencer = {
            ...influencer,
            // Criar ID único para cada associação (influencer + marca)
            id: `${influencer.id}_${association.brandId}`,
            originalInfluencerId: influencer.id, // Manter ID original para referência
            
            // Garantir compatibilidade com campos antigos
            nome: influencer.name,
            pais: influencer.country,
            cidade: influencer.city,
            estado: influencer.state,
            idade: influencer.age,
            categoria: influencer.category,
            verificado: influencer.isVerified,
            genero: influencer.gender,
            
            // Transformar redes sociais para o formato esperado
            redesSociais: {
              instagram: influencer.instagramUsername ? {
                username: influencer.instagramUsername,
                seguidores: influencer.instagramFollowers || 0,
                engajamento: influencer.instagramAvgViews || 0
              } : null,
              youtube: influencer.youtubeUsername ? {
                username: influencer.youtubeUsername,
                seguidores: influencer.youtubeFollowers || 0,
                visualizacoes: influencer.youtubeAvgViews || 0
              } : null,
              tiktok: influencer.tiktokUsername ? {
                username: influencer.tiktokUsername,
                seguidores: influencer.tiktokFollowers || 0,
                curtidas: influencer.tiktokAvgViews || 0
              } : null
            },
            
            // Adicionar informações da marca
            brandInfo: {
              brandId: association.brandId,
              brandName: association.brandName,
              brandLogo: association.brandLogo,
              associationDate: new Date(association.createdAt || Date.now()),
              tags: association.tags || []
            },
            
            // Adicionar status para compatibilidade com DataTable
            status: 'ativo' as const
          };
          
          transformedInfluencers.push(transformedInfluencer);
        }
      }

      // ===== ETAPA 6: Verificar status de propostas por marca específica =====
      console.log('🔍 [BACKLOG] Verificando status de propostas por marca...');
      
      // Obter marcas únicas
      const uniqueBrands = [...new Set(transformedInfluencers.map((inf: any) => inf.brandInfo.brandId))];
      console.log('🏷️ [BACKLOG] Marcas encontradas:', uniqueBrands.length);

      // Criar um mapa para armazenar o status por marca e influenciador
      const brandInfluencerStatusMap = new Map<string, Record<string, any>>();

      // Para cada marca, verificar o status dos influenciadores
      for (const brandId of uniqueBrands) {
        const influencersInBrand = transformedInfluencers
          .filter((inf: any) => inf.brandInfo.brandId === brandId)
          .map((inf: any) => inf.originalInfluencerId);
        
        const uniqueInfluencersInBrand = [...new Set(influencersInBrand)];
        
        console.log(`🔍 [BACKLOG] Verificando ${uniqueInfluencersInBrand.length} influenciadores para marca ${brandId}`);
        
        const statusMapForBrand = await checkInfluencerProposalStatus(uniqueInfluencersInBrand, brandId);
        brandInfluencerStatusMap.set(brandId, statusMapForBrand);
      }

      // ===== ETAPA 7: Adicionar status de propostas específicos por marca =====
      const influencersWithProposalStatus = transformedInfluencers.map((influencer: any) => {
        const brandId = influencer.brandInfo.brandId;
        const statusMapForThisBrand = brandInfluencerStatusMap.get(brandId) || {};
        const statusInfo = statusMapForThisBrand[influencer.originalInfluencerId] || {
          status: 'Não enviado' as const,
          totalProposals: 0
        };

        return {
          ...influencer,
          proposalStatus: statusInfo.status,
          totalProposals: statusInfo.totalProposals,
          lastProposalDate: statusInfo.lastProposalDate ? new Date(statusInfo.lastProposalDate) : undefined,
          lastProposalName: statusInfo.lastProposalName
        };
      });

      // ===== ETAPA 8: Calcular estatísticas das marcas =====
      const brandStats = new Map<string, { name: string; logo?: string; count: number }>();
      
      associationsData.associations.forEach((assoc: any) => {
        const existing = brandStats.get(assoc.brandId);
        brandStats.set(assoc.brandId, {
          name: assoc.brandName,
          logo: assoc.brandLogo,
          count: (existing?.count || 0) + 1
        });
      });

      const brandsArray = Array.from(brandStats.entries()).map(([id, data]) => ({
        id,
        ...data
      }));

      setBacklogInfluencers(influencersWithProposalStatus);
      setBrandsSummary(brandsArray);
      setBacklogLoaded(true);
      
      console.log('✅ [BACKLOG HÍBRIDO] Dados carregados:', {
        associationsTotal: associationsData.associations.length,
        influencersFound: associatedInfluencers.length,
        influencersTransformed: transformedInfluencers.length,
        influencersWithStatus: influencersWithProposalStatus.length,
        brands: brandsArray.length,
        proposalStats: {
          enviados: influencersWithProposalStatus.filter(inf => inf.proposalStatus === 'Enviado').length,
          naoEnviados: influencersWithProposalStatus.filter(inf => inf.proposalStatus === 'Não enviado').length
        }
      });

    } catch (error) {
      console.error('❌ [BACKLOG HÍBRIDO] Erro ao carregar influenciadores:', error);
      setBacklogError(error instanceof Error ? error.message : 'Erro desconhecido');
      setBacklogInfluencers([]);
      setBrandsSummary([]);
      setBacklogLoaded(false);
    } finally {
      setBacklogLoading(false);
    }
  }, [currentUser, userId, checkInfluencerProposalStatus]);

  // ✅ Carregar dados do backlog
  useEffect(() => {
    if (canAccess && currentUser && !backlogLoaded) {
      console.log('🔄 [BACKLOG] Carregando dados...', {
        isFirstTime: backlogInfluencers.length === 0,
        isForceReload: backlogInfluencers.length > 0
      });
      loadBacklogInfluencers();
    }
  }, [canAccess, currentUser, backlogLoaded, loadBacklogInfluencers]);



  // Filtrar influenciadores do backlog
  const filteredBacklogInfluencers = useMemo(() => {
    return backlogInfluencers.filter(influencer => {
      const matchesBrand = activeBrandTab === 'all' || influencer.brandInfo.brandId === activeBrandTab;
      const matchesSearch = influencer.nome.toLowerCase().includes(backlogSearchTerm.toLowerCase()) ||
                           influencer.categoria.toLowerCase().includes(backlogSearchTerm.toLowerCase()) ||
                           influencer.brandInfo.brandName.toLowerCase().includes(backlogSearchTerm.toLowerCase());
      
      return matchesBrand && matchesSearch;
    });
  }, [backlogInfluencers, backlogSearchTerm, activeBrandTab]);

  // Funções auxiliares para formatação
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const InstagramIcon = ({ className }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className={className}>
      <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
    </svg>
  );

  const YoutubeIcon = ({ className }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16" className={className}>
      <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
    </svg>
  );

  const TiktokIcon = ({ className }: { className?: string }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 448 512" className={className}>
      <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
    </svg>
  );

  // Todas as colunas possíveis para o backlog de influenciadores
  const allBacklogColumns = useMemo(() => [
    {
      accessorKey: "nomeInfluencer",
      header: () => <span className="text-[#270038] dark:text-white">INFLUENCIADOR</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        return (
          <div className="flex items-center space-x-2 min-w-[140px] max-w-[200px] text-left">
            <img
              src={influencer.avatar || '/placeholder-user.jpg'}
              alt={influencer.nome}
              className="w-7 h-7 rounded-full object-cover flex-shrink-0"
            />
            <div className="min-w-0 flex-1">
              <div className="text-sm font-medium truncate text-left">{influencer.nome}</div>
              <div className="text-xs text-muted-foreground truncate text-left">@{influencer.nome.toLowerCase().replace(' ', '')}</div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "marca",
      header: () => <span className="text-[#270038] dark:text-white">MARCA</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        const brandInfo = influencer.brandInfo;
        
        return (
          <div className="flex items-center space-x-2  max-w-[130px]">
            {brandInfo?.brandLogo && (
              <img
                src={brandInfo.brandLogo}
                alt={brandInfo.brandName}
                className="w-6 h-6 rounded object-cover flex-shrink-0"
              />
            )}
            <div className="min-w-0 flex-1">
              <div className="text-sm font-medium truncate" title={brandInfo?.brandName || 'N/A'}>
                {brandInfo?.brandName || 'N/A'}
              </div>
            
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "proposalStatus",
      header: () => <span className="text-[#270038] dark:text-white">STATUS PROPOSTA</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        const status = influencer.proposalStatus || 'Não enviado';
        const totalProposals = influencer.totalProposals || 0;
        const lastProposalName = influencer.lastProposalName;
        
        return (
          <div className="min-w-[110px] max-w-[130px]">
            <div className="flex flex-col items-center gap-1">
              <Badge 
                variant={status === 'Enviado' ? 'default' : 'secondary'}
                className={cn(
                  "text-xs font-medium",
                  status === 'Enviado' 
                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300" 
                    : "bg-gray-100 text-gray-800 dark:bg-[#080210] dark:text-gray-300"
                )}
              >
                {status}
              </Badge>
              {status === 'Enviado' && totalProposals > 0 && (
                <div className="text-xs text-muted-foreground">
                  {totalProposals} prop.
                </div>
              )}
              {status === 'Enviado' && lastProposalName && (
                <div className="text-xs text-muted-foreground font-medium truncate max-w-[100px]" title={lastProposalName}>
                  {lastProposalName}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "plataforma",
      header: () => <span className="text-[#270038] dark:text-white">PLATAFORMA</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const platforms = [];
        if (influencer.redesSociais?.instagram) {
          platforms.push({ 
            type: 'instagram', 
            icon: <InstagramIcon />,
            followers: influencer.redesSociais.instagram.seguidores
          });
        }
        if (influencer.redesSociais?.youtube) {
          platforms.push({ 
            type: 'youtube', 
            icon: <YoutubeIcon />,
            followers: influencer.redesSociais.youtube.seguidores
          });
        }
        if (influencer.redesSociais?.tiktok) {
          platforms.push({ 
            type: 'tiktok', 
            icon: <TiktokIcon />,
            followers: influencer.redesSociais.tiktok.seguidores
          });
        }
        
        return (
          <div className="flex items-center gap-1 min-w-[90px] max-w-[110px]">
            {platforms.slice(0, 2).map((platform) => (
              <div key={platform.type} className="flex items-center gap-1">
                <div className="flex items-center justify-center w-4 h-4">
                  {platform.icon}
                </div>
                <span className="text-xs text-muted-foreground">
                  {formatNumber(platform.followers)}
                </span>
              </div>
            ))}
            {platforms.length > 2 && (
              <span className="text-xs text-muted-foreground">+{platforms.length - 2}</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "seguidores",
      header: () => <span className="text-[#270038] dark:text-white">SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const maxFollowers = Math.max(
          influencer.redesSociais?.instagram?.seguidores || 0,
          influencer.redesSociais?.youtube?.seguidores || 0,
          influencer.redesSociais?.tiktok?.seguidores || 0
        );
        
        return (
          <div className="text-sm min-w-[90px] max-w-[110px] text-center">
            <div className="font-medium text-sm text-[#ec003f]">
              {formatNumber(maxFollowers)}
            </div>
            <div className="text-xs text-muted-foreground/70">maior rede</div>
          </div>
        );
      },
    },
    {
      accessorKey: "country",
      header: () => <span className="text-[#270038] dark:text-white">PAÍS</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        return (
          <div className="min-w-[70px] max-w-[90px] text-center">
            <span className="text-sm truncate block">{influencer.country || influencer.pais || 'N/A'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "location",
      header: () => <span className="text-[#270038] dark:text-white">LOCALIZAÇÃO</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const location = influencer.location || 
                        (influencer.cidade && influencer.estado ? 
                         `${influencer.cidade}/${influencer.estado}` : null);
        
        return (
          <div className="min-w-[100px] max-w-[130px] text-center">
            <span className="text-sm truncate block" title={location || 'N/A'}>{location || 'N/A'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "age",
      header: () => <span className="text-[#270038] dark:text-white">IDADE</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        return (
          <div className="min-w-[60px] max-w-[70px] text-center">
            <span className="text-sm">{influencer.age || influencer.idade || 'N/A'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "gender",
      header: () => <span className="text-[#270038] dark:text-white">GÊNERO</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const gender = influencer.gender || influencer.genero;
        const genderText = gender === 'female' ? 'F' : 
                          gender === 'male' ? 'M' : 
                          gender === 'Feminino' ? 'F' :
                          gender === 'Masculino' ? 'M' :
                          gender ? gender.charAt(0).toUpperCase() : 'N/A';
        
        return (
          <div className="min-w-[60px] max-w-[70px] text-center">
            <span className="text-sm" title={gender}>{genderText}</span>
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: () => <span className="text-[#270038] dark:text-white">CATEGORIA</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        return (
          <div className="min-w-[100px] max-w-[120px] text-center">
            <span className="text-sm truncate block" title={influencer.category || influencer.categoria || 'N/A'}>
              {influencer.category || influencer.categoria || 'N/A'}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "verified",
      header: () => <span className="text-[#270038] dark:text-white">VERIFICADO</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        const isVerified = influencer.isVerified || influencer.verificado;
        
        return (
          <div className="min-w-[100px] text-right">
            {isVerified ? (
              <div className="flex items-center justify-end gap-1">
                <Check className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-blue-600">Sim</span>
              </div>
            ) : (
              <span className="text-sm">Não</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "mainNetwork",
      header: () => <span className="text-[#270038] dark:text-white">REDE PRINCIPAL</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        // Determinar a rede principal baseado nos seguidores
        let mainNetwork = '';
        let maxFollowers = 0;
        
        if (influencer.redesSociais?.instagram?.seguidores > maxFollowers) {
          mainNetwork = 'instagram';
          maxFollowers = influencer.redesSociais.instagram.seguidores;
        }
        if (influencer.redesSociais?.youtube?.seguidores > maxFollowers) {
          mainNetwork = 'youtube';
          maxFollowers = influencer.redesSociais.youtube.seguidores;
        }
        if (influencer.redesSociais?.tiktok?.seguidores > maxFollowers) {
          mainNetwork = 'tiktok';
          maxFollowers = influencer.redesSociais.tiktok.seguidores;
        }
        
        const networkIcon = mainNetwork === 'instagram' ? <InstagramIcon /> :
                           mainNetwork === 'tiktok' ? <TiktokIcon /> :
                           mainNetwork === 'youtube' ? <YoutubeIcon /> :
                           null;
        
        return (
          <div className="min-w-[120px] text-right">
            {networkIcon ? (
              <div className="flex items-center justify-end gap-2">
                <div className="flex items-center justify-end w-5 h-5">
                  {networkIcon}
                </div>
                <span className="text-sm capitalize">
                  {mainNetwork}
                </span>
              </div>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "engagementRate",
      header: () => <span className="text-[#270038] dark:text-white">ENGAJAMENTO</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const engagementRate = influencer.engagementRate;
        
        return (
          <div className="min-w-[120px] text-right">
            {engagementRate ? (
              <div className="text-sm">
                <span className="font-medium text-[#ec003f]">
                  {typeof engagementRate === 'number' ? `${engagementRate.toFixed(2)}%` : engagementRate}
                </span>
              </div>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "promotesTraders",
      header: () => <span className="text-[#270038] dark:text-white">DIVULGA TRADER</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const promotesTraders = influencer.promotesTraders;
        
        return (
          <div className="min-w-[120px] text-right">
            {promotesTraders === true ? (
              <span className="text-sm text-green-600">Sim</span>
            ) : promotesTraders === false ? (
              <span className="text-sm text-red-600">Não</span>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "responsibleInfo",
      header: () => <span className="text-[#270038] dark:text-white">RESPONSÁVEL</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const responsibleName = influencer.responsibleName;
        const agencyName = influencer.agencyName;
        
        return (
          <div className="min-w-[150px] text-center">
            <Protect role="org:admin">
              {responsibleName || agencyName ? (
                <div className="text-sm">
                  {responsibleName && (
                    <div>
                      <span className="text-muted-foreground">Resp:</span> <span>{responsibleName}</span>
                    </div>
                  )}
                  {agencyName && (
                    <div>
                      <span className="text-muted-foreground">Agência:</span> <span>{agencyName}</span>
                    </div>
                  )}
                </div>
              ) : (
                <span className="text-sm">N/A</span>
              )}
            </Protect>
          </div>
        );
      },
    },
    {
      accessorKey: "contato",
      header: () => <span className="text-[#270038] dark:text-white">CONTATO</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const whatsapp = influencer.whatsapp;
        const email = influencer.email;
        
        return (
          <div className="min-w-[200px]">
            {whatsapp && (
              <div className="text-sm">
                <span className="text-muted-foreground">WhatsApp:</span> <span>{whatsapp}</span>
              </div>
            )}
            {email && (
              <div className="text-sm">
                <span className="text-muted-foreground">Email:</span> <span>{email}</span>
              </div>
            )}
          </div>
        );
      },
    },
    // Colunas detalhadas de redes sociais
    {
      accessorKey: "instagram_followers",
      header: () => <span className="text-[#270038] dark:text-white">INSTAGRAM SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const followers = influencer.redesSociais?.instagram?.seguidores || 0;
        
        return (
          <div className="min-w-[150px] text-center">
            {followers > 0 ? (
              <span className="text-sm font-medium">
                {formatNumber(followers)}
              </span>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "instagram_views",
      header: () => <span className="text-[#270038] dark:text-white">INSTAGRAM VIEWS</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const avgViews = influencer.redesSociais?.instagram?.engajamento || 0;
        
        return (
          <div className="min-w-[150px] text-center">
            {avgViews > 0 ? (
              <span className="text-sm font-medium">
                {formatNumber(avgViews)}
              </span>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "tiktok_followers",
      header: () => <span className="text-[#270038] dark:text-white">TIKTOK SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const followers = influencer.redesSociais?.tiktok?.seguidores || 0;
        
        return (
          <div className="min-w-[150px] text-center">
            {followers > 0 ? (
              <span className="text-sm font-medium">
                {formatNumber(followers)}
              </span>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "tiktok_views",
      header: () => <span className="text-[#270038] dark:text-white">TIKTOK VIEWS</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const avgViews = influencer.redesSociais?.tiktok?.curtidas || 0;
        
        return (
          <div className="min-w-[150px] text-center">
            {avgViews > 0 ? (
              <span className="text-sm font-medium">
                {formatNumber(avgViews)}
              </span>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_followers",
      header: () => <span className="text-[#270038] dark:text-white">YOUTUBE SEGUIDORES</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const followers = influencer.redesSociais?.youtube?.seguidores || 0;
        
        return (
          <div className="min-w-[150px] text-center">
            {followers > 0 ? (
              <span className="text-sm font-medium">
                {formatNumber(followers)}
              </span>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "youtube_views",
      header: () => <span className="text-[#270038] dark:text-white">YOUTUBE VIEWS</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        const avgViews = influencer.redesSociais?.youtube?.visualizacoes || 0;
        
        return (
          <div className="min-w-[150px] text-center">
            {avgViews > 0 ? (
              <span className="text-sm font-medium">
                {formatNumber(avgViews)}
              </span>
            ) : (
              <span className="text-sm">N/A</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "acoes",
      header: () => <span className="text-[#270038] dark:text-white">AÇÕES</span>,
      cell: ({ row }: any) => {
        const influencer = row.original;
        
        return (
          <div className="flex items-center gap-2 min-w-[100px]">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setSelectedInfluencersForList([influencer.id]);
                setShowAddToListDialog(true);
              }}
              className="text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Adicionar
            </Button>
          </div>
        );
      },
    },
  ], []);

  // Colunas filtradas baseadas na visibilidade configurada e ordenadas
  const backlogColumns = useMemo(() => {
    // Filtrar colunas visíveis
    const visibleColumns = allBacklogColumns.filter(column => {
      const key = column.accessorKey;
      return backlogVisibleColumns[key] !== false;
    });

    // Ordenar colunas baseado na ordem configurada
    if (backlogColumnOrder.length > 0) {
      const orderedColumns = [];
      
      // Primeiro, adicionar colunas na ordem especificada
      for (const columnKey of backlogColumnOrder) {
        if (columnKey !== 'select') { // Pular 'select' pois não é uma coluna real
          const column = visibleColumns.find(col => col.accessorKey === columnKey);
          if (column) {
            orderedColumns.push(column);
          }
        }
      }
      
      // Depois, adicionar qualquer coluna que não esteja na ordem (fallback)
      for (const column of visibleColumns) {
        if (!orderedColumns.find(col => col.accessorKey === column.accessorKey)) {
          orderedColumns.push(column);
        }
      }
      
      return orderedColumns;
    }

    return visibleColumns;
  }, [allBacklogColumns, backlogVisibleColumns, backlogColumnOrder]);

  // ===== FUNÇÕES PARA CRIAÇÃO DE PROPOSTA (SHEET UNIFICADO) =====

  // Função para verificar se há organização válida
  const hasValidOrganization = () => {
    return currentOrganization && organizations.length > 0;
  };

  // ✅ NOVA FUNÇÃO: Criar proposta com influenciadores selecionados
  const handleCreateProposta = () => {
    setNewPropostaName('');
    setNewPropostaDescription('');
    setNewPropostaPriority('medium');
    setSelectedIcon('FileText');
    setNewPropostaDate(new Date());
    setShowPropostaSheet(true);
  };

  // ✅ NOVA FUNÇÃO: Salvar proposta
  const handleSaveProposta = async () => {
    if (!newPropostaName.trim()) {
      toast.error("Nome da proposta é obrigatório.");
      return;
    }

    if (activeBrandTab === 'all') {
      toast.error("Selecione uma marca específica para criar propostas.");
      return;
    }

    if (!activeBrandTab || activeBrandTab === 'default-brand') {
      toast.error("Marca não identificada. Selecione uma marca válida.");
      return;
    }

    const hasOrg = hasValidOrganization();
    
    if (!hasOrg || !currentOrganization) {
      toast.error("Você precisa fazer parte de uma organização para criar propostas.");
      return;
    }

    try {
      // Obter influenciadores selecionados da tabela
      const selectedInfluencerIds = selectedBacklogItems;
      
      if (selectedInfluencerIds.length === 0) {
        toast.error("Selecione pelo menos um influenciador para criar a proposta.");
        return;
      }

      // ✅ CORREÇÃO: Extrair IDs originais dos influenciadores (sem prefixo da marca)
      const originalInfluencerIds = selectedInfluencerIds.map(influencerId => {
        const influencer = backlogInfluencers.find(inf => inf.id === influencerId);
        return influencer?.originalInfluencerId || influencerId;
      });

      // ✅ NOVA ESTRUTURA: Criar proposta VAZIA primeiro (seguindo padrão de /propostas/id)
      const proposalData = {
        nome: newPropostaName,
        descricao: newPropostaDescription || '',
        criadoPor: authUser?.id || 'unknown-user',
        criadoPorNome: authUser?.name || authUser?.firstName || 'Usuário Desconhecido',
        organizationId: currentOrganization.id,
        organizationName: currentOrganization.name,
        influencers: [], // ✅ Array vazio - influenciadores irão para subcoleção
        brandId: activeBrandTab,
        services: [],
        totalAmount: 0,
        dataEnvio: newPropostaDate ? newPropostaDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        grupo: '',
        priority: newPropostaPriority,
        icon: selectedIcon
      };

      // ✅ 1. Criar proposta vazia
      const proposalId = await ProposalServiceLib.createProposal(proposalData);
      
      // ✅ 2. Adicionar influenciadores à subcoleção
      await ProposalServiceLib.addInfluencersToProposal(
        proposalId, 
        originalInfluencerIds, 
        authUser?.id || 'unknown-user'
      );
      
      // ✅ 3. Criar acesso via metadata do Clerk
      await createProposalAccess(proposalId, currentOrganization.id);
      
      toast.success(`Proposta criada com sucesso com ${selectedInfluencerIds.length} influenciador(es)!`);

      // Limpar estados
      setNewPropostaName('');
      setNewPropostaDescription('');
      setNewPropostaDate(new Date());
      setSelectedIcon('FileText');
      setNewPropostaPriority('medium');
      setShowPropostaSheet(false);
      resetBacklogSelection(); // Limpar seleção de influenciadores
      
    } catch (error) {
      console.error('❌ Erro ao criar proposta:', error);
      toast.error("Não foi possível criar a proposta.");
    }
  };

  // ✅ NOVA FUNÇÃO: Criar entrada de acesso usando metadata do Clerk
  const createProposalAccess = async (proposalId: string, organizationId: string) => {
    try {
      if (!authUser) {
        console.error('❌ [PROPOSAL_ACCESS] Usuário não encontrado');
        return;
      }

      // ✅ Usar metadata do Clerk para armazenar acesso
      const currentMetadata = (authUser.unsafeMetadata as any) || {};
      const newMetadata = {
        ...currentMetadata,
        p: {
          ...currentMetadata.p,
          [proposalId]: 'a' // a = agência (criador da proposta é sempre agência)
        }
      };

      await authUser.update({
        unsafeMetadata: newMetadata
      });

      console.log('✅ [PROPOSAL_ACCESS] Acesso adicionado ao metadata do Clerk:', {
        proposalId,
        role: 'admin',
        userId: authUser.id,
        organizationId
      });

    } catch (error) {
      console.error('❌ [PROPOSAL_ACCESS] Erro ao atualizar metadata:', error);
      // Não bloquear a criação da proposta por erro de acesso
    }
  };



  if (!userLoaded || !currentUser || !canAccess) {
    return (
      <div className="p-6">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
          <p className="text-muted-foreground">
            {!userLoaded 
              ? "Carregando informações do usuário..."
              : !currentUser 
              ? "Você precisa estar logado para acessar o backlog."
              : "Você só pode acessar seu próprio backlog."
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Loader Global */}
      <Loader isLoading={globalLoading} showLogo={true} message="" />
      
      <div className="flex bg-muted/30 dark:bg-[#080210] h-screen overflow-hidden">
        <div className="flex-1 flex flex-col h-full min-w-0"> {/* min-w-0 permite flexbox shrink */}
          <div className="p-4 lg:p-6 flex-none">
            <div className="mb-6 lg:mb-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 gap-2">
                <div className="min-w-0"> {/* min-w-0 para permitir text truncate */}
                  <h1 className="text-xl lg:text-2xl font-bold text-[#270038] dark:text-white">
                    Backlog de Influenciadores
                  </h1>
                  <p className="text-sm lg:text-base text-muted-foreground">
                    Gerencie todos os influenciadores associados às suas marcas
                  </p>
                </div>
              </div>

            
              
              {!backlogLoading && !backlogError && brandsSummary.length === 0 && (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">Nenhuma marca encontrada</h3>
                    <p className="text-muted-foreground mb-4">
                      Você ainda não possui marcas com influenciadores associados.
                    </p>
                    <p className="text-muted-foreground text-sm">
                      Para ter influenciadores no backlog, associe-os às suas marcas primeiro.
                    </p>
                  </CardContent>
                </Card>
              )}
              
           
            </div>
          </div>
          
          {/* DataTable */}
          <div className="px-4 lg:px-6 pb-4 lg:pb-6 flex-1 overflow-hidden min-w-0">
            {!backlogLoading && brandsSummary.length > 0 && (
              <div className="w-full h-full flex flex-col min-w-0">
                {/* Toolbar responsiva */}
                <div className="flex-none mb-4 space-y-3">
                  {/* Linha 1: Abas das marcas */}
                  <div className="overflow-x-auto">
                    <div className="flex items-center gap-1 min-w-max">
                      <Button
                        variant={activeBrandTab === 'all' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setActiveBrandTab('all')}
                        className={cn(
                          "flex items-center gap-2 whitespace-nowrap",
                          activeBrandTab === 'all' && "bg-gradient-to-r from-[#9810fa] to-[#ff0074] hover:bg-gradient-to-r hover:from-[#ff0074] hover:to-[#9810fa] text-white"
                        )}
                      >
                        <Building2 className="h-4 w-4" />
                        <span>Todas</span>
                        <Badge variant="secondary" className="ml-1 text-xs bg-muted text-muted-foreground">
                          {backlogInfluencers.length}
                        </Badge>
                      </Button>

                      {brandsSummary.map((brand) => (
                        <Button
                          key={brand.id}
                          variant={activeBrandTab === brand.id ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setActiveBrandTab(brand.id)}
                          className={cn(
                            "flex items-center gap-2 whitespace-nowrap",
                            activeBrandTab === brand.id && "bg-[#ec003f] hover:bg-[#ec003f]/90 text-white"
                          )}
                        >
                          <Avatar className="h-4 w-4">
                            <AvatarImage src={brand.logo} alt={brand.name} />
                            <AvatarFallback className="text-xs bg-[#270038] text-white">
                              {brand.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="truncate max-w-24">{brand.name}</span>
                          <Badge variant="secondary" className="ml-1 text-xs bg-muted text-muted-foreground">
                            {brand.count}
                          </Badge>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Linha 2: Controles */}
                  <div className="flex flex-wrap items-center gap-2">
                    <Button
                      variant="outline"
                      onClick={forceReloadBacklog}
                      disabled={backlogLoading}
                      size="sm"
                      className="whitespace-nowrap"
                    >
                      <RefreshCw className={cn("h-4 w-4 mr-2", backlogLoading && "animate-spin")} />
                      Atualizar
                    </Button>

                    <Badge variant="secondary" className="text-xs whitespace-nowrap">
                      {filteredBacklogInfluencers.length} encontrado(s)
                    </Badge>

                    {selectedBacklogItems.length > 0 && (
                      <Button
                        size="sm"
                        className="bg-[#9810fa] hover:bg-[#9810fa]/90 text-white whitespace-nowrap"
                        onClick={() => {
                          setSelectedInfluencersForList(selectedBacklogItems);
                          setShowAddToListDialog(true);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Lista ({selectedBacklogItems.length})
                      </Button>
                    )}

                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => setShowBacklogColumnDialog(true)}
                      className="border dark:border-[#1c1627] hover:bg-gray-50 whitespace-nowrap"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Colunas
                    </Button>
                  </div>
                </div>

                {/* Container da tabela */}
                <div className="flex-1 min-h-0">
                  <DataTable
                    data={filteredBacklogInfluencers}
                    columns={backlogColumns}
                    enableColumnOrdering={true}
                    onColumnOrderChange={handleBacklogColumnOrderChange}
                    columnOrder={backlogColumnOrder}
                    enableRowSelection={true}
                    onRowSelectionChange={handleBacklogRowSelectionChange}
                    searchKey="nome"
                    searchPlaceholder="Buscar influenciadores..."
                    className="w-full h-full"
                    pageSize={20}
                    toolbarActions={
                      <div className="flex items-center gap-2">
                        {/* Contador de seleção */}
                        {selectedBacklogItems.length > 0 && (
                          <Badge variant="secondary" className="hidden sm:inline-flex">
                            {selectedBacklogItems.length} selecionado(s)
                          </Badge>
                        )}

                        {/* Botão Criar Proposta */}
                        <Button 
                          onClick={handleCreateProposta}
                          className="bg-[#ff0074] text-white hover:bg-[#ff0074]/90"
                          disabled={selectedBacklogItems.length === 0 || activeBrandTab === 'all'}
                          title={
                            selectedBacklogItems.length === 0 
                              ? 'Selecione influenciadores para criar proposta' 
                              : activeBrandTab === 'all' 
                              ? 'Selecione uma marca específica para criar proposta'
                              : 'Criar proposta com influenciadores selecionados'
                          }
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Criar Proposta
                        </Button>
                      </div>
                    }
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Dialog: Configuração de Colunas do Backlog */}
        <Dialog open={showBacklogColumnDialog} onOpenChange={setShowBacklogColumnDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-[#ec003f]" />
                Configurar Colunas do Backlog
              </DialogTitle>
              <p className="text-sm text-muted-foreground">
                Selecione quais colunas você deseja visualizar na tabela do backlog de influenciadores.
              </p>
            </DialogHeader>

            <div className="space-y-6">
              {/* Seção: Colunas Principais */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-[#270038] dark:text-white">📊 Colunas Principais</h4>
                <div className="grid grid-cols-2 gap-3">
                  {['nomeInfluencer', 'marca', 'proposalStatus', 'plataforma', 'seguidores', 'country', 'location', 'age', 'gender', 'category'].map((column) => (
                    <div key={column} className="flex items-center space-x-2">
                      <Checkbox
                        id={`col-${column}`}
                        checked={backlogVisibleColumns[column] !== false}
                        onCheckedChange={() => toggleBacklogColumnVisibility(column)}
                      />
                      <Label htmlFor={`col-${column}`} className="text-sm cursor-pointer">
                        {backlogColumnLabels[column]}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Seção: Colunas Avançadas */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-[#270038] dark:text-white">⚙️ Colunas Avançadas</h4>
                <div className="grid grid-cols-2 gap-3">
                  {['verified', 'mainNetwork', 'engagementRate', 'promotesTraders', 'responsibleInfo', 'contato'].map((column) => (
                    <div key={column} className="flex items-center space-x-2">
                      <Checkbox
                        id={`col-${column}`}
                        checked={backlogVisibleColumns[column] !== false}
                        onCheckedChange={() => toggleBacklogColumnVisibility(column)}
                      />
                      <Label htmlFor={`col-${column}`} className="text-sm cursor-pointer">
                        {backlogColumnLabels[column]}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Seção: Colunas Detalhadas de Redes Sociais */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-[#270038] dark:text-white">📱 Detalhamento de Redes Sociais</h4>
                <div className="grid grid-cols-2 gap-3">
                  {['instagram_followers', 'instagram_views', 'tiktok_followers', 'tiktok_views', 'youtube_followers', 'youtube_views'].map((column) => (
                    <div key={column} className="flex items-center space-x-2">
                      <Checkbox
                        id={`col-${column}`}
                        checked={backlogVisibleColumns[column] !== false}
                        onCheckedChange={() => toggleBacklogColumnVisibility(column)}
                      />
                      <Label htmlFor={`col-${column}`} className="text-sm cursor-pointer">
                        {backlogColumnLabels[column]}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Seção: Ações */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-[#270038] dark:text-white">🎯 Controles</h4>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="col-acoes"
                    checked={backlogVisibleColumns['acoes'] !== false}
                    onCheckedChange={() => toggleBacklogColumnVisibility('acoes')}
                  />
                  <Label htmlFor="col-acoes" className="text-sm cursor-pointer">
                    {backlogColumnLabels['acoes']}
                  </Label>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between pt-6 border-t">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Restaurar ordem padrão das colunas com marca na terceira posição
                    const defaultOrder = [
                      'select',
                      'nomeInfluencer', 
                      'marca',
                      'proposalStatus', 
                      'plataforma', 
                      'seguidores', 
                      'country', 
                      'category', 
                      'location', 
                      'age', 
                      'gender', 
                      'verified', 
                      'mainNetwork', 
                      'engagementRate', 
                      'promotesTraders', 
                      'responsibleInfo', 
                      'contato',
                      'instagram_followers',
                      'instagram_views',
                      'tiktok_followers',
                      'tiktok_views',
                      'youtube_followers',
                      'youtube_views',
                      'acoes'
                    ];
                    
                    console.log('🔄 [RESTORE_DEFAULT] Restaurando ordem padrão das colunas:', defaultOrder);
                    
                    // Limpar configurações antigas
                    localStorage.removeItem('backlog-table-columns');
                    localStorage.removeItem('backlog-table-order');
                    
                    setBacklogVisibleColumns(defaultBacklogVisibleColumns);
                    setBacklogColumnOrder(defaultOrder);
                    saveBacklogColumnSettings(defaultBacklogVisibleColumns, defaultOrder);
                    
                    toast.success('Ordem das colunas restaurada com sucesso!');
                  }}
                >
                  Restaurar Padrão
                </Button>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowBacklogColumnDialog(false)}
                >
                  Cancelar
                </Button>
                <Button
                  className="bg-[#ec003f]  text-white hover:bg-[#d10037]"
                  onClick={() => {
                    setShowBacklogColumnDialog(false);
                    toast.success('Configurações das colunas salvas com sucesso!');
                  }}
                >
                  Salvar Configurações
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>



        {/* Sheet: Criar Proposta (Unificado) */}
        <Sheet open={showPropostaSheet} onOpenChange={setShowPropostaSheet}>
          <SheetContent className="w-[400px] sm:w-[500px]">
            <SheetHeader>
              <SheetTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5 text-[#ff0074]" />
                Criar Nova Proposta
              </SheetTitle>
              <SheetDescription>
                Preencha os detalhes da proposta. Os influenciadores selecionados serão incluídos automaticamente.
              </SheetDescription>
            </SheetHeader>
            
            <div className="mt-6 space-y-6">
              <div className="space-y-2">
                <label className="text-sm font-medium">Nome da Proposta *</label>
                <Input
                  value={newPropostaName}
                  onChange={(e) => setNewPropostaName(e.target.value)}
                  placeholder="Ex: Proposta Campanha Verão 2024"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Descrição/Grupo (Opcional)</label>
                <Textarea
                  value={newPropostaDescription}
                  onChange={(e) => setNewPropostaDescription(e.target.value)}
                  placeholder="Descrição da proposta ou nome do grupo..."
                  rows={3}
                  className="resize-none"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Data da Proposta
                </label>
                <Input
                  type="date"
                  value={newPropostaDate ? newPropostaDate.toISOString().split('T')[0] : ''}
                  onChange={(e) => {
                    const date = e.target.value ? new Date(e.target.value) : new Date();
                    setNewPropostaDate(date);
                  }}
                  className="bg-background/50 border-border/50 focus:border-[#ff0074]/50 focus:ring-[#ff0074]/20 transition-all duration-200"
                />
              </div>

              {/* Seção: Seleção de Ícone */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Ícone da Proposta</label>
                <div className="grid grid-cols-6 gap-2">
                  {AVAILABLE_ICONS.map((iconItem) => {
                    const IconComponent = iconItem.icon;
                    return (
                      <Button
                        key={iconItem.name}
                        variant={selectedIcon === iconItem.name ? "default" : "outline"}
                        size="sm"
                        className={cn(
                          "h-10 w-10 p-2",
                          selectedIcon === iconItem.name && "bg-[#ff0074] text-white hover:bg-[#ff0074]/90"
                        )}
                        onClick={() => setSelectedIcon(iconItem.name)}
                        title={iconItem.label}
                      >
                        <IconComponent className="h-4 w-4" />
                      </Button>
                    );
                  })}
                </div>
              </div>

              {/* Seção: Prioridade */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Prioridade</label>
                <div className="flex gap-2">
                  {(['low', 'medium', 'high'] as ProposalPriority[]).map((priority) => (
                    <Button
                      key={priority}
                      variant={newPropostaPriority === priority ? "default" : "outline"}
                      size="sm"
                      className={cn(
                        newPropostaPriority === priority && "bg-[#ff0074] text-white hover:bg-[#ff0074]/90"
                      )}
                      onClick={() => setNewPropostaPriority(priority)}
                    >
                      {priority === 'low' && 'Baixa'}
                      {priority === 'medium' && 'Média'}
                      {priority === 'high' && 'Alta'}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Informações sobre influenciadores selecionados */}
              {selectedBacklogItems.length > 0 && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Influenciadores Selecionados</label>
                  <div className="bg-muted/50 rounded-lg p-3">
                    <p className="text-sm text-muted-foreground">
                      {selectedBacklogItems.length} influenciador(es) serão incluídos na proposta
                    </p>
                    {activeBrandTab !== 'all' && brands.find(b => b.id === activeBrandTab) && (
                      <p className="text-sm text-muted-foreground mt-1">
                        Marca: {brands.find(b => b.id === activeBrandTab)?.name || 'Marca selecionada'}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center justify-end gap-3 mt-8">
              <Button 
                variant="outline" 
                onClick={() => setShowPropostaSheet(false)} 
              >
                Cancelar
              </Button>
              <Button 
                onClick={handleSaveProposta} 
                className="bg-[#ff0074] text-white hover:bg-[#ff0074]/90"
                disabled={!newPropostaName.trim() || selectedBacklogItems.length === 0}
              >
                Criar Proposta
              </Button>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
} 