import { useState, useCallback, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { 
  InfluencerFormSchema, 
  type InfluencerFormData,
  type SocialPlatform,
  PLATFORM_CONFIG 
} from '@/types/influencer-form'
import { useToast } from '@/components/ui/use-toast'
import { compressImage } from '@/lib/utils/image-compression'

interface UseInfluencerFormProps {
  initialData?: any
  onSubmit?: (data: InfluencerFormData) => Promise<void>
}

// Função para converter dados do Firestore para o formato do formulário
function convertFromFirestoreFormat(data: any): InfluencerFormData {
  if (!data) {
    return {
      personalInfo: { name: "", verified: false },
      location: { country: "Brasil" },
      contact: {},
      business: { promotesTraders: false, brandPartnerships: [] },
      brands: []
    }
  }


  // Mapear gênero do Firestore
  const genderMap: { [key: string]: 'Masculino' | 'Feminino' } = {
    'male': 'Mas<PERSON><PERSON>', 
    'female': 'Feminino'
    // outros valores (other, not_specified) não serão mapeados - ficam undefined
  }

  // Converter plataformas do Firestore
  const platforms: any = {}
  
  // NOVA ESTRUTURA: Campos diretos (instagramUsername, tiktokFollowers, etc.)
  const directFieldPlatforms = [
    { name: 'instagram', usernameField: 'instagramUsername', followersField: 'instagramFollowers', engagementField: 'instagramEngagementRate', viewsField: 'instagramAvgViews' },
    { name: 'tiktok', usernameField: 'tiktokUsername', followersField: 'tiktokFollowers', engagementField: 'tiktokEngagementRate', viewsField: 'tiktokAvgViews' },
    { name: 'youtube', usernameField: 'youtubeUsername', followersField: 'youtubeFollowers', engagementField: 'youtubeEngagementRate', viewsField: 'youtubeAvgViews' },
    { name: 'facebook', usernameField: 'facebookUsername', followersField: 'facebookFollowers', engagementField: 'facebookEngagementRate', viewsField: 'facebookAvgViews' },
    { name: 'twitch', usernameField: 'twitchUsername', followersField: 'twitchFollowers', engagementField: 'twitchEngagementRate', viewsField: 'twitchViews' },
    { name: 'kwai', usernameField: 'kwaiUsername', followersField: 'kwaiFollowers', engagementField: 'kwaiEngagementRate', viewsField: 'kwaiViews' }
  ]
  
  // Processar campos diretos primeiro (nova estrutura)
  directFieldPlatforms.forEach(({ name, usernameField, followersField, engagementField, viewsField }) => {
    const username = data[usernameField]
    const followers = data[followersField]
    
    const hasViewsData = data[viewsField] && Number(data[viewsField]) > 0
    const hasOtherViewsData = (name === 'instagram' && (data.instagramStoriesViews > 0 || data.instagramReelsViews > 0)) ||
                              (name === 'tiktok' && data.tiktokVideoViews > 0) ||
                              (name === 'youtube' && (data.youtubeShortsViews > 0 || data.youtubeLongFormViews > 0)) ||
                              (name === 'facebook' && (data.facebookReelsViews > 0 || data.facebookStoriesViews > 0))
    
    const hasPricingData = data.currentPricing?.services?.[name] && 
                          Object.keys(data.currentPricing.services[name]).length > 0
    const hasDirectPricingData = (name === 'facebook' && data.facebookPost > 0) ||
                                (name === 'twitch' && data.twitchStream > 0) ||
                                (name === 'kwai' && data.kwaiVideo > 0)
    
    if ((username && username.trim() !== '') || (followers && followers > 0) || hasViewsData || hasOtherViewsData || hasPricingData || hasDirectPricingData) {
      const platformData: any = {
        username: username || "",
        followers: Number(followers) || 0,
        engagementRate: Number(data[engagementField]) || 0,
        pricing: {},
        metrics: {},
        audienceGender: { male: 0, female: 0, other: 0 },
        audienceLocations: [],
        audienceCities: [],
        audienceAgeRanges: {},
        brandHistory: [],
        screenshots: []
      }
      
      // Adicionar campos específicos de visualizações por plataforma
      if (name === 'instagram') {
        platformData.views = {
          storiesViews: Number(data.instagramStoriesViews) || 0,
          reelsViews: Number(data.instagramReelsViews) || 0
        }
      } else if (name === 'tiktok') {
        platformData.views = {
          videoViews: Number(data.tiktokVideoViews) || 0
        }
      } else if (name === 'youtube') {
        platformData.views = {
          shortsViews: Number(data.youtubeShortsViews) || 0,
          longFormViews: Number(data.youtubeLongFormViews) || 0
        }
      } else if (name === 'facebook') {
        platformData.views = {
          reelsViews: Number(data.facebookReelsViews) || 0,
          storiesViews: Number(data.facebookStoriesViews) || 0
        }
        // Mapear pricing direto do Facebook
        if (data.facebookPost && Number(data.facebookPost) > 0) {
          platformData.pricing.post = Number(data.facebookPost) || 0
        }
      } else if (name === 'twitch') {
        platformData.avgViews = Number(data.twitchViews) || 0
        // Mapear pricing direto do Twitch
        if (data.twitchStream && Number(data.twitchStream) > 0) {
          platformData.pricing.stream = Number(data.twitchStream) || 0
        }
      } else if (name === 'kwai') {
        platformData.avgViews = Number(data.kwaiViews) || 0
        // Mapear pricing direto do Kwai
        if (data.kwaiVideo && Number(data.kwaiVideo) > 0) {
          platformData.pricing.video = Number(data.kwaiVideo) || 0
        }
      }
      
      platforms[name] = platformData
    }
  })
  
  // Fallback para estrutura antiga (socialNetworks) se nenhum campo direto foi encontrado
  if (Object.keys(platforms).length === 0 && data.socialNetworks) {
    console.log('🔄 Usando estrutura antiga (socialNetworks) como fallback')
    Object.keys(data.socialNetworks).forEach(platform => {
      const networkData = data.socialNetworks[platform]
      if (networkData && networkData.followers > 0) {
        platforms[platform] = {
          username: networkData.username || "",
          followers: Number(networkData.followers) || 0,
          engagementRate: networkData.engagementRate ? Number(networkData.engagementRate) : undefined,
          pricing: networkData.pricing || {},
          metrics: networkData.metrics || {},
          audienceGender: networkData.audienceGender || { male: 0, female: 0, other: 0 },
          audienceLocations: networkData.audienceLocations || [],
          audienceCities: networkData.audienceCities || [],
          audienceAgeRange: networkData.audienceAgeRange || {},
          brandHistory: networkData.brandHistory || [],
          screenshots: networkData.screenshots || []
        }
      }
    })
  }

  // 💰 BUSCAR PREÇOS DA NOVA ESTRUTURA SEPARADA (currentPricing)
  if (data.currentPricing?.services && Object.keys(platforms).length > 0) {
    
    Object.keys(platforms).forEach(platform => {
      const pricingData = data.currentPricing.services[platform]
      if (pricingData) {
        
        if (platform === 'instagram') {
          if (pricingData.story?.price) {
            platforms[platform].pricing.story = Number(pricingData.story.price) || 0
          }
          if (pricingData.reel?.price) {
            platforms[platform].pricing.reel = Number(pricingData.reel.price) || 0
          }
        }
        if (platform === 'youtube') {
          if (pricingData.insertion?.price) {
            platforms[platform].pricing.insertion = Number(pricingData.insertion.price) || 0
          }
          if (pricingData.dedicated?.price) {
            platforms[platform].pricing.dedicated = Number(pricingData.dedicated.price) || 0
          }
          if (pricingData.shorts?.price) {
            platforms[platform].pricing.shorts = Number(pricingData.shorts.price) || 0
          }
        }
        if (platform === 'tiktok' && pricingData.video?.price) {
          platforms[platform].pricing.video = Number(pricingData.video.price) || 0
        }
        if (platform === 'facebook' && pricingData.post?.price) {
          platforms[platform].pricing.post = Number(pricingData.post.price) || 0
        }
        if (platform === 'twitch' && pricingData.stream?.price) {
          platforms[platform].pricing.stream = Number(pricingData.stream.price) || 0
        }
        if (platform === 'kwai' && pricingData.video?.price) {
          platforms[platform].pricing.video = Number(pricingData.video.price) || 0
        }
      } else {
      }
    })
  } else {
    // Logs removidos para limpar console
  }
  
  // Fallback para estrutura antiga (financialData) se a nova não existir
  const financialData = data.financials || data.financialData
  if (!data.currentPricing && financialData?.prices && Object.keys(platforms).length > 0) {
    Object.keys(platforms).forEach(platform => {
      if (platform === 'instagram') {
        if (financialData.prices.instagramStory) {
          platforms[platform].pricing.story = Number(financialData.prices.instagramStory.price) || 0
        }
        if (financialData.prices.instagramReel) {
          platforms[platform].pricing.reel = Number(financialData.prices.instagramReel.price) || 0
        }
      }
      if (platform === 'youtube') {
        if (financialData.prices.youtubeInsertion) {
          platforms[platform].pricing.insertion = Number(financialData.prices.youtubeInsertion.price) || 0
        }
        if (financialData.prices.youtubeDedicated) {
          platforms[platform].pricing.dedicated = Number(financialData.prices.youtubeDedicated.price) || 0
        }
        if (financialData.prices.youtubeShorts) {
          platforms[platform].pricing.shorts = Number(financialData.prices.youtubeShorts.price) || 0
        }
      }
      if (platform === 'tiktok' && financialData.prices.tiktokVideo) {
        platforms[platform].pricing.video = Number(financialData.prices.tiktokVideo.price) || 0
      }
    })
  }
  
  // 📊 BUSCAR DEMOGRAPHICS DA NOVA ESTRUTURA SEPARADA (currentDemographics)
  if (data.currentDemographics && Array.isArray(data.currentDemographics) && Object.keys(platforms).length > 0) {
    
    data.currentDemographics.forEach((demographic: any) => {
      const platform = demographic.platform
      if (platforms[platform]) {
        
        // Audiência por gênero
        if (demographic.audienceGender) {
          platforms[platform].audienceGender = {
            male: demographic.audienceGender.male || 0,
            female: demographic.audienceGender.female || 0,
            other: demographic.audienceGender.other || 0
          }
        }
        
        // Localizações da audiência
        if (demographic.audienceLocations) {
          platforms[platform].audienceLocations = demographic.audienceLocations
        }
        
        // Cidades da audiência
        if (demographic.audienceCities) {
          platforms[platform].audienceCities = demographic.audienceCities
        }
        
        // Faixas etárias da audiência - converter de array ou objeto
        if (demographic.audienceAgeRange) {
          let ageRangesArray: any[] = []
          
          if (Array.isArray(demographic.audienceAgeRange)) {
            // Se já é array, usar direto
            ageRangesArray = demographic.audienceAgeRange.filter((item: any) => 
              item && item.range && typeof item.percentage === 'number'
            )
          } else if (typeof demographic.audienceAgeRange === 'object') {
            // Se é objeto, converter para array
            ageRangesArray = Object.entries(demographic.audienceAgeRange).map(([range, percentage]) => ({
              range,
              percentage: Number(percentage) || 0
            })).filter(item => item.range && !isNaN(item.percentage))
          }
          
          platforms[platform].audienceAgeRange = ageRangesArray
                 } else {
           // Se não há dados, criar array vazio
           platforms[platform].audienceAgeRange = []
        }
      }
    })
  }


  // Lógica melhorada para extrair avatar - verificar múltiplos campos
  const getAvatarFromData = (data: any): string | undefined => {
    // Log removido para limpar console

    // Lista de possíveis campos onde o avatar pode estar
    const avatarFields = [
      data.avatar,
      data.avatarUrl, 
      data.foto,
      data.image,
      data.picture,
      data.profileImage
    ]

    for (const field of avatarFields) {
      if (field && typeof field === 'string' && field.trim() !== '') {
        // Aceitar qualquer URL que comece com https:// ou data:
        if (field.startsWith('https://') || field.startsWith('data:image/')) {
          return field
        }
        // Também aceitar paths relativos (para migração futura)
        if (field.startsWith('/uploads/') || field.startsWith('./uploads/')) {
          console.log('📁 Avatar local encontrado (será migrado):', field)
          return field
        }
      }
    }

    return undefined
  }

  const avatarUrl = getAvatarFromData(data)

  return {
    id: data.id || undefined, // Preservar o ID do documento Firestore
    personalInfo: {
      name: data.name || "",
      age: data.age || undefined,
      gender: genderMap[data.gender] || undefined,
      bio: data.bio || undefined,
      avatar: avatarUrl,
      verified: data.isVerified || data.verified || false,
    },
    location: {
      country: data.country || "Brasil",
      state: data.state || undefined,
      city: data.city || undefined,
      cep: data.cep || undefined,
    },
    contact: {
      email: data.email || undefined,
      whatsapp: data.whatsapp || data.phone || undefined,
    },
    business: {
      agencyName: financialData?.agencyName || data.agencyName || undefined,
      responsibleName: financialData?.responsibleName || data.responsibleName || undefined,
      responsibleCapturer: financialData?.responsibleCapturer || data.responsibleCapturer || financialData?.additionalData?.responsibleRecruiter || undefined,
      categories: data.mainCategories || data.categories || [],
      promotesTraders: data.promotesTraders || financialData?.additionalData?.promotesTraders || false,
      brandPartnerships: data.brandPartnerships || [],
    },
    brands: [], // Campo para novas marcas selecionadas (não incluir marcas já associadas)
    platforms: Object.keys(platforms).length > 0 ? platforms as any : undefined,
    mainPlatform: data.mainNetwork || data.mainPlatform || undefined,
    metrics: {
      totalFollowers: data.totalFollowers || 0,
      overallEngagementRate: data.engagementRate || data.overallEngagementRate || 0,
    },
  }
}

// Função para converter dados do formulário para o formato do GraphQL
function convertToGraphQLFormat(formData: InfluencerFormData): any {
  console.log('🔍 [ENTRADA convertToGraphQLFormat] formData recebido:', JSON.stringify(formData, null, 2))
  console.log('🔍 [ENTRADA] formData.platforms:', formData.platforms)
  console.log('🔍 [ENTRADA] Twitch avgViews:', (formData.platforms as any)?.twitch?.avgViews)
  console.log('🔍 [ENTRADA] Kwai avgViews:', (formData.platforms as any)?.kwai?.avgViews)

  // Mapear gênero para o GraphQL
  const genderMap: { [key: string]: string } = {
    'Masculino': 'male',
    'Feminino': 'female'
  }

  // Dados para o GraphQL CreateInfluencerInput - apenas campos com valores
  const graphqlInput: any = {}

  // Incluir apenas campos preenchidos
  if (formData.personalInfo?.name && formData.personalInfo.name.trim() !== '') {
    graphqlInput.name = formData.personalInfo.name.trim()
  }

  if (formData.contact?.email && formData.contact.email.trim() !== '') {
    graphqlInput.email = formData.contact.email.trim()
  }

  if (formData.contact?.whatsapp && formData.contact.whatsapp.trim() !== '') {
    graphqlInput.phone = formData.contact.whatsapp.trim()
    graphqlInput.whatsapp = formData.contact.whatsapp.trim()
  }

  if (formData.location?.country && formData.location.country.trim() !== '') {
    graphqlInput.country = formData.location.country.trim()
  }

  if (formData.location?.state && formData.location.state.trim() !== '') {
    graphqlInput.state = formData.location.state.trim()
  }

  if (formData.location?.city && formData.location.city.trim() !== '') {
    graphqlInput.city = formData.location.city.trim()
  }

  // Criar location combinada se city e state existirem
  if (formData.location?.city && formData.location?.state) {
    graphqlInput.location = `${formData.location.city}/${formData.location.state}`
  }

  if (formData.personalInfo?.age && formData.personalInfo.age > 0) {
    graphqlInput.age = formData.personalInfo.age
  }

  if (formData.personalInfo?.gender && genderMap[formData.personalInfo.gender]) {
    graphqlInput.gender = genderMap[formData.personalInfo.gender]
  }

  if (formData.personalInfo?.bio && formData.personalInfo.bio.trim() !== '') {
    graphqlInput.bio = formData.personalInfo.bio.trim()
  }

  if (formData.business?.categories && formData.business.categories.length > 0) {
    graphqlInput.category = formData.business.categories[0]
    graphqlInput.categories = formData.business.categories
  }

  if (formData.personalInfo?.avatar && formData.personalInfo.avatar.trim() !== '') {
    graphqlInput.avatar = formData.personalInfo.avatar.trim()
  }

  if (formData.personalInfo?.verified) {
    graphqlInput.isVerified = formData.personalInfo.verified
  }

  // Adicionar dados de métricas
  if (formData.metrics?.totalFollowers && formData.metrics.totalFollowers > 0) {
    graphqlInput.totalFollowers = formData.metrics.totalFollowers
  }

  if (formData.metrics?.overallEngagementRate && formData.metrics.overallEngagementRate > 0) {
    graphqlInput.engagementRate = formData.metrics.overallEngagementRate
  }

  // Dados profissionais
  if (formData.business?.agencyName && formData.business.agencyName.trim() !== '') {
    graphqlInput.agencyName = formData.business.agencyName.trim()
  }

  if (formData.business?.responsibleName && formData.business.responsibleName.trim() !== '') {
    graphqlInput.responsibleName = formData.business.responsibleName.trim()
  }

  if (formData.business?.promotesTraders) {
    graphqlInput.promotesTraders = formData.business.promotesTraders
  }

  // 🔥 CORREÇÃO: Adicionar responsibleCapturer
  if (formData.business?.responsibleCapturer && formData.business.responsibleCapturer.trim() !== '') {
    graphqlInput.responsibleCapturer = formData.business.responsibleCapturer.trim()
  }

  // 💰 [CORREÇÃO CRÍTICA] Incluir dados de socialNetworks com pricing
  if (formData.platforms && Object.keys(formData.platforms).length > 0) {
    const socialNetworks: any = {}
    
    Object.keys(formData.platforms).forEach(platformKey => {
      const platformData = (formData.platforms as any)[platformKey]
      if (platformData && (platformData.followers > 0 || platformData.username || platformData.avgViews > 0 || (platformData.views && Object.values(platformData.views).some((v: any) => v > 0)))) {
        
        const socialNetworkData: any = {
          username: platformData.username || "",
          followers: Number(platformData.followers) || 0,
          engagementRate: Number(platformData.engagementRate) || 0,
        }

        // Adicionar views específicas da plataforma
        if (platformData.views) {
          socialNetworkData.views = platformData.views
        }

        // Adicionar campos específicos por plataforma
        if (platformKey === 'instagram') {
          socialNetworkData.storiesViews = platformData.views?.storiesViews || 0
          socialNetworkData.reelsViews = platformData.views?.reelsViews || 0
        } else if (platformKey === 'youtube') {
          socialNetworkData.subscribers = Number(platformData.followers) || 0
          socialNetworkData.shortsViews = platformData.views?.shortsViews || 0
          socialNetworkData.longFormViews = platformData.views?.longFormViews || 0
        } else if (platformKey === 'tiktok') {
          socialNetworkData.videoViews = platformData.views?.videoViews || 0
        } else if (platformKey === 'facebook') {
          socialNetworkData.reelsViews = platformData.views?.reelsViews || 0
          socialNetworkData.storiesViews = platformData.views?.storiesViews || 0
        }

        // 💰 [CRUCIAL] Adicionar dados de pricing se existirem
        if (platformData.pricing && Object.keys(platformData.pricing).length > 0) {
          socialNetworkData.pricing = {}
          
          // Mapear todos os campos de pricing disponíveis
          Object.keys(platformData.pricing).forEach(priceType => {
            const priceValue = platformData.pricing[priceType]
            if (priceValue && priceValue > 0) {
              socialNetworkData.pricing[priceType] = Number(priceValue)
            }
          })
          
        }

        socialNetworks[platformKey] = socialNetworkData
      }
    })

    if (Object.keys(socialNetworks).length > 0) {
      graphqlInput.socialNetworks = socialNetworks
    }
  }

  // Redes sociais como campos diretos (nova estrutura)
  if (formData.platforms && Object.keys(formData.platforms).length > 0) {
    console.log('📱 [GraphQL Convert] Processando plataformas:', Object.keys(formData.platforms))
    console.log('📱 [GraphQL Convert] Dados completos das plataformas:', JSON.stringify(formData.platforms, null, 2))
    
    Object.keys(formData.platforms).forEach(platformKey => {
      const platformData = (formData.platforms as any)[platformKey]
      console.log(`🔍 [CONDITION] Verificando plataforma ${platformKey}:`, {
        hasData: !!platformData,
        followers: platformData?.followers,
        username: platformData?.username,
        avgViews: platformData?.avgViews,
        views: platformData?.views,
        shouldProcess: platformData && (platformData.followers > 0 || platformData.username || platformData.avgViews > 0 || (platformData.views && Object.values(platformData.views).some((v: any) => v > 0)))
      })
      
      if (platformData && (platformData.followers > 0 || platformData.username || platformData.avgViews > 0 || (platformData.views && Object.values(platformData.views).some((v: any) => v > 0)))) {
        console.log(`✅ [PROCESSING] Processando plataforma: ${platformKey}`)
        
        // Instagram - campos diretos
        if (platformKey === 'instagram') {
          graphqlInput.instagramUsername = platformData.username || ''
          graphqlInput.instagramFollowers = Number(platformData.followers) || 0
          graphqlInput.instagramEngagementRate = Number(platformData.engagementRate) || 0
          
          // Campos específicos de visualizações do Instagram
          graphqlInput.instagramStoriesViews = Number(platformData.views?.storiesViews) || 0
          graphqlInput.instagramReelsViews = Number(platformData.views?.reelsViews) || 0
          
          console.log(`📱 [GraphQL Convert] Instagram campos diretos:`, {
            username: graphqlInput.instagramUsername,
            followers: graphqlInput.instagramFollowers,
            storiesViews: graphqlInput.instagramStoriesViews,
            reelsViews: graphqlInput.instagramReelsViews
          })
        }
        
        // TikTok - campos diretos
        if (platformKey === 'tiktok') {
          graphqlInput.tiktokUsername = platformData.username || ''
          graphqlInput.tiktokFollowers = Number(platformData.followers) || 0
          graphqlInput.tiktokEngagementRate = Number(platformData.engagementRate) || 0
          
          // Campos específicos de visualizações do TikTok
          graphqlInput.tiktokVideoViews = Number(platformData.views?.videoViews) || 0
          
          console.log(`📱 [GraphQL Convert] TikTok campos diretos:`, {
            username: graphqlInput.tiktokUsername,
            followers: graphqlInput.tiktokFollowers,
            videoViews: graphqlInput.tiktokVideoViews
          })
        }
        
        // YouTube - campos diretos
        if (platformKey === 'youtube') {
          graphqlInput.youtubeUsername = platformData.username || ''
          graphqlInput.youtubeFollowers = Number(platformData.followers) || 0
          graphqlInput.youtubeSubscribers = Number(platformData.followers) || 0
          graphqlInput.youtubeEngagementRate = Number(platformData.engagementRate) || 0
          
          // Campos específicos de visualizações do YouTube
          graphqlInput.youtubeShortsViews = Number(platformData.views?.shortsViews) || 0
          graphqlInput.youtubeLongFormViews = Number(platformData.views?.longFormViews) || 0
          
          console.log(`📱 [GraphQL Convert] YouTube campos diretos:`, {
            username: graphqlInput.youtubeUsername,
            followers: graphqlInput.youtubeFollowers,
            shortsViews: graphqlInput.youtubeShortsViews,
            longFormViews: graphqlInput.youtubeLongFormViews
          })
        }
        
        // Facebook - campos diretos (reels e stories views)
        if (platformKey === 'facebook') {
          graphqlInput.facebookUsername = platformData.username || ''
          graphqlInput.facebookFollowers = Number(platformData.followers) || 0
          graphqlInput.facebookEngagementRate = Number(platformData.engagementRate) || 0
          
          // Campos específicos de visualizações do Facebook (Reels e Stories)
          graphqlInput.facebookReelsViews = Number(platformData.views?.reelsViews) || 0
          graphqlInput.facebookStoriesViews = Number(platformData.views?.storiesViews) || 0
          
          console.log(`🔍 [DEBUG] Facebook platformData RAW:`, platformData)
          console.log(`🔍 [DEBUG] Facebook views object:`, platformData.views)
          console.log(`📱 [GraphQL Convert] Facebook campos diretos:`, {
            username: graphqlInput.facebookUsername,
            followers: graphqlInput.facebookFollowers,
            reelsViews: graphqlInput.facebookReelsViews,
            storiesViews: graphqlInput.facebookStoriesViews
          })
        }
        
        // Twitch - campos diretos
        if (platformKey === 'twitch') {
          graphqlInput.twitchUsername = platformData.username || ''
          graphqlInput.twitchFollowers = Number(platformData.followers) || 0
          graphqlInput.twitchEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.twitchViews = Number(platformData.avgViews) || 0
          
          console.log(`🔍 [DEBUG] Twitch platformData RAW:`, platformData)
          console.log(`🔍 [DEBUG] Twitch avgViews value:`, platformData.avgViews)
          console.log(`🔍 [DEBUG] Twitch avgViews type:`, typeof platformData.avgViews)
          console.log(`🔍 [DEBUG] Twitch avgViews isEmpty:`, !platformData.avgViews)
          console.log(`📱 [GraphQL Convert] Twitch campos diretos:`, {
            username: graphqlInput.twitchUsername,
            followers: graphqlInput.twitchFollowers,
            avgViews: graphqlInput.twitchViews,
            avgViewsRaw: platformData.avgViews
          })
        }
        
        // Kwai - campos diretos
        if (platformKey === 'kwai') {
          graphqlInput.kwaiUsername = platformData.username || ''
          graphqlInput.kwaiFollowers = Number(platformData.followers) || 0
          graphqlInput.kwaiEngagementRate = Number(platformData.engagementRate) || 0
          graphqlInput.kwaiViews = Number(platformData.avgViews) || 0
          
          console.log(`🔍 [DEBUG] Kwai platformData RAW:`, platformData)
          console.log(`🔍 [DEBUG] Kwai avgViews value:`, platformData.avgViews)
          console.log(`🔍 [DEBUG] Kwai avgViews type:`, typeof platformData.avgViews)
          console.log(`🔍 [DEBUG] Kwai avgViews isEmpty:`, !platformData.avgViews)
          console.log(`📱 [GraphQL Convert] Kwai campos diretos:`, {
            username: graphqlInput.kwaiUsername,
            followers: graphqlInput.kwaiFollowers,
            avgViews: graphqlInput.kwaiViews,
            avgViewsRaw: platformData.avgViews
          })
        }
      }
    })
  }

  // Plataforma principal
  console.log('🎯 [GraphQL Convert] formData completo:', JSON.stringify(formData, null, 2))
  console.log('🎯 [GraphQL Convert] formData.mainPlatform trim?:', formData.mainPlatform?.trim?.())
  
  if (formData.mainPlatform && formData.mainPlatform.trim() !== '') {
    graphqlInput.mainPlatform = formData.mainPlatform.trim()
    graphqlInput.mainNetwork = formData.mainPlatform.trim() // Compatibilidade
  } else {
    console.log('❌ [GraphQL Convert] Condições: existe?', !!formData.mainPlatform, 'trim vazio?', formData.mainPlatform?.trim() === '')
  }

  // Campos padrão sempre presentes
  graphqlInput.isAvailable = true
  graphqlInput.status = 'active'

  console.log('💾 Dados convertidos para GraphQL (apenas campos preenchidos):', graphqlInput)
  
  return graphqlInput
}

// Função para converter dados do formulário para o formato do Firestore
function convertToFirestoreFormat(formData: InfluencerFormData): { influencerData: any, financialData: any } {

  // Mapear gênero para o Firestore
  const genderMap: { [key: string]: string } = {
    'Masculino': 'male',
    'Feminino': 'female'
  }

  // Dados principais do influenciador
  const influencerData: any = {
    name: formData.personalInfo.name,
    age: formData.personalInfo.age,
    gender: formData.personalInfo.gender ? genderMap[formData.personalInfo.gender] : 'not_specified',
    bio: formData.personalInfo.bio,
    avatar: formData.personalInfo.avatar,
    isVerified: formData.personalInfo.verified,
    country: formData.location.country || "Brasil",
    state: formData.location.state,
    city: formData.location.city,
    location: formData.location.city && formData.location.state 
      ? `${formData.location.city}/${formData.location.state}` 
      : undefined,
    email: formData.contact.email,
    whatsapp: formData.contact.whatsapp,
    phone: formData.contact.whatsapp,
    mainCategories: formData.business.categories || [],
    categories: formData.business.categories || [],
    category: formData.business.categories?.[0] || null,
    brandPartnerships: formData.business.brandPartnerships || [],
    totalFollowers: formData.metrics?.totalFollowers || 0,
    engagementRate: formData.metrics?.overallEngagementRate || 0,
    overallEngagementRate: formData.metrics?.overallEngagementRate || 0,
    mainNetwork: formData.mainPlatform,
    mainPlatform: formData.mainPlatform,
    isAvailable: true,
    status: 'active',
    socialNetworks: {},
    // Distribuição de gênero da audiência (padrão)
    audienceGender: { male: 0, female: 0, other: 0 }
  }

  // Converter plataformas para o formato do Firestore
  if (formData.platforms) {
    Object.keys(formData.platforms).forEach(platform => {
      const platformData = (formData.platforms as any)[platform]
      if (platformData && platformData.followers > 0) {
        influencerData.socialNetworks[platform] = {
          username: platformData.username || "",
          followers: Number(platformData.followers) || 0,
          engagementRate: Number(platformData.engagementRate) || 0,
          audienceGender: platformData.audienceGender || { male: 0, female: 0, other: 0 },
          audienceLocations: platformData.audienceLocations || [],
          audienceCities: platformData.audienceCities || [],
          audienceAgeRange: platformData.audienceAgeRange || {},
          brandHistory: platformData.brandHistory || [],
          screenshots: platformData.screenshots || [],
          pricing: platformData.pricing || {},
          metrics: platformData.metrics || {}
        }
      }
    })
  }

  // Dados financeiros separados
  const financialData: any = {
    responsibleName: formData.business.responsibleName,
    agencyName: formData.business.agencyName,
    email: formData.contact.email,
    whatsapp: formData.contact.whatsapp,
    prices: {},
    brandHistory: { instagram: [], tiktok: [], youtube: [] },
    additionalData: {
      promotesTraders: formData.business.promotesTraders,
      responsibleRecruiter: formData.business.responsibleCapturer,
      contentType: [],
      socialMediaScreenshots: [],
      notes: "",
      documents: []
    }
  }

  // Extrair preços das plataformas
  if (formData.platforms) {
    Object.keys(formData.platforms).forEach(platformKey => {
      const platformData = (formData.platforms as any)[platformKey]
      if (platformData?.pricing) {
        if (platformKey === 'instagram') {
          if (platformData.pricing.story) {
            financialData.prices.instagramStory = { 
              name: "Stories", 
              price: Number(platformData.pricing.story) 
            }
          }
          if (platformData.pricing.reel) {
            financialData.prices.instagramReel = { 
              name: "Reels", 
              price: Number(platformData.pricing.reel) 
            }
          }
        }
        if (platformKey === 'youtube') {
          if (platformData.pricing.insertion) {
            financialData.prices.youtubeInsertion = { 
              name: "Inserção", 
              price: Number(platformData.pricing.insertion) 
            }
          }
          if (platformData.pricing.dedicated) {
            financialData.prices.youtubeDedicated = { 
              name: "Dedicado", 
              price: Number(platformData.pricing.dedicated) 
            }
          }
          if (platformData.pricing.shorts) {
            financialData.prices.youtubeShorts = { 
              name: "Shorts", 
              price: Number(platformData.pricing.shorts) 
            }
          }
        }
        if (platformKey === 'tiktok' && platformData.pricing.video) {
          financialData.prices.tiktokVideo = { 
            name: "Vídeo", 
            price: Number(platformData.pricing.video) 
          }
        }

        // Extrair histórico de marcas
        if (platformData.brandHistory && Array.isArray(platformData.brandHistory)) {
          if (platformKey === 'instagram') {
            financialData.brandHistory.instagram = platformData.brandHistory as string[]
          } else if (platformKey === 'tiktok') {
            financialData.brandHistory.tiktok = platformData.brandHistory as string[]
          } else if (platformKey === 'youtube') {
            financialData.brandHistory.youtube = platformData.brandHistory as string[]
          }
        }
      }
    })
  }


  return { influencerData, financialData }
}

export function useInfluencerForm({ initialData, onSubmit }: UseInfluencerFormProps = {}) {
  const { toast } = useToast()
  const lastInitialDataRef = useRef<any>(null)
  const [activePlatforms, setActivePlatforms] = useState<SocialPlatform[]>([])
  
  // Formulário principal
  const form = useForm<InfluencerFormData>({
    resolver: zodResolver(InfluencerFormSchema),
    defaultValues: {
      personalInfo: { name: "", verified: false },
      location: { country: "Brasil" },
      contact: {},
      business: { promotesTraders: false, brandPartnerships: [] },
      brands: [] // Adicionar campo brands aos valores padrão
    }
  })

  // Inicializar e atualizar formulário
  useEffect(() => {
    if (initialData && JSON.stringify(initialData) !== JSON.stringify(lastInitialDataRef.current)) {
      
      lastInitialDataRef.current = initialData
      const formData = convertFromFirestoreFormat(initialData)
      
      // Logs removidos para limpar console
      
      form.reset(formData)
      
      // Identificar plataformas ativas
      if (formData.platforms) {
        const platforms = Object.keys(formData.platforms).filter(key => {
          const platformData = (formData.platforms as any)[key]
          return platformData && typeof platformData === 'object'
        }) as SocialPlatform[]
        setActivePlatforms(platforms)
      }
    }
  }, [initialData, form])

  // Calcular métricas automaticamente
  const calculateMetrics = useCallback(() => {
    const platforms = form.getValues('platforms') || {}
    let totalFollowers = 0
    let totalEngagement = 0
    let platformCount = 0

    Object.values(platforms).forEach((platform: any) => {
      if (platform?.followers) {
        totalFollowers += Number(platform.followers) || 0
        if (platform.engagementRate) {
          totalEngagement += Number(platform.engagementRate) || 0
          platformCount++
        }
      }
    })

    const overallEngagementRate = platformCount > 0 ? totalEngagement / platformCount : 0

    form.setValue('metrics', {
      totalFollowers,
      overallEngagementRate: Math.round(overallEngagementRate * 100) / 100
    }, { shouldValidate: false })
  }, [form])

  // Gerenciar plataformas
  const addPlatform = useCallback((platform: SocialPlatform) => {
    if (!activePlatforms.includes(platform)) {
      const newPlatforms = [...activePlatforms, platform]
      setActivePlatforms(newPlatforms)
      
      // Adicionar plataforma no formulário
      const currentPlatforms = form.getValues('platforms') || {}
      
      const newPlatformData = {
        username: "",
        followers: 0,
        engagementRate: undefined, // Campo opcional - não definir valor padrão
        pricing: {},
        metrics: {},
        audienceGender: { male: 0, female: 0, other: 0 },
        audienceLocations: [],
        audienceCities: [],
        audienceAgeRange: [],
        brandHistory: [],
        screenshots: []
      }
      
      const updatedPlatforms = {
        ...currentPlatforms,
        [platform as string]: newPlatformData
      } as any
      
      form.setValue('platforms', updatedPlatforms, { shouldValidate: false })
      
    }
  }, [activePlatforms, form])

  const removePlatform = useCallback((platform: SocialPlatform) => {
    const newPlatforms = activePlatforms.filter(p => p !== platform)
    setActivePlatforms(newPlatforms)
    
          // Remover plataforma do formulário
      const currentPlatforms = form.getValues('platforms') || {}
      const platformKey = platform as string
      const { [platformKey]: removed, ...remainingPlatforms } = currentPlatforms as any
    
    form.setValue('platforms', remainingPlatforms, { shouldValidate: false })
    
  }, [activePlatforms, form])

  const availablePlatforms = (Object.keys(PLATFORM_CONFIG) as SocialPlatform[]).filter(
    platform => !activePlatforms.includes(platform)
  )

  // Submit otimizado para GraphQL
  const handleSubmit = useCallback(async () => {
    try {
      
      const formData = form.getValues()
      
      // 🎯 DEBUG ESPECÍFICO PARA FAIXAS ETÁRIAS
      if (formData.platforms) {
        Object.keys(formData.platforms).forEach(platform => {
          const platformData = (formData.platforms as any)[platform]
          if (platformData?.audienceAgeRange) {
            console.log(`🎯 [DEBUG FAIXAS ETÁRIAS] É array?:`, Array.isArray(platformData.audienceAgeRange))
          } else {
          }
        })
      }
      

      // 📸 UPLOAD DO AVATAR SE HOUVER ARQUIVO SELECIONADO
      if (formData.personalInfo?.avatarFile) {
        
        try {
          const avatarFile = formData.personalInfo.avatarFile as File
          const influencerId = initialData?.id || `temp_${Date.now()}`
          
          // Fazer upload via API
          const formDataUpload = new FormData()
          formDataUpload.append('avatar', avatarFile)
          formDataUpload.append('influencerId', influencerId)
          
          const uploadResponse = await fetch('/api/influencers/avatar', {
            method: 'POST',
            body: formDataUpload
          })
          
          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json()
            throw new Error(errorData.error || 'Erro ao fazer upload do avatar')
          }
          
          const uploadResult = await uploadResponse.json()
          
          // Atualizar o formulário com a URL do avatar
          form.setValue('personalInfo.avatar', uploadResult.avatarUrl, { shouldValidate: false })
          
          // Limpar o arquivo temporário
          form.setValue('personalInfo.avatarFile', undefined as any, { shouldValidate: false })
          
          // Atualizar os dados do formulário
          formData.personalInfo.avatar = uploadResult.avatarUrl
          delete formData.personalInfo.avatarFile
          
        } catch (uploadError) {
          toast({
            title: "Erro no Upload",
            description: "Falha ao enviar a imagem do avatar. O influenciador será criado sem foto.",
            variant: "destructive",
          })
          // Continuar sem avatar
        }
      }
      
      // 📸 PROCESSAR SCREENSHOTS PENDENTES
      const pendingScreenshots = form.getValues('pendingScreenshots') || []
      
      if (pendingScreenshots && pendingScreenshots.length > 0) {
        
        try {
          const influencerId = initialData?.id || form.getValues('id') || `temp_${Date.now()}`
          
          console.log(`📸 [FORM-SUBMIT] Processando ${pendingScreenshots.length} screenshots em lote via GraphQL`)
          
          // 🔥 UPLOAD EM LOTE OTIMIZADO VIA GRAPHQL - UMA ÚNICA MUTATION
          // Converter arquivos para base64 para envio via GraphQL
          const screenshots = await Promise.all(
            pendingScreenshots.map(async (pendingFile: any) => {
              return new Promise<any>((resolve, reject) => {
                const reader = new FileReader()
                reader.onload = () => {
                  resolve({
                    platform: pendingFile.platform,
                    filename: pendingFile.file.name,
                    fileData: reader.result as string,
                    contentType: pendingFile.file.type,
                    size: pendingFile.file.size
                  })
                }
                reader.onerror = reject
                reader.readAsDataURL(pendingFile.file)
              })
            })
          )

          console.log(`📸 [FORM-SUBMIT] Fazendo upload em lote via GraphQL mutation (${screenshots.length} arquivos)`)
          
          // Fazer upload em lote via GraphQL mutation
          const uploadResponse = await fetch('/api/graphql', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: `
                mutation UploadScreenshotsBatch($input: UploadScreenshotsBatchInput!) {
                  uploadScreenshotsBatch(input: $input) {
                    success
                    totalUploaded
                    results {
                      id
                      influencerId
                      platform
                      url
                      filename
                      size
                      contentType
                      uploadedAt
                      uploadedBy
                    }
                    errors {
                      platform
                      filename
                      error
                    }
                  }
                }
              `,
              variables: {
                input: {
                  influencerId,
                  screenshots
                }
              }
            })
          })

          if (!uploadResponse.ok) {
            throw new Error(`Erro HTTP: ${uploadResponse.status}`)
          }

          const result = await uploadResponse.json()

          if (result.errors) {
            throw new Error(result.errors[0]?.message || 'Erro GraphQL desconhecido')
          }

          const uploadResult = result.data?.uploadScreenshotsBatch
          
          if (!uploadResult.success && uploadResult.totalUploaded === 0) {
            throw new Error('Todos os uploads falharam')
          }

          console.log(`✅ [FORM-SUBMIT] Upload em lote GraphQL concluído:`, {
            total: screenshots.length,
            sucessos: uploadResult.totalUploaded,
            falhas: uploadResult.errors?.length || 0
          })
          
          // Atualizar URLs dos screenshots nas plataformas
          if (uploadResult.results && uploadResult.results.length > 0) {
            const currentPlatforms = form.getValues('platforms') || {}
            
            uploadResult.results.forEach((upload: any) => {
              const platformKey = upload.platform
              const existingPlatform = (currentPlatforms as any)[platformKey]
              
              if (existingPlatform) {
                if (!existingPlatform.screenshots) {
                  existingPlatform.screenshots = []
                }
                existingPlatform.screenshots.push(upload.url)
                
                // Atualizar a plataforma específica
                form.setValue(`platforms.${platformKey}.screenshots` as any, existingPlatform.screenshots, { shouldValidate: false })
              }
            })
            
            // Toast com informações do resultado
            if (uploadResult.errors && uploadResult.errors.length > 0) {
              toast({
                title: "Upload parcialmente concluído",
                description: `${uploadResult.totalUploaded} de ${screenshots.length} screenshot(s) enviado(s). ${uploadResult.errors.length} falhou(aram).`,
                variant: "default"
              })
            } else {
              toast({
                title: "Screenshots enviados",
                description: `${uploadResult.totalUploaded} screenshot(s) enviado(s) com sucesso`
              })
            }
          }
          
          // Limpar arquivos pendentes
          form.setValue('pendingScreenshots', [], { shouldValidate: false })
          
        } catch (screenshotError) {
          toast({
            title: "Erro nos Screenshots",
            description: "Alguns screenshots podem não ter sido enviados. O influenciador será salvo sem eles.",
            variant: "destructive",
          })
        }
      } else {
      }
      
      // Chamar onSubmit customizado se fornecido
      if (onSubmit) {
        await onSubmit(formData)
      }
      
      toast({
        title: "Sucesso!",
        description: "Influenciador salvo com sucesso.",
      })
      
    } catch (error) {
      
      toast({
        title: "Erro",
        description: error instanceof Error ? error.message : "Erro ao salvar influenciador. Tente novamente.",
        variant: "destructive",
      })
    }
  }, [form, onSubmit, toast, initialData])

  const formHandleSubmit = form.handleSubmit(handleSubmit)

  const resetForm = useCallback(() => {
    form.reset()
    setActivePlatforms([])
  }, [form])

  return {
    form,
    handleSubmit: formHandleSubmit,
    resetForm,
    isSubmitting: form.formState.isSubmitting,
    errors: form.formState.errors,
    isValid: form.formState.isValid,
    calculateMetrics,
    activePlatforms,
    addPlatform,
    removePlatform,
    availablePlatforms
  }
} 

