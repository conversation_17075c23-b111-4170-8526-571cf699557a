import React from 'react';
import { DataTable } from '@/components/ui/data-table';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ZeroLCPAvatar, ZeroLCPInfluencerAvatar } from '@/components/ui/zero-lcp-avatar';
import { Badge } from '@/components/ui/badge';
import { CheckSquare, Square, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';

interface Influencer {
  id: string;
  nome: string;
  verified: boolean;
  pais: string;
  cidade: string;
  estado: string;
  idade: number;
  categoria: string;
  divulgaTrader: boolean;
  genero: 'Masculino' | 'Feminino' | 'Outro';
  whatsapp: string;
  redesSociais: {
    instagram?: {
      username: string;
      seguidores: number;
      engajamento: number;
    };
    youtube?: {
      username: string;
      seguidores: number;
      visualizacoes: number;
    };
    tiktok?: {
      username: string;
      seguidores: number;
      curtidas: number;
    };
  };
  servicos: {
    postFeed: number;
    stories: number;
    reels: number;
    videoYoutube: number;
    videoTiktok: number;
  };
  avatar?: string;
}

interface BrandInfluencer {
  id: string;
  brandId: string;
  brandName: string;
  influencerId: string;
  influencerName: string;
  influencerData: Influencer;
  status: 'enviado' | 'visualizado' | 'interessado' | 'rejeitado' | 'proposta_enviada';
  sentAt: Date;
  viewedAt?: Date;
  lastInteractionAt?: Date;
  notes?: string;
  createdBy: string;
  updatedAt: Date;
}

interface InfluencerTableProps {
  viewMode: 'list' | 'card';
  activeTab: 'all' | 'sent' | 'proposals';
  filteredInfluencers: Influencer[];
  brandInfluencers: BrandInfluencer[];
  selectedInfluencers: string[];
  selectedInfluencer: Influencer | null;
  showDetailsPanel: boolean;
  selectionMode: boolean;
  influencerColumns: ColumnDef<Influencer>[];
  proposalsLoading: boolean;
  filteredProposals: any[];
  createdProposals: any[];
  setSelectedInfluencers: (ids: string[]) => void;
  setSelectedInfluencer: (influencer: Influencer | null) => void;
  setShowDetailsPanel: (show: boolean) => void;
  handleRowSelection: (selectedRows: any) => void;
  getInfluencerStatus: (id: string) => BrandInfluencer | null;
}

export function InfluencerTable({
  viewMode,
  activeTab,
  filteredInfluencers,
  brandInfluencers,
  selectedInfluencers,
  selectedInfluencer,
  showDetailsPanel,
  selectionMode,
  influencerColumns,
  proposalsLoading,
  filteredProposals,
  createdProposals,
  setSelectedInfluencers,
  setSelectedInfluencer,
  setShowDetailsPanel,
  handleRowSelection,
  getInfluencerStatus
}: InfluencerTableProps) {
  if (activeTab === 'proposals') {
    return (
      <div className="space-y-4">
        {filteredProposals.length === 0 ? (
          <div className="text-center py-8">
            <h3 className="text-lg font-medium mb-2">Nenhuma proposta encontrada</h3>
            <p className="text-muted-foreground">
              {createdProposals.length === 0 
                ? "Você ainda não criou nenhuma proposta para influenciadores."
                : "Nenhuma proposta corresponde aos filtros aplicados."}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredProposals.map((proposal) => (
              <Card key={proposal.id} className="hover:shadow-md transition-shadow border rounded-2xl">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{proposal.nomeGrupo || proposal.grupo}</h4>
                        {proposal.influencers && proposal.influencers.length > 1 && (
                          <Badge variant="outline" className="text-xs">
                            <Users className="h-3 w-3 mr-1" />
                            {proposal.influencers.length} influenciadores
                          </Badge>
                        )}
                      </div>
                      
                      {/* Mostrar influenciadores do grupo */}
                      {proposal.influencers && proposal.influencers.length > 0 && (
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex -space-x-2">
                            {proposal.influencers.slice(0, 3).map((influencer: any) => {
                              // Se for um objeto com dados do influenciador, usar diretamente
                              if (typeof influencer === 'object' && influencer.name) {
                                return (
                                  <Avatar key={influencer.id} className="h-6 w-6 border-2 border-background">
                                    <AvatarImage src={influencer.avatar} />
                                    <AvatarFallback className="text-xs">{influencer.name.charAt(0)}</AvatarFallback>
                                  </Avatar>
                                );
                              }
                              // Se for apenas um ID, buscar nos influenciadores filtrados
                              const foundInfluencer = filteredInfluencers.find(inf => inf && inf.id === influencer);
                              return foundInfluencer ? (
                                <Avatar key={foundInfluencer.id} className="h-6 w-6 border-2 border-background">
                                  <AvatarImage src={foundInfluencer.avatar} />
                                  <AvatarFallback className="text-xs">{foundInfluencer.nome?.charAt(0) || '?'}</AvatarFallback>
                                </Avatar>
                              ) : null;
                            })}
                            {proposal.influencers.length > 3 && (
                              <div className="h-6 w-6 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium">
                                +{proposal.influencers.length - 3}
                              </div>
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {proposal.influencers.slice(0, 2).map((influencer: any) => {
                              // Se for um objeto com dados do influenciador, usar diretamente
                              if (typeof influencer === 'object' && influencer.name) {
                                return influencer.name;
                              }
                              // Se for apenas um ID, buscar nos influenciadores filtrados
                              const foundInfluencer = filteredInfluencers.find(inf => inf && inf.id === influencer);
                              return foundInfluencer?.nome;
                            }).filter(Boolean).join(', ')}
                            {proposal.influencers.length > 2 && ` e mais ${proposal.influencers.length - 2}`}
                          </span>
                        </div>
                      )}
                      
                      <p className="text-sm text-muted-foreground mt-1">{proposal.services?.length || 0} serviço(s) - R$ {proposal.totalAmount?.toLocaleString('pt-BR') || '0'}</p>
                      <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                        <span>Data: {proposal.dataEnvio}</span>
                        <span>Criada: {new Date(proposal.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={proposal.status === 'approved' ? 'default' : 
                                proposal.status === 'rejected' ? 'destructive' : 'secondary'}
                      >
                        {proposal.status === 'pending' || proposal.status === 'pendente' ? 'Pendente' :
                         proposal.status === 'approved' ? 'Aprovada' :
                         proposal.status === 'rejected' ? 'Rejeitada' : proposal.status}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    );
  }

  if (viewMode === 'list') {
    return (
      <DataTable
        columns={influencerColumns}
        data={activeTab === 'all' ? filteredInfluencers : brandInfluencers.map(bi => bi.influencerData)}
        onRowSelectionChange={handleRowSelection}
        enableFiltering={false}
        onRowClick={(row) => {
          if (selectionMode) {
            // No modo de seleção, clicar em qualquer lugar da linha seleciona/desseleciona
            if (row?.id) {
              if (selectedInfluencers.includes(row.id)) {
                setSelectedInfluencers(selectedInfluencers.filter(id => id !== row.id));
              } else {
                setSelectedInfluencers([...selectedInfluencers, row.id]);
              }
            }
          } else {
            // Se clicar no mesmo influenciador que já está selecionado, fecha o painel
            if (selectedInfluencer?.id === row?.id && showDetailsPanel) {
              setShowDetailsPanel(false);
              setSelectedInfluencer(null);
            } else {
              setSelectedInfluencer(row);
              setShowDetailsPanel(true);
            }
          }
        }}
      />
    );
  }

  // Card view
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {(activeTab === 'all' ? filteredInfluencers : brandInfluencers.map(bi => bi.influencerData)).map((influencer) => {
        const brandInfluencer = influencer?.id ? getInfluencerStatus(influencer.id) : null;
        const isSelected = influencer?.id ? selectedInfluencers.includes(influencer.id) : false;
        
        return (
          <Card 
            key={influencer?.id || Math.random()}
            className={cn(
              "transition-all duration-200 overflow-hidden relative backdrop-blur-md cursor-pointer",
              selectionMode 
                ? (isSelected ? 'bg-white/10 dark:bg-black/10 border-[#ff0074]' : 'bg-white/5 dark:bg-black/5 border border-gray-200 dark:border-white/5')
                : (isSelected ? 'group border border-purple-500/30' : 'bg-white/5 dark:bg-black/5 border border-gray-200 dark:border-white/5')
            )}
            style={{
              backdropFilter: 'blur(2px)',
              WebkitBackdropFilter: 'blur(2px)'
            }}
            onClick={() => {
              if (selectionMode) {
                if (influencer?.id) {
                  if (selectedInfluencers.includes(influencer.id)) {
                    setSelectedInfluencers(selectedInfluencers.filter(id => id !== influencer.id));
                  } else {
                    setSelectedInfluencers([...selectedInfluencers, influencer.id]);
                  }
                }
              } else {
                // Se clicar no mesmo influenciador que já está selecionado, fecha o painel
                if (selectedInfluencer?.id === influencer?.id && showDetailsPanel) {
                  setShowDetailsPanel(false);
                  setSelectedInfluencer(null);
                } else {
                  setSelectedInfluencer(influencer);
                  setShowDetailsPanel(true);
                }
              }
            }}
          >
            {/* Checkbox de seleção (visível apenas no modo de seleção) */}
            {selectionMode && (
              <div 
                className="absolute top-3 right-3 z-10"
                onClick={(e) => {
                  e.stopPropagation();
                  if (selectedInfluencers.includes(influencer.id)) {
                    setSelectedInfluencers(selectedInfluencers.filter(id => id !== influencer.id));
                  } else {
                    setSelectedInfluencers([...selectedInfluencers, influencer.id]);
                  }
                }}
              >
                {isSelected ? (
                  <CheckSquare className="h-5 w-5 text-[#ff0074]" />
                ) : (
                  <Square className="h-5 w-5 text-muted-foreground" />
                )}
              </div>
            )}

            {/* Conteúdo principal do card */}
            <div className="p-4 flex flex-col">
              {/* Header com avatar e informações */}
              <div className="flex flex-col items-center w-full">
                {/* Avatar e informações em coluna centralizada */}
                <div className="flex flex-col items-center gap-3 w-full">
                  {/* Avatar Otimizado */}
                  <div className="relative">
                    <ZeroLCPInfluencerAvatar
                      influencerName={influencer?.nome || 'Influencer'}
                      avatarUrl={influencer?.avatar}
                      size="xl"
                      className="border-2 border-foreground/5"
                    />
                    {influencer?.verified && (
                      <div className="absolute -bottom-1 -right-1 bg-blue-500 rounded-full p-1">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Nome e localização */}
                  <div className="text-center">
                    <h3 className="font-semibold text-lg leading-tight">{influencer?.nome || 'Nome não disponível'}</h3>
                    <p className="text-sm text-muted-foreground">
                      {influencer?.cidade && influencer?.estado ? `${influencer.cidade}, ${influencer.estado}` : 'Localização não disponível'}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {influencer?.categoria || 'Categoria não disponível'} • {influencer?.idade ? `${influencer.idade} anos` : 'Idade não disponível'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Estatísticas das redes sociais */}
              <div className="mt-4 space-y-2">
                {influencer?.redesSociais?.instagram && (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-sm"></div>
                      <span>Instagram</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{influencer.redesSociais.instagram.seguidores?.toLocaleString() || '0'}</div>
                      <div className="text-xs text-muted-foreground">{influencer.redesSociais.instagram.engajamento || 0}% eng.</div>
                    </div>
                  </div>
                )}
                
                {influencer?.redesSociais?.youtube && (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-red-500 rounded-sm"></div>
                      <span>YouTube</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{influencer.redesSociais.youtube.seguidores?.toLocaleString() || '0'}</div>
                      <div className="text-xs text-muted-foreground">{influencer.redesSociais.youtube.visualizacoes?.toLocaleString() || '0'} views</div>
                    </div>
                  </div>
                )}
                
                {influencer?.redesSociais?.tiktok && (
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-black dark:bg-white rounded-sm"></div>
                      <span>TikTok</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{influencer.redesSociais.tiktok.seguidores?.toLocaleString() || '0'}</div>
                      <div className="text-xs text-muted-foreground">{influencer.redesSociais.tiktok.curtidas?.toLocaleString() || '0'} likes</div>
                    </div>
                  </div>
                )}
              </div>

              {/* Tags e badges */}
              <div className="mt-4 flex flex-wrap gap-1">
                {influencer?.divulgaTrader && (
                  <Badge variant="secondary" className="text-xs">
                    Trader
                  </Badge>
                )}
                {brandInfluencer && (
                  <Badge variant="outline" className="text-xs">
                    {brandInfluencer.status === 'enviado' ? 'Enviado' :
                     brandInfluencer.status === 'visualizado' ? 'Visualizado' :
                     brandInfluencer.status === 'interessado' ? 'Interessado' :
                     brandInfluencer.status === 'rejeitado' ? 'Rejeitado' :
                     brandInfluencer.status === 'proposta_enviada' ? 'Proposta Enviada' :
                     brandInfluencer.status}
                  </Badge>
                )}
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
}


