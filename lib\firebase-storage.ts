import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getStorage } from 'firebase-admin/storage';

const serviceAccount = require('../deumatch-demo-firebase-adminsdk-fbsvc-f4c5beed4a.json');

// Inicialize o Firebase apenas se não estiver já inicializado
const firebaseApp = getApps().length === 0 
  ? initializeApp({
      credential: cert(serviceAccount),
      storageBucket: 'deumatch-demo.firebasestorage.app' // Bucket correto do Firebase
    }, 'storage-app')
  : getApps().find(app => app.name === 'storage-app') || getApps()[0];

// Exporte o Storage para uso em toda a aplicação
export const storage = getStorage(firebaseApp);
export const bucket = storage.bucket('deumatch-demo.firebasestorage.app');

// Função para upload de imagem
export async function uploadImage(file: File, path: string): Promise<string> {
  try {
    // Converter o arquivo para buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Caminho completo no storage
    const filePath = `${path}/${Date.now()}_${file.name}`;
    const fileRef = bucket.file(filePath);
    
    // Upload do arquivo
    await fileRef.save(buffer, {
      metadata: {
        contentType: file.type
      }
    });
    
    // Configurar para acesso público
    await fileRef.makePublic();
    
    // Retornar a URL da imagem
    return `https://storage.googleapis.com/${bucket.name}/${filePath}`;
  } catch (error) {
    console.error('Erro ao fazer upload da imagem:', error);
    throw error;
  }
}

// Função para excluir imagem
export async function deleteImage(imageUrl: string): Promise<boolean> {
  try {
    // Extrair o caminho do arquivo da URL
    const urlPath = new URL(imageUrl).pathname;
    const filePath = decodeURIComponent(urlPath.split('/o/')[1].split('?')[0]);
    
    // Excluir o arquivo
    await bucket.file(filePath).delete();
    return true;
  } catch (error) {
    console.error('Erro ao excluir imagem:', error);
    return false;
  }
}

// Função para upload específico de avatar de influenciador
export async function uploadInfluencerAvatar(file: File, influencerId: string): Promise<string> {
  try {
    console.log(`[INFLUENCER_AVATAR_UPLOAD] Iniciando upload: ${file.name} (${file.type}) para influencer ${influencerId}`);

    // Converter o arquivo para buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Caminho específico para avatars de influenciadores
    const fileName = `${influencerId}_${Date.now()}.${file.name.split('.').pop()}`;
    const filePath = `influencers/avatars/${fileName}`;
    const fileRef = bucket.file(filePath);
    
    // Upload do arquivo
    await fileRef.save(buffer, {
      metadata: {
        contentType: file.type,
        customMetadata: {
          influencerId: influencerId,
          uploadedAt: new Date().toISOString()
        }
      }
    });
    
    // Configurar para acesso público
    await fileRef.makePublic();
    
    // Retornar a URL da imagem
    return `https://storage.googleapis.com/${bucket.name}/${filePath}`;
  } catch (error) {
    console.error('Erro ao fazer upload do avatar do influenciador:', error);
    throw error;
  }
}

// Função para upload específico de avatar de usuário
export async function uploadUserAvatar(file: File, userId: string): Promise<string> {
  try {
    console.log(`[USER_AVATAR_UPLOAD] Iniciando upload: ${file.name} (${file.type}) para usuário ${userId}`);

    // Converter o arquivo para buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Caminho específico para avatars de usuários
    const fileName = `${userId}_${Date.now()}.${file.name.split('.').pop()}`;
    const filePath = `users/avatars/${fileName}`;
    const fileRef = bucket.file(filePath);
    
    // Upload do arquivo
    await fileRef.save(buffer, {
      metadata: {
        contentType: file.type,
        customMetadata: {
          userId: userId,
          uploadedAt: new Date().toISOString(),
          purpose: 'user-avatar'
        }
      }
    });
    
    // Configurar para acesso público
    await fileRef.makePublic();
    
    // Retornar a URL da imagem
    return `https://storage.googleapis.com/${bucket.name}/${filePath}`;
  } catch (error) {
    console.error('Erro ao fazer upload do avatar do usuário:', error);
    throw error;
  }
}

// Função para buscar URL do avatar do influenciador
export async function getInfluencerAvatarUrl(influencerId: string, avatarPath?: string): Promise<string> {
  try {
    // Se já é uma URL válida do Firebase, retornar diretamente
    if (avatarPath && (
      avatarPath.startsWith('https://storage.googleapis.com') ||
      avatarPath.startsWith('https://firebasestorage.googleapis.com')
    )) {
      return avatarPath;
    }

    // Definir o caminho padrão do avatar se não fornecido
    const defaultPath = avatarPath || `avatars/${influencerId}.jpg`;
    
    // Verificar se o arquivo existe
    const file = bucket.file(defaultPath);
    const [exists] = await file.exists();
    
    if (!exists) {
      // Retornar string vazia para usar fallback CSS
      return '';
    }
    
    // Gerar URL pública do arquivo
    const [url] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + 24 * 60 * 60 * 1000, // 24 horas
    });
    
    return url;
  } catch (error) {
    console.error('Erro ao buscar URL do avatar:', error);
    // Retornar string vazia para usar fallback CSS
    return '';
  }
}

// Função para upload de screenshot de rede social
export async function uploadScreenshot(file: File, influencerId: string, platform: string): Promise<string> {
  try {
    console.log(`[SCREENSHOT_UPLOAD] Iniciando upload: ${file.name} (${file.type}) para ${platform}`);

    // Converter o arquivo para buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Caminho específico para screenshots de influenciadores
    // Manter o nome original do arquivo sem renomear
    const originalFileName = file.name;

    // Usar apenas o nome original do arquivo
    const fileName = originalFileName;
    const filePath = `influencers/${influencerId}/screenshots/${platform}/${fileName}`;

    // Verificar se arquivo já existe para evitar sobrescrever
    const fileRef = bucket.file(filePath);
    const [exists] = await fileRef.exists();

    if (exists) {
      console.log(`⚠️ [SCREENSHOT_UPLOAD] Arquivo ${fileName} já existe, será sobrescrito`);
    }
    
    // Upload do arquivo
    await fileRef.save(buffer, {
      metadata: {
        contentType: file.type,
        customMetadata: {
          influencerId: influencerId,
          platform: platform,
          originalName: file.name,
          uploadedAt: new Date().toISOString()
        }
      }
    });
    
    // Configurar para acesso público
    await fileRef.makePublic();
    
    // Retornar a URL da imagem
    return `https://storage.googleapis.com/${bucket.name}/${filePath}`;
  } catch (error) {
    console.error('Erro ao fazer upload do screenshot:', error);
    throw error;
  }
}

// Função para deletar screenshot
export async function deleteScreenshot(screenshotUrl: string): Promise<boolean> {
  try {
    // Extrair o caminho do arquivo da URL
    const urlPath = new URL(screenshotUrl).pathname;
    const filePath = decodeURIComponent(urlPath.split('/o/')[1]?.split('?')[0] || urlPath.split(`${bucket.name}/`)[1]);
    
    // Excluir o arquivo
    await bucket.file(filePath).delete();
    return true;
  } catch (error) {
    console.error('Erro ao excluir screenshot:', error);
    return false;
  }
}

// Função para listar screenshots de um influenciador por plataforma
export async function getInfluencerScreenshots(influencerId: string, platform?: string): Promise<{ name: string; url: string; platform: string }[]> {
  try {
    const prefix = platform 
      ? `influencers/${influencerId}/screenshots/${platform}/`
      : `influencers/${influencerId}/screenshots/`;
    
    const [files] = await bucket.getFiles({ prefix });
    
    const screenshots = await Promise.all(
      files
        .filter(file => {
          const fileName = file.name.toLowerCase();
          return fileName.endsWith('.jpg') || 
                 fileName.endsWith('.jpeg') || 
                 fileName.endsWith('.png') || 
                 fileName.endsWith('.webp');
        })
        .map(async (file) => {
          try {
            // Gerar URL pública para cada arquivo
            const [url] = await file.getSignedUrl({
              action: 'read',
              expires: Date.now() + 24 * 60 * 60 * 1000, // 24 horas
            });
            
            // Extrair informações do arquivo
            const fileName = file.name.split('/').pop() || file.name;
            const platformFromPath = file.name.split('/screenshots/')[1]?.split('/')[0] || 'unknown';
            
            return {
              name: fileName,
              url,
              platform: platformFromPath
            };
          } catch (error) {
            console.error('Erro ao processar screenshot:', error);
            return null;
          }
        })
    );
    
    return screenshots.filter(screenshot => screenshot !== null) as { name: string; url: string; platform: string }[];
  } catch (error) {
    console.error('Erro ao buscar screenshots:', error);
    return [];
  }
}

// Função para listar todos os avatares disponíveis
export async function getAvailableAvatars(): Promise<{ name: string; url: string }[]> {
  try {
    // Listar todos os arquivos na pasta influencers/avatars
    const [files] = await bucket.getFiles({
      prefix: 'influencers/avatars/',
      delimiter: '/'
    });
    
    const avatars = await Promise.all(
      files
        .filter(file => {
          // Filtrar apenas arquivos de imagem
          const fileName = file.name.toLowerCase();
          return fileName.endsWith('.jpg') || 
                 fileName.endsWith('.jpeg') || 
                 fileName.endsWith('.png') || 
                 fileName.endsWith('.webp');
        })
        .map(async (file) => {
          try {
            // Gerar URL pública para cada arquivo
            const [url] = await file.getSignedUrl({
              action: 'read',
              expires: Date.now() + 24 * 60 * 60 * 1000, // 24 horas
            });
            
            // Extrair nome limpo do arquivo
            const fileName = file.name.split('/').pop() || file.name;
            const cleanName = fileName.replace(/^\d+_/, '').replace(/\.[^/.]+$/, '');
            
            return {
              name: cleanName,
              url: url,
              fullPath: file.name
            };
          } catch (error) {
            console.error(`Erro ao processar arquivo ${file.name}:`, error);
            return null;
          }
        })
    );
    
    // Filtrar resultados válidos
    return avatars.filter(avatar => avatar !== null) as { name: string; url: string }[];
  } catch (error) {
    console.error('Erro ao buscar avatares disponíveis:', error);
    return [];
  }
}

// ===== FUNÇÕES PARA LOGOS DE MARCAS =====

interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
}

interface BrandLogoMetadata {
  originalName: string;
  mimeType: string;
  size: number;
}

/**
 * Função para comprimir e otimizar imagens
 */
export async function compressAndOptimizeImage(
  buffer: Buffer, 
  options: ImageOptimizationOptions = {}
): Promise<Buffer> {
  try {
    // Se sharp não estiver disponível, retornar buffer original
    // Em produção, você deve instalar sharp: npm install sharp
    // Por enquanto, retornamos o buffer original
    console.log('[IMAGE_COMPRESSION] Sharp não disponível, retornando imagem original');
    console.log('[IMAGE_COMPRESSION] Opções:', options);
    
    // TODO: Implementar compressão real com Sharp quando disponível
    // const sharp = require('sharp');
    // let image = sharp(buffer);
    // 
    // if (options.maxWidth || options.maxHeight) {
    //   image = image.resize(options.maxWidth, options.maxHeight, {
    //     fit: 'inside',
    //     withoutEnlargement: true
    //   });
    // }
    // 
    // if (options.format) {
    //   image = image.toFormat(options.format, { quality: options.quality || 90 });
    // }
    // 
    // return await image.toBuffer();
    
    return buffer;
  } catch (error) {
    console.error('Erro na compressão da imagem:', error);
    return buffer; // Retornar original em caso de erro
  }
}

/**
 * Upload específico para logos de marcas
 */
export async function uploadBrandLogo(
  buffer: Buffer, 
  userId: string, 
  metadata: BrandLogoMetadata
): Promise<string> {
  try {
    console.log(`[BRAND_LOGO_UPLOAD] Iniciando upload: ${metadata.originalName} (${metadata.mimeType})`);

    // Gerar nome único para o arquivo
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const extension = metadata.mimeType.split('/')[1] || 'webp';
    const fileName = `logo_${timestamp}_${randomSuffix}.${extension}`;
    
    // Caminho organizado por usuário
    const filePath = `brands/logos/${userId}/${fileName}`;
    const fileRef = bucket.file(filePath);
    
    // Upload do arquivo com metadados
    await fileRef.save(buffer, {
      metadata: {
        contentType: metadata.mimeType,
        customMetadata: {
          userId: userId,
          originalName: metadata.originalName,
          uploadedAt: new Date().toISOString(),
          fileSize: metadata.size.toString(),
          purpose: 'brand-logo'
        }
      },
      resumable: false // Para arquivos pequenos
    });
    
    // Configurar para acesso público
    await fileRef.makePublic();
    
    // Retornar URL pública
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${filePath}`;
    
    console.log(`[BRAND_LOGO_UPLOAD] Logo enviado: ${publicUrl}`);
    
    return publicUrl;
  } catch (error) {
    console.error('Erro no upload do logo da marca:', error);
    throw new Error(`Falha no upload: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
  }
}

/**
 * Função para deletar logo de marca
 */
export async function deleteBrandLogo(logoUrl: string, userId: string): Promise<boolean> {
  try {
    // Extrair caminho do arquivo da URL
    const url = new URL(logoUrl);
    const pathParts = url.pathname.split('/');
    const bucketIndex = pathParts.findIndex(part => part === 'o') + 1;
    
    if (bucketIndex === 0) {
      console.error('URL inválida para deletar logo:', logoUrl);
      return false;
    }
    
    const filePath = decodeURIComponent(pathParts.slice(bucketIndex).join('/'));
    
    // Verificar se o arquivo pertence ao usuário (segurança)
    if (!filePath.includes(`brands/logos/${userId}/`)) {
      console.error('Tentativa de deletar logo de outro usuário:', { filePath, userId });
      return false;
    }
    
    // Deletar arquivo
    const fileRef = bucket.file(filePath);
    await fileRef.delete();
    
    console.log(`[BRAND_LOGO_DELETE] Logo deletado: ${filePath}`);
    return true;
  } catch (error) {
    console.error('Erro ao deletar logo da marca:', error);
    return false;
  }
}

/**
 * Função para listar logos de marcas de um usuário
 */
export async function getBrandLogos(userId: string): Promise<{ name: string; url: string; metadata: any }[]> {
  try {
    const [files] = await bucket.getFiles({
      prefix: `brands/logos/${userId}/`,
      delimiter: '/'
    });
    
    const logos = await Promise.all(
      files
        .filter(file => {
          const fileName = file.name.toLowerCase();
          return fileName.endsWith('.jpg') || 
                 fileName.endsWith('.jpeg') || 
                 fileName.endsWith('.png') || 
                 fileName.endsWith('.webp');
        })
        .map(async (file) => {
          try {
            const [metadata] = await file.getMetadata();
            const [url] = await file.getSignedUrl({
              action: 'read',
              expires: Date.now() + 24 * 60 * 60 * 1000, // 24 horas
            });
            
            const fileName = file.name.split('/').pop() || file.name;
            
            return {
              name: fileName,
              url: url,
              metadata: metadata.metadata || {}
            };
          } catch (error) {
            console.error(`Erro ao processar logo ${file.name}:`, error);
            return null;
          }
        })
    );
    
    return logos.filter(logo => logo !== null) as { name: string; url: string; metadata: any }[];
  } catch (error) {
    console.error('Erro ao buscar logos do usuário:', error);
    return [];
  }
}

/**
 * Upload específico para documentos de influenciadores
 */
export async function uploadDocument(file: File, influencerId: string): Promise<string> {
  try {
    console.log(`[DOCUMENT_UPLOAD] Iniciando upload: ${file.name} (${file.type}) para influencer ${influencerId}`);

    // Converter o arquivo para buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Gerar nome único para o arquivo, preservando a extensão original
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const extension = file.name.split('.').pop() || 'bin';
    const fileName = `${file.name.replace(/\.[^/.]+$/, "")}_${timestamp}_${randomSuffix}.${extension}`;
    
    // Caminho organizado por influenciador
    const filePath = `influencers/${influencerId}/documents/${fileName}`;
    const fileRef = bucket.file(filePath);
    
    // Upload do arquivo com metadados
    await fileRef.save(buffer, {
      metadata: {
        contentType: file.type,
        customMetadata: {
          influencerId: influencerId,
          originalName: file.name,
          uploadedAt: new Date().toISOString(),
          fileSize: file.size.toString(),
          purpose: 'influencer-document'
        }
      },
      resumable: false // Para arquivos pequenos
    });
    
    // Configurar para acesso público
    await fileRef.makePublic();
    
    // Retornar a URL pública
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${filePath}`;
    
    console.log(`[DOCUMENT_UPLOAD] Upload concluído: ${publicUrl}`);
    
    return publicUrl;
  } catch (error) {
    console.error('Erro ao fazer upload do documento:', error);
    throw error;
  }
}

/**
 * Função para deletar documento do Firebase Storage
 */
export async function deleteDocument(documentUrl: string): Promise<boolean> {
  try {
    // Extrair o caminho do arquivo da URL
    const urlPath = new URL(documentUrl).pathname;
    const filePath = decodeURIComponent(urlPath.split('/o/')[1]?.split('?')[0] || urlPath.split(`${bucket.name}/`)[1]);
    
    console.log(`[DOCUMENT_DELETE] Tentando excluir: ${filePath}`);
    
    // Excluir o arquivo
    await bucket.file(filePath).delete();
    
    console.log(`[DOCUMENT_DELETE] Arquivo excluído com sucesso: ${filePath}`);
    return true;
  } catch (error) {
    console.error('Erro ao excluir documento:', error);
    return false;
  }
}


