interface SocialIconProps {
  platform: string;
  size?: number;
  className?: string;
}

export function SocialIcon({ platform, size = 16, className = "text-black dark:text-white" }: SocialIconProps) {
  const normalizedPlatform = platform.toLowerCase();
  
  switch (normalizedPlatform) {
    case 'instagram':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} fill="currentColor" viewBox="0 0 16 16" className={className}>
          <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
        </svg>
      );
    
    case 'youtube':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} fill="currentColor" viewBox="0 0 16 16" className={className}>
          <path d="M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.01 2.01 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.01 2.01 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31 31 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.01 2.01 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A100 100 0 0 1 7.858 2zM6.4 5.209v4.818l4.157-2.408z"/>
        </svg>
      );
    
    case 'tiktok':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} fill="currentColor" viewBox="0 0 448 512" className={className}>
          <path d="M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"/>
        </svg>
      );
    
    case 'facebook':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} fill="currentColor" viewBox="0 0 16 16" className={className}>
          <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
        </svg>
      );
    
    case 'twitch':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 300 300" fill="currentColor" className={className}>
          <path fillRule="evenodd" clipRule="evenodd" d="M215.2 260.8h-58.7L117.4 300H78.3v-39.2H6.6V52.2L26.1 0h267.3v182.6l-78.2 78.2zm52.2-91.2V26.1H52.2v189.1h58.7v39.1l39.1-39.1h71.7l45.7-45.6z"/>
          <path d="M195.6 78.3v78.3h26.1V78.3h-26.1zm-71.7 78.2H150V78.3h-26.1v78.2z"/>
        </svg>
      );
    
    case 'kwai':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 184 217" fill="currentColor" className={className}>
          <path d="m48.84 196.94c-2.92-.13-10.26-1.5-17.7-3.57a65.77 65.77 0 0 1 -12.42-4.72 7.9 7.9 0 0 1 -4.18-6.2c.12 0-.94-7.54-.94-22.21a157.22 157.22 0 0 1 .83-19c.28-2 1.22-3.61 3.74-3.49a80.77 80.77 0 0 1 14 1.55 110.54 110.54 0 0 1 16.6 4.16 6.75 6.75 0 0 1 4.66 5.5 164.81 164.81 0 0 1 1.1 20.44c0 10-.69 19.85-1 23-.3 3.01-1.77 4.68-4.69 4.54zm43.49-34.26a6.44 6.44 0 0 0 -2.06-4.7l-24.89-24.41a19.41 19.41 0 0 0 -10.39-5.73s-9.39-2.32-23.37-4.66-17.63-2.08-17.63-2.08c-12.39 0-12.84 13.22-13 14.55-.31 3.19-1 11.65-1 23.42a259.56 259.56 0 0 0 1.3 29.4c1.08 9 8 13.19 11.15 14.78a183 183 0 0 0 18.15 6.74c5.88 1.82 15.31 4.48 19.73 5.18 1.45.23 10 1.7 14-3.65l26.45-34.3a6.38 6.38 0 0 0 1.54-4.67zm57.8-50a71.19 71.19 0 0 0 -11.29-.25l-59.48 5.57a10.51 10.51 0 0 0 -6.19 3.6 11.44 11.44 0 0 0 -2.1 4.25c.79.78 1.11 1.1 1.54 1.5l23.49 24c2.63 2.58 4.15 5.09 4.15 8.85v13.5c0 2.4-.21 4.42-1.54 6.23l-12.64 17.06 12.67 4.92c4.81 1.59 9.28 1.54 14.3.78l54.56-12.53s9.84-2.62 9.84-12v-33.24c0-.64-.37-1.19-1-1.06l-34.84 6.9a2.63 2.63 0 0 0 -2.11 2.63v16.07a1.75 1.75 0 0 0 2.11 1.51l10.92-2.55c.57-.16 1 .26 1 1v5.2a2.36 2.36 0 0 1 -2.05 2.14l-12.4 2.62s-8.29 2.12-8.29-6.76v-20.55c0-9 6.5-9.88 6.5-9.88l38.36-7.4a2.19 2.19 0 0 0 1.8-2v-8.37c0-9.24-9-9.81-9-9.81zm-71.29-87.22c14.27.61 25.08 14.16 24.34 30.64s-13.19 29.55-26.93 29c-14.26-.61-24.6-14.29-23.86-30.77s12.68-29.44 26.45-28.87zm57.47 38.55c.58-11.24 8.6-20.12 18.06-19.76 9 .4 16.18 9.95 15.7 20.82-.58 11.27-8.72 19.81-18.16 19.51-9-.47-16.07-9.66-15.6-20.57zm47.83 1.47c0-22.18-16.52-40-36.83-40-11.85 0-22.61 6.28-29.57 15.59-5.35-23.68-25.1-41.13-48.76-41.06-28.23.08-50.93 24.48-50.83 54.57.09 30.4 22.91 55.09 50.83 55 1.61 0 78.54-5.19 78.32-5.19 22.17-1.92 36.94-17.23 36.83-38.9z"/>
        </svg>
      );
    
    case 'whatsapp':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} shapeRendering="geometricPrecision" textRendering="geometricPrecision" imageRendering="optimizeQuality" viewBox="0 0 510 512.459" className={className}>
          <path fill="currentColor" d="M435.689 74.468C387.754 26.471 324 .025 256.071 0 116.098 0 2.18 113.906 2.131 253.916c-.024 44.758 11.677 88.445 33.898 126.946L0 512.459l134.617-35.311c37.087 20.238 78.85 30.891 121.345 30.903h.109c139.949 0 253.88-113.917 253.88-253.928.024-67.855-26.361-131.645-74.31-179.643v-.012zm-179.618 390.7h-.085c-37.868-.011-75.016-10.192-107.428-29.417l-7.707-4.577-79.886 20.953 21.32-77.889-5.017-7.987c-21.125-33.605-32.29-72.447-32.266-112.322.049-116.366 94.729-211.046 211.155-211.046 56.373.025 109.364 22.003 149.214 61.903 39.853 39.888 61.781 92.927 61.757 149.313-.05 116.377-94.728 211.058-211.057 211.058v.011zm115.768-158.067c-6.344-3.178-37.537-18.52-43.358-20.639-5.82-2.119-10.044-3.177-14.27 3.178-4.225 6.357-16.388 20.651-20.09 24.875-3.702 4.238-7.403 4.762-13.747 1.583-6.343-3.178-26.787-9.874-51.029-31.487-18.86-16.827-31.597-37.598-35.297-43.955-3.702-6.355-.39-9.789 2.775-12.943 2.849-2.848 6.344-7.414 9.522-11.116s4.225-6.355 6.343-10.581c2.12-4.238 1.06-7.937-.522-11.117-1.584-3.177-14.271-34.409-19.568-47.108-5.151-12.37-10.385-10.69-14.269-10.897-3.703-.183-7.927-.219-12.164-.219s-11.105 1.582-16.925 7.939c-5.82 6.354-22.209 21.709-22.209 52.927 0 31.22 22.733 61.405 25.911 65.642 3.177 4.237 44.745 68.318 108.389 95.812 15.135 6.538 26.957 10.446 36.175 13.368 15.196 4.834 29.027 4.153 39.96 2.52 12.19-1.825 37.54-15.353 42.824-30.172 5.283-14.818 5.283-27.529 3.701-30.172-1.582-2.641-5.819-4.237-12.163-7.414l.011-.024z"/>
        </svg>
      );

    case 'pacotes':
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} fill="currentColor" viewBox="0 0 24 24" className={className}>
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
        </svg>
      );

    default:
      return (
        <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} fill="currentColor" viewBox="0 0 16 16" className={className}>
          <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0zM4.5 7.5a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1h-7z"/>
        </svg>
      );
  }
}

// Helper function para obter o nome da plataforma formatado
export function getPlatformDisplayName(platformName: string): string {
  switch (platformName.toLowerCase()) {
    case 'instagram':
      return 'Instagram';
    case 'youtube':
      return 'YouTube';
    case 'tiktok':
      return 'TikTok';
    case 'facebook':
      return 'Facebook';
    case 'twitch':
      return 'Twitch';
    case 'kwai':
      return 'Kwai';
    case 'whatsapp':
      return 'WhatsApp';
    case 'pacotes':
      return 'Pacotes de Serviços';
    default:
      return platformName.charAt(0).toUpperCase() + platformName.slice(1);
  }
} 

