Guide to fast websites with Next.js: Tips for maximizing server speeds and minimizing client burden

<PERSON><PERSON> Co-founder
8 min read

Nov 29, 2023
Tinloof Co-founder <PERSON><PERSON> gives insights into how his team measures website speed with best practices to make faster websites.

Tinloof is an agency obsessed with delivering fast websites such as jewelry brand <PERSON> Fisher, which went from a Shopify theme to a modern Next.js website that instantly loads with 80% less JavaScript.


00:07

00:30
When evaluating the speed of a website, they look at key metrics in a typical user journey:

Server response time: How long it takes for the user to get any feedback once landed on the page.

Page render time: How long it takes for a page to become fully visible and interactive.

User interaction time: How long it takes the user to make key interactions on the page such as navigating between pages or adding an item to the cart.

This article covers tips to measure and make each part of the user journey as fast as it gets.

The basics of site speed: Measuring data correctly
The key to making your site fast and keeping it fast—for any user on any device—is data.
Speed is more than just a way to gauge user experience, it's crucial for getting the top spot on any search platform and winning organic page views.

To ensure they're measuring the right data correctly, tools like Google PageSpeed Insights and Vercel Speed Insights are able to provide objective metrics. These tools can be used to diagnose page performance issues, providing insights into aspects like loading times, interactivity, and visual stability.

It's also equally important to test the user journey under various network conditions.

Combining objective tools with a hands-on approach provides a comprehensive view of a website experience, ensuring it’s optimised for all users.

Vercel Speed Insights

Learn how to get a detailed view of your website's performance metrics to help facilitate informed decisions on optimization.

Get Started

How to speed up server response: Make use of Next.js’ rendering toolbox
Once key performance metrics are evaluated, the team is able to pinpoint where and how to make improvements in things like server response.

When possible: Pre-render the entire page
Pre-rendering the page at build-time ensures it is served from a CDN instead of your origin server, resulting in the fastest server response possible. This is done automatically by Next.js if you don’t use the edge runtime and the page doesn’t rely on cookies, headers, or search parameters.

Will Thomson
Will Thomson
@willthomson__
·
Follow
Caching turns god-mode on for your website.

Look at the speed of @Tinloof's website.

You type, hit enter, site appears.

No loading spinners. No waiting.

Optimisation is why I choose code over no-code.

P.S They have epic developer resources on their site.


11:15 AM · Sep 19, 2023
25
Reply

Copy link
Else: Partial Prerender 
Pre-rendering an entire page might not be always possible.  

With Partial Prerendering (PPR) in Next.js, it's possible to pre-render a shell of the page that is served from a CDN while streaming the dynamic bits at the same time.

Partial Prerendering is currently an experimental feature that allows you to render a route with a static loading shell, while keeping some parts dynamic. In other words, you can isolate the dynamic parts of a page instead of a whole route.

Final resort: Render a loading shell while waiting for the final response
When a page is rendered at request time, it’s better to immediately show the user a UI hint that indicates a page is loading, rather than unresponsive links.

It’s best to make the loading UI resemble as much as possible the final one.

In Next.js, there are two places where you can render the loading UI:

In a loading.tsx file inside the page route folder.

Alex Sidorenko
Alex Sidorenko
@asidorenko_
·
Follow
"I have one server component that fetches a lot of data and makes the entire page slow to load in Next.js 13"

Wrap it with Suspense


12:44 PM · Aug 29, 2023
946
Reply

Copy link
Inside the fallback prop of the Suspense boundary that wraps async component making requests inside the page.

Cache fetch requests for fast server responses when using loading spinners
Loading shells are not an excuse for slow server responses. Most server responses can be cached instead of making the user wait for them on every page visit.
Although this is the default behaviour of fetch requests in Next.js, you can still control the freshness of this data:

By revalidating the server response every x number seconds.

app/page.tsx

export default function Home() {
  // The CMS data is guaranteed to be fresh every 2 minutes
  const cmsData = await fetch(`https://...`, { next: { revalidate: 120 } });
  
  return <h1>{cmsData.title}</h1>
}
Or by revalidating the server response when a certain event happens. Here’s an example where a CMS response is revalidated whenever a new CMS page gets published.

app/page.tsx

export default function Home() {
 // The CMS data is cached until the tag is revalidated
  const cmsData = await fetch(`https://...`, { next: { tags: ['landing-page']);
  
  return <h1>{cmsData.title}</h1>
}
app/api/revalidate/route.ts

import { revalidateTag } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest): Promise<NextResponse> {
  const secret = req.nextUrl.searchParams.get('secret');
  const tag = req.nextUrl.searchParams.get('landing-page');

  if(!tag || !isValid(secret)) {
		return NextResponse.json({ status: 400});
  }

  return revalidate(tag);
} 
The Next.js guide on caching and revalidation and the App Router explainer video are perfect to help understand these concepts.

How to speed up the page render: Minimize client burden
Short answer: Make the browser do the least amount of work to render the page.

Once the browser receives a response from the server, it still has to paint the entire page and make it ready for user interactions (e.g. button clicks).

While parsing the HTML and rendering, the browser is also downloading resources such as CSS, JavaScript, font, or image files.

The following tips help make page render fast by making the browser do as little work as possible.

Reduce the JavaScript bundle size and minimize the impact of hydration
The JavaScript shipped with React websites usually consists of React, Next.js, the application code including the JSX of every single React component, and third-party dependencies.

Once the page HTML is rendered and the JavaScript is downloaded, React goes through a process called “hydration” where it attaches event listeners and state to the components of the page.

Just by using React Server Components you already get a speed bump because:

Their JavaScript (including application code, JSX, and third-party dependencies) is not shipped to the browser.

React skips their hydration.

Guillermo Rauch
Guillermo Rauch
@rauchg
·
Follow
We moved our Web Analytics dashboard pages to @nextjs App Router and shaved off 800ms of LCP.

What a time to be alive.

2:55 PM · Aug 30, 2023
596
Reply

Copy link
When a component requires interactivity (e.g. state, event listeners), a use client directive can be used to convert it to a client component which in addition of being rendered in the server, also has its JavaScript shipped to the browser and is hydrated by React.

Reduce the impact of client components on page speed
Only use client components when necessary
URLs can be used to store a component state without having to make it a client component that relies on React’s state.

It requires less code to manage the state, turns state buttons to links that work even without JavaScript, and makes it possible to persist the state on page refresh or when sharing the URL.

Lee Robinson
Lee Robinson
@leerob
·
Follow
Replying to @leerob
From `useState` to URL state.

Rather than using client-side React state, we can instead lift state up to the URL for color, size, and even the selected image.

With the added bonus you can now "deep link" to specific variations. Yay, using the web platform!


4:52 PM · Aug 7, 2023
98
Reply

Copy link
Place client components in the leaves of the page tree
To minimize the JavaScript footprint of imported child components, it’s a good practice to place client components the furthest possible at the bottom of the components tree.

Alex Sidorenko
Alex Sidorenko
@asidorenko_
·
Follow
Move client components to the leaves of the component tree where possible.


11:29 AM · Aug 21, 2023
1.1K
Reply

Copy link
Be mindful of third-party dependencies’ bundle sizes
Any client component dependency is more JavaScript for the browser to download, parse, and execute.

Tools such as pkg-size can be used to determine the size impact of NPM packages based on what’s imported from them and help decide between alternatives.


Lazy-load client components when possible
Even when a client component is necessarily heavy, it’s still possible to only download its JavaScript once it’s rendered.

For example, the stockists page on Jennifer Fisher uses mapbox-gl, an extremely heavy package, to display interactive maps.

Since mapbox-gl is only used to display maps, its wrapper client component is lazy-loaded so the package bundle is only downloaded when the component is rendered.

You can lazy-load a client component either via next/dynamic or a combination of React.lazy and Suspense , more details can be found on Next.js guide on the topic.

Efficiently load third-party scripts
Some third-party dependencies like Google Tag Manager are injected via script tags instead of imports in client components.

@next/third-parties can be used to reduce their impact on page render speed and if dependency is not supported, next/script is also a great option.


How to load fonts more efficiently
Some web fonts are unnecessarily heavy because they include characters not even needed by the website.

In the case of Jennifer Fisher, Tinloof was able to trim out more than 50% of font files using tools such as transfonter.

next/font makes it possible to load local and Google Fonts while providing the following optimizations:

Only load fonts on pages where they are used.

Preload fonts to make them available early on when rendering.

Use display strategies such as swap to avoid blocking text rendering by using a fallback font.

How to load images more efficiently
Short answer: use next/image when you can.

The next/image component provides so many optimizations for local or remote images.

A detailed guide is available on Next.js docs so I’ll only highlight some of them:

Images are automatically served in modern efficient formats such as AVIF or WebP that preserve quality and dramatically reduce the download size.

Images are only loaded when visible in the viewport and a lazy boolean prop is available

to do the opposite for critical images.

A preload prop is available to make the browser load critical images ASAP.

Images are automatically served in different sizes based on the viewport and props such as sizes or loader are available to customise the behaviour.

Local images can automatically show a placeholder while loading and you can provide a

blurDataURL to achieve the same with remote images.

The next/image component is just a very handy utility and is not required to achieve the benefits above:

Images can still be served in modern formats by using CDNs that can convert them on the fly.

Lazy-loading images is a native browser attribute that can be used by default.

Images can be preloaded using a preload link

<link rel="preload" as="image" href="..." /> in the document’s head or using ReactDOM.preload.

When loading images from a different domain, it’s a good practice to use

preconnect links to inform the browser to establish a connection with the image provider domain early-on.


How to load videos more efficiently
Solutions such as Mux, Cloudinary, or CDNs such as Fastly can be used to help optimise video delivery by serving videos as close as possible to users.

A poster image is a must-have for any video and you can either manually set it or easily extract the first frame of the video to be the poster image when using any video CDN.

The best part is that you can use the same image optimizations tips discussed earlier to render the poster image efficiently.

Here’s an example Mux video component that utilises these optimizations and it’s only rendered on the server:


import { preload } from "react-dom";
import { unstable_getImgProps as getImgProps } from "next/image";

type Props = {
  playbackId: string;
  loading: "lazy" | "eager";
  resolution: "SD" | "HD";
};

export default function MuxVideo({ playBackId, loading, loading }: Props) {
  const mp4Url = `https://stream.mux.com/${playbackId}/${
    resolution === "SD" ? "medium" : "high"
  }.mp4`;

  const webmUrl = `https://stream.mux.com/${playbackId}/${
    resolution === "SD" ? "medium" : "high"
  }.webm`;

  // Use `getImgProps` to convert the video poster image to WebP
  const {
    props: { src: poster },
  } = getImgProps({
    src: `https://image.mux.com/${playbackId}/thumbnail.webp?fit_mode=smartcrop&time=0`,
    alt: "",
    fill: true,
  });

  // Preload the poster when applicable
  if (loading === "eager") {
    preload(poster, {
      as: "image",
      fetchPriority: "high",
    });
  }

  return (
    <video
      autoPlay
      playsInline
      loop
      controls={false}
      muted
      preload="none"
    >
      <source src={mp4Url} type="video/mp4" />
      <source src={webmUrl} type="video/webm" />
    </video>
  );
}
For videos that are not required to load immediately, you lazy-load them without causing any layout shift:


'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import useInView from '~/hooks/useInView';
import Video, { VideoProps } from './Video';

export default function LazyLoadedVideo(props: VideoProps) {
  const { ref, inView } = useInView({ triggerOnce: true });

  return (
    <>
      {!inView ? (
        <Image
          ref={ref as React.RefObject<HTMLImageElement>}
          alt={'Video poster'}
          src={props.poster ?? ''}
          className={props.className}
          style={props.style}
          loading={'lazy'}
          layout="fill"
        />
      ) : (
        <Video {...props} />
      )}
    </>
  );
}
How to reduce the HTML document size
The HTML document is a critical resource the browser has to download and parse.

Use virtualization
Components such as carousels/sliders, tables, and lists are also usual culprits.

You can use libraries such TanStack Virtual to only render items when they are visible in the viewport while avoiding any layout shifts.

How to speed up user interactions
Short answer: Provide feedback to the user as early as possible.

Some user interactions such as URL state navigations when filtering or adding an item to the cart rely on server responses, which are not always immediate, causing slow interactions or leaving the user puzzled on whether something went wrong.

Optimistic UI techniques can be used to make such interactions snappy and provide immediate feedback to users.

The idea is to use JavaScript to show the predicted result to the user without waiting the server to return a response.

It can be achieved either through normal React state management or using React’s useOptimistic hook.

Alex Sidorenko
Alex Sidorenko
@asidorenko_
·
Follow
Optimistic updates in Next.js 13


11:13 AM · Sep 23, 2023
1.0K
Reply

Copy link
The importance of a performant website
Fast websites are more pleasant to use, more engaging to users, and it’s no surprise they directly impact success metrics such as conversion rate and search engine indexation.

Although the tips above are focused on Next.js, the concepts behind them can be used to make any website faster.